#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:113), pid=10976, tid=12756
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\1eb1b3ffae57f2c6129ade188dcbd886\redhat.java\ss_ws --pipe=\\.\pipe\lsp-cce31334cece12711e1c5847512e99c1-sock

Host: 11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz, 8 cores, 7G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5794)
Time: Sun Jun  1 15:10:00 2025 South Africa Standard Time elapsed time: 0.966063 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000019868475310):  JavaThread "main"             [_thread_in_vm, id=12756, stack(0x0000002951200000,0x0000002951300000) (1024K)]

Stack: [0x0000002951200000,0x0000002951300000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0x8a41ee]
V  [jvm.dll+0x670575]
V  [jvm.dll+0x6705da]
V  [jvm.dll+0x672dc2]
V  [jvm.dll+0x672c92]
V  [jvm.dll+0x670f4e]
V  [jvm.dll+0x26a8d0]
V  [jvm.dll+0x216127]
V  [jvm.dll+0x20bbae]
V  [jvm.dll+0x5ae58c]
V  [jvm.dll+0x21d26a]
V  [jvm.dll+0x820d6c]
V  [jvm.dll+0x821d94]
V  [jvm.dll+0x822580]
V  [jvm.dll+0x2169dc]
V  [jvm.dll+0x20bbc7]
V  [jvm.dll+0x5ae58c]
V  [jvm.dll+0x21d26a]
V  [jvm.dll+0x820d6c]
V  [jvm.dll+0x821d94]
V  [jvm.dll+0x822362]
V  [jvm.dll+0x821fe8]
V  [jvm.dll+0x26cf1b]
V  [jvm.dll+0x3d493e]
C  0x0000019872bb925f

The last pc belongs to new (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.util.concurrent.SynchronousQueue.<init>(Z)V+10 java.base@21.0.7
j  java.util.concurrent.SynchronousQueue.<init>()V+2 java.base@21.0.7
j  org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.<init>(Lorg/eclipse/osgi/internal/framework/EquinoxContainer;Lorg/eclipse/osgi/storage/Storage;Ljava/util/Map;)V+186
j  org.eclipse.osgi.storage.Storage.<init>(Lorg/eclipse/osgi/internal/framework/EquinoxContainer;[Ljava/lang/String;)V+669
j  org.eclipse.osgi.storage.Storage.createStorage(Lorg/eclipse/osgi/internal/framework/EquinoxContainer;)Lorg/eclipse/osgi/storage/Storage;+11
j  org.eclipse.osgi.internal.framework.EquinoxContainer.<init>(Ljava/util/Map;Lorg/osgi/framework/connect/ModuleConnector;)V+146
j  org.eclipse.osgi.launch.Equinox.<init>(Ljava/util/Map;Lorg/osgi/framework/connect/ModuleConnector;)V+10
j  org.eclipse.osgi.launch.Equinox.<init>(Ljava/util/Map;)V+3
j  org.eclipse.core.runtime.adaptor.EclipseStarter.startup([Ljava/lang/String;Ljava/lang/Runnable;)Lorg/osgi/framework/BundleContext;+28
j  org.eclipse.core.runtime.adaptor.EclipseStarter.run([Ljava/lang/String;Ljava/lang/Runnable;)Ljava/lang/Object;+21
j  java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+11 java.base@21.0.7
j  java.lang.invoke.LambdaForm$MH+0x000001980108a800.invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+54 java.base@21.0.7
j  java.lang.invoke.LambdaForm$MH+0x0000019801002c00.invokeExact_MT(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+22 java.base@21.0.7
j  jdk.internal.reflect.DirectMethodHandleAccessor.invokeImpl(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+72 java.base@21.0.7
j  jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+23 java.base@21.0.7
j  java.lang.reflect.Method.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+102 java.base@21.0.7
j  org.eclipse.equinox.launcher.Main.invokeFramework([Ljava/lang/String;[Ljava/net/URL;)V+155
j  org.eclipse.equinox.launcher.Main.basicRun([Ljava/lang/String;)V+191
j  org.eclipse.equinox.launcher.Main.run([Ljava/lang/String;)I+4
j  org.eclipse.equinox.launcher.Main.main([Ljava/lang/String;)V+10
v  ~StubRoutines::call_stub 0x0000019872ba10e7
new  187 new  [0x0000019872bb90e0, 0x0000019872bb92e8]  520 bytes
[MachCode]
  0x0000019872bb90e0: 4883 ec08 | c5fa 1104 | 24eb 1f48 | 83ec 10c5 | fb11 0424 | eb14 4883 | ec10 4889 | 0424 48c7 
  0x0000019872bb9100: 4424 0800 | 0000 00eb | 0150 410f | b755 010f | cac1 ea10 | 488b 4de8 | 488b 4908 | 488b 4908 
  0x0000019872bb9120: 488b 4108 | 807c 1004 | 070f 85d3 | 0000 0066 | 8b54 d148 | 488b 4928 | 488b 4cd1 | 0851 80b9 
  0x0000019872bb9140: 2101 0000 | 040f 85b6 | 0000 008b | 5108 f6c2 | 010f 85aa | 0000 0049 | 8b87 b801 | 0000 488d 
  0x0000019872bb9160: 1c10 493b | 9fc8 0100 | 000f 8792 | 0000 0049 | 899f b801 | 0000 4883 | ea10 0f84 | 0f00 0000 
  0x0000019872bb9180: 33c9 c1ea | 0348 894c | d008 48ff | ca75 f648 | c700 0100 | 0000 5933 | f689 700c | 49ba 0000 
  0x0000019872bb91a0: 0000 9801 | 0000 492b | ca89 4808 | 49ba 3e5b | 39d5 fd7f | 0000 4180 | 3a00 0f84 | 3c00 0000 
  0x0000019872bb91c0: 5048 8bc8 | 4883 ec20 | 40f6 c40f | 0f84 1900 | 0000 4883 | ec08 48b8 | 904b e3d4 | fd7f 0000 
  0x0000019872bb91e0: ffd0 4883 | c408 e90c | 0000 0048 | b890 4be3 | d4fd 7f00 | 00ff d048 | 83c4 2058 | e9cb 0000 
  0x0000019872bb9200: 0059 488b | 55e8 488b | 5208 488b | 5208 450f | b745 0141 | 0fc8 41c1 | e810 e805 | 0000 00e9 
  0x0000019872bb9220: a800 0000 | 488d 4424 | 084c 896d | c049 8bcf | c5f8 7749 | 89af a803 | 0000 4989 | 8798 0300 
  0x0000019872bb9240: 0048 83ec | 2040 f6c4 | 0f0f 8419 | 0000 0048 | 83ec 0848 | b8f0 48ad | d4fd 7f00 | 00ff d048 
  0x0000019872bb9260: 83c4 08e9 | 0c00 0000 | 48b8 f048 | add4 fd7f | 0000 ffd0 | 4883 c420 | 49c7 8798 | 0300 0000 
  0x0000019872bb9280: 0000 0049 | c787 a803 | 0000 0000 | 0000 49c7 | 87a0 0300 | 0000 0000 | 00c5 f877 | 4983 7f08 
  0x0000019872bb92a0: 000f 8405 | 0000 00e9 | 547c feff | 498b 87f0 | 0300 0049 | c787 f003 | 0000 0000 | 0000 4c8b 
  0x0000019872bb92c0: 6dc0 4c8b | 75c8 4e8d | 74f5 00c3 | 410f b65d | 0349 83c5 | 0349 bab0 | 203c d5fd | 7f00 0041 
  0x0000019872bb92e0: ff24 da0f | 1f44 0000 
[/MachCode]

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001987e97c3e0, length=13, elements={
0x0000019868475310, 0x000001987c823340, 0x000001987c824c40, 0x000001987c8269c0,
0x000001987c8278d0, 0x000001987c82afe0, 0x000001987c82ca40, 0x000001987c82d870,
0x000001987c86e580, 0x000001987e2c0790, 0x000001987e4a59a0, 0x000001987e4ff530,
0x000001987e4f9c30
}

Java Threads: ( => current thread )
=>0x0000019868475310 JavaThread "main"                              [_thread_in_vm, id=12756, stack(0x0000002951200000,0x0000002951300000) (1024K)]
  0x000001987c823340 JavaThread "Reference Handler"          daemon [_thread_blocked, id=876, stack(0x0000002951600000,0x0000002951700000) (1024K)]
  0x000001987c824c40 JavaThread "Finalizer"                  daemon [_thread_blocked, id=7564, stack(0x0000002951700000,0x0000002951800000) (1024K)]
  0x000001987c8269c0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=10396, stack(0x0000002951800000,0x0000002951900000) (1024K)]
  0x000001987c8278d0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=9196, stack(0x0000002951900000,0x0000002951a00000) (1024K)]
  0x000001987c82afe0 JavaThread "Service Thread"             daemon [_thread_blocked, id=3304, stack(0x0000002951a00000,0x0000002951b00000) (1024K)]
  0x000001987c82ca40 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=6572, stack(0x0000002951b00000,0x0000002951c00000) (1024K)]
  0x000001987c82d870 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=460, stack(0x0000002951c00000,0x0000002951d00000) (1024K)]
  0x000001987c86e580 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=2308, stack(0x0000002951d00000,0x0000002951e00000) (1024K)]
  0x000001987e2c0790 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=14140, stack(0x0000002951e00000,0x0000002951f00000) (1024K)]
  0x000001987e4a59a0 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=888, stack(0x0000002951f00000,0x0000002952000000) (1024K)]
  0x000001987e4ff530 JavaThread "Notification Thread"        daemon [_thread_blocked, id=8108, stack(0x0000002952000000,0x0000002952100000) (1024K)]
  0x000001987e4f9c30 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=16168, stack(0x0000002952100000,0x0000002952200000) (1024K)]
Total: 13

Other Threads:
  0x000001987c810a30 VMThread "VM Thread"                           [id=2708, stack(0x0000002951500000,0x0000002951600000) (1024K)]
  0x000001987c74c950 WatcherThread "VM Periodic Task Thread"        [id=15576, stack(0x0000002951400000,0x0000002951500000) (1024K)]
  0x0000019868493710 WorkerThread "GC Thread#0"                     [id=13820, stack(0x0000002951300000,0x0000002951400000) (1024K)]
  0x000001987eab20a0 WorkerThread "GC Thread#1"                     [id=16436, stack(0x0000002952200000,0x0000002952300000) (1024K)]
  0x000001987f090010 WorkerThread "GC Thread#2"                     [id=7720, stack(0x0000002952300000,0x0000002952400000) (1024K)]
  0x000001987f0903b0 WorkerThread "GC Thread#3"                     [id=10028, stack(0x0000002952400000,0x0000002952500000) (1024K)]
  0x000001987e5c4d20 WorkerThread "GC Thread#4"                     [id=13840, stack(0x0000002952500000,0x0000002952600000) (1024K)]
  0x000001987e5c50c0 WorkerThread "GC Thread#5"                     [id=15656, stack(0x0000002952600000,0x0000002952700000) (1024K)]
  0x000001987e5c5460 WorkerThread "GC Thread#6"                     [id=8832, stack(0x0000002952700000,0x0000002952800000) (1024K)]
Total: 9

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffdd53ac308] Metaspace_lock - owner thread: 0x0000019868475310

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000019800000000-0x0000019800ba0000-0x0000019800ba0000), size 12189696, SharedBaseAddress: 0x0000019800000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000019801000000-0x0000019841000000, reserved size: 1073741824
Narrow klass base: 0x0000019800000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 7975M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 29696K, used 9364K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 23% used [0x00000000eab00000,0x00000000eb0efb80,0x00000000ec400000)
  from space 4096K, 80% used [0x00000000ec400000,0x00000000ec735860,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 16K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0004000,0x00000000c4300000)
 Metaspace       used 5684K, committed 5952K, reserved 1114112K
  class space    used 595K, committed 768K, reserved 1048576K

Card table byte_map: [0x0000019867e40000,0x0000019868050000] _byte_map_base: 0x0000019867840000

Marking Bits: (ParMarkBitMap*) 0x00007ffdd53b31f0
 Begin Bits: [0x000001987a670000, 0x000001987b670000)
 End Bits:   [0x000001987b670000, 0x000001987c670000)

Polling page: 0x0000019866510000

Metaspace:

Usage:
  Non-class:      4.97 MB used.
      Class:    595.06 KB used.
       Both:      5.55 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       5.06 MB (  8%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     768.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       5.81 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  10.38 MB
       Class:  15.08 MB
        Both:  25.45 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 228.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 93.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 329.
num_chunk_merges: 0.
num_chunk_splits: 219.
num_chunks_enlarged: 139.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=502Kb max_used=502Kb free=119497Kb
 bounds [0x0000019873140000, 0x00000198733b0000, 0x000001987a670000]
CodeHeap 'profiled nmethods': size=120000Kb used=2136Kb max_used=2136Kb free=117863Kb
 bounds [0x000001986b670000, 0x000001986b8e0000, 0x0000019872ba0000]
CodeHeap 'non-nmethods': size=5760Kb used=1215Kb max_used=1233Kb free=4545Kb
 bounds [0x0000019872ba0000, 0x0000019872e10000, 0x0000019873140000]
 total_blobs=1774 nmethods=1305 adapters=376
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.958 Thread 0x000001987c86e580 nmethod 1299 0x000001986b880d10 code [0x000001986b880fa0, 0x000001986b881dd8]
Event: 0.958 Thread 0x000001987c86e580 1300   !   3       jdk.internal.ref.PhantomCleanable::insert (53 bytes)
Event: 0.958 Thread 0x000001987c86e580 nmethod 1300 0x000001986b882310 code [0x000001986b8824c0, 0x000001986b882730]
Event: 0.959 Thread 0x000001987c86e580 1301   !   3       java.util.zip.ZipCoder$UTF8ZipCoder::compare (81 bytes)
Event: 0.960 Thread 0x000001987c86e580 nmethod 1301 0x000001986b882890 code [0x000001986b882b00, 0x000001986b8836e8]
Event: 0.960 Thread 0x000001987c86e580 1302       3       java.lang.System$2::getBytesNoRepl (6 bytes)
Event: 0.961 Thread 0x000001987c86e580 nmethod 1302 0x000001986b883c10 code [0x000001986b883e20, 0x000001986b884608]
Event: 0.961 Thread 0x000001987c86e580 1303       3       java.security.MessageDigest::update (45 bytes)
Event: 0.961 Thread 0x000001987c86e580 nmethod 1303 0x000001986b884810 code [0x000001986b884a00, 0x000001986b884ec0]
Event: 0.961 Thread 0x000001987c86e580 1304       3       java.util.ArrayDeque::addLast (51 bytes)
Event: 0.962 Thread 0x000001987e4f9c30 nmethod 1291 0x00000198731bc290 code [0x00000198731bc480, 0x00000198731bcd98]
Event: 0.962 Thread 0x000001987c86e580 nmethod 1304 0x000001986b885090 code [0x000001986b885280, 0x000001986b8856f8]
Event: 0.962 Thread 0x000001987c82d870 1306       4       java.lang.AbstractStringBuilder::<init> (39 bytes)
Event: 0.962 Thread 0x000001987e4a59a0 1305       4       java.lang.AbstractStringBuilder::newCapacity (59 bytes)
Event: 0.963 Thread 0x000001987c86e580 1307       3       java.util.Collections$SetFromMap::add (22 bytes)
Event: 0.963 Thread 0x000001987c86e580 nmethod 1307 0x000001986b885890 code [0x000001986b885a40, 0x000001986b885c78]
Event: 0.963 Thread 0x000001987c86e580 1308       3       java.util.zip.ZipUtils::LOCSIG (6 bytes)
Event: 0.963 Thread 0x000001987c86e580 nmethod 1308 0x000001986b885d10 code [0x000001986b885ec0, 0x000001986b886180]
Event: 0.963 Thread 0x000001987e4a59a0 nmethod 1305 0x00000198731bd190 code [0x00000198731bd320, 0x00000198731bd438]
Event: 0.964 Thread 0x000001987c82d870 nmethod 1306 0x00000198731bd510 code [0x00000198731bd6a0, 0x00000198731bd870]

GC Heap History (2 events):
Event: 0.837 GC heap before
{Heap before GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 25600K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 0K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0000000,0x00000000c4300000)
 Metaspace       used 4315K, committed 4544K, reserved 1114112K
  class space    used 459K, committed 576K, reserved 1048576K
}
Event: 0.844 GC heap after
{Heap after GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 3286K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 80% used [0x00000000ec400000,0x00000000ec735860,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 16K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0004000,0x00000000c4300000)
 Metaspace       used 4315K, committed 4544K, reserved 1114112K
  class space    used 459K, committed 576K, reserved 1048576K
}

Dll operation events (8 events):
Event: 0.008 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.126 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.152 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.156 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.158 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.163 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.181 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.295 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll

Deoptimization events (20 events):
Event: 0.867 Thread 0x0000019868475310 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000198731a153c relative=0x000000000000027c
Event: 0.867 Thread 0x0000019868475310 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000198731a153c method=java.util.HashMap.hash(Ljava/lang/Object;)I @ 1 c2
Event: 0.867 Thread 0x0000019868475310 DEOPT PACKING pc=0x00000198731a153c sp=0x00000029512fea40
Event: 0.867 Thread 0x0000019868475310 DEOPT UNPACKING pc=0x0000019872bf6da2 sp=0x00000029512fe948 mode 2
Event: 0.867 Thread 0x0000019868475310 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001987319e2d0 relative=0x00000000000000b0
Event: 0.867 Thread 0x0000019868475310 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001987319e2d0 method=java.util.HashMap.hash(Ljava/lang/Object;)I @ 1 c2
Event: 0.867 Thread 0x0000019868475310 DEOPT PACKING pc=0x000001987319e2d0 sp=0x00000029512feac0
Event: 0.867 Thread 0x0000019868475310 DEOPT UNPACKING pc=0x0000019872bf6da2 sp=0x00000029512fea58 mode 2
Event: 0.868 Thread 0x0000019868475310 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000198731834b4 relative=0x0000000000000594
Event: 0.868 Thread 0x0000019868475310 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000198731834b4 method=java.util.Collections$UnmodifiableCollection$1.<init>(Ljava/util/Collections$UnmodifiableCollection;)V @ 17 c2
Event: 0.868 Thread 0x0000019868475310 DEOPT PACKING pc=0x00000198731834b4 sp=0x00000029512feaf0
Event: 0.868 Thread 0x0000019868475310 DEOPT UNPACKING pc=0x0000019872bf6da2 sp=0x00000029512fea38 mode 2
Event: 0.897 Thread 0x0000019868475310 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000019873184694 relative=0x0000000000000934
Event: 0.897 Thread 0x0000019868475310 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000019873184694 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 253 c2
Event: 0.897 Thread 0x0000019868475310 DEOPT PACKING pc=0x0000019873184694 sp=0x00000029512fd150
Event: 0.897 Thread 0x0000019868475310 DEOPT UNPACKING pc=0x0000019872bf6da2 sp=0x00000029512fd110 mode 2
Event: 0.935 Thread 0x0000019868475310 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000019873187078 relative=0x0000000000000198
Event: 0.935 Thread 0x0000019868475310 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000019873187078 method=java.lang.invoke.VarHandle.checkAccessModeThenIsDirect(Ljava/lang/invoke/VarHandle$AccessDescriptor;)Z @ 4 c2
Event: 0.935 Thread 0x0000019868475310 DEOPT PACKING pc=0x0000019873187078 sp=0x00000029512feaa0
Event: 0.935 Thread 0x0000019868475310 DEOPT UNPACKING pc=0x0000019872bf6da2 sp=0x00000029512fe9d8 mode 2

Classes loaded (20 events):
Event: 0.931 Loading class java/util/ComparableTimSort
Event: 0.931 Loading class java/util/ComparableTimSort done
Event: 0.952 Loading class java/security/AllPermissionCollection
Event: 0.953 Loading class java/security/AllPermissionCollection done
Event: 0.954 Loading class java/lang/ArrayIndexOutOfBoundsException
Event: 0.954 Loading class java/lang/IndexOutOfBoundsException
Event: 0.954 Loading class java/lang/IndexOutOfBoundsException done
Event: 0.954 Loading class java/lang/ArrayIndexOutOfBoundsException done
Event: 0.957 Loading class java/util/concurrent/BlockingQueue
Event: 0.957 Loading class java/util/concurrent/BlockingQueue done
Event: 0.959 Loading class java/util/concurrent/Callable
Event: 0.959 Loading class java/util/concurrent/Callable done
Event: 0.963 Loading class java/util/concurrent/atomic/AtomicReference
Event: 0.963 Loading class java/util/concurrent/atomic/AtomicReference done
Event: 0.964 Loading class java/util/concurrent/SynchronousQueue
Event: 0.964 Loading class java/util/AbstractQueue
Event: 0.964 Loading class java/util/AbstractQueue done
Event: 0.964 Loading class java/util/concurrent/SynchronousQueue done
Event: 0.964 Loading class java/util/concurrent/SynchronousQueue$Transferer
Event: 0.964 Loading class java/util/concurrent/LinkedTransferQueue

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.695 Thread 0x0000019868475310 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ebae40a8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ebae40a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.711 Thread 0x0000019868475310 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ebb7b5c0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, int)'> (0x00000000ebb7b5c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.783 Thread 0x0000019868475310 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ebfc9728}: 'java.lang.ClassLoader java.lang.ClassLoader.getPlatformClassLoader(java.lang.Class)'> (0x00000000ebfc9728) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.832 Thread 0x0000019868475310 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ec3cde60}: sun/net/www/protocol/c/Handler> (0x00000000ec3cde60) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 0.832 Thread 0x0000019868475310 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ec3cf1f0}: sun/net/www/protocol/c/Handler> (0x00000000ec3cf1f0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 0.832 Thread 0x0000019868475310 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ec3d03b0}: sun/net/www/protocol/c/Handler> (0x00000000ec3d03b0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 0.837 Thread 0x0000019868475310 Exception <a 'java/io/FileNotFoundException'{0x00000000ec3f4ee0}> (0x00000000ec3f4ee0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 0.865 Thread 0x0000019868475310 Implicit null exception at 0x000001987319e6c9 to 0x000001987319f060
Event: 0.866 Thread 0x0000019868475310 Implicit null exception at 0x00000198731a1a1a to 0x00000198731a1c24
Event: 0.866 Thread 0x0000019868475310 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaca36f8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eaca36f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.867 Thread 0x0000019868475310 Implicit null exception at 0x00000198731a131a to 0x00000198731a1524
Event: 0.867 Thread 0x0000019868475310 Implicit null exception at 0x000001987319e23d to 0x000001987319e2c1
Event: 0.902 Thread 0x0000019868475310 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eae60fa0}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x00000000eae60fa0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.903 Thread 0x0000019868475310 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eae68990}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x00000000eae68990) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.904 Thread 0x0000019868475310 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eae76388}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000eae76388) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.911 Thread 0x0000019868475310 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eae9fc20}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eae9fc20) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.912 Thread 0x0000019868475310 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000eaea8120}: Found class java.lang.Object, but interface was expected> (0x00000000eaea8120) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 0.913 Thread 0x0000019868475310 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaeac2c0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000eaeac2c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.914 Thread 0x0000019868475310 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaeb33b8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000eaeb33b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.946 Thread 0x0000019868475310 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eafefb58}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eafefb58) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (12 events):
Event: 0.125 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.125 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.197 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.197 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.638 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.638 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.654 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.654 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.791 Executing VM operation: ICBufferFull
Event: 0.791 Executing VM operation: ICBufferFull done
Event: 0.837 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 0.844 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (13 events):
Event: 0.028 Thread 0x0000019868475310 Thread added: 0x0000019868475310
Event: 0.049 Thread 0x0000019868475310 Thread added: 0x000001987c823340
Event: 0.049 Thread 0x0000019868475310 Thread added: 0x000001987c824c40
Event: 0.049 Thread 0x0000019868475310 Thread added: 0x000001987c8269c0
Event: 0.049 Thread 0x0000019868475310 Thread added: 0x000001987c8278d0
Event: 0.049 Thread 0x0000019868475310 Thread added: 0x000001987c82afe0
Event: 0.049 Thread 0x0000019868475310 Thread added: 0x000001987c82ca40
Event: 0.050 Thread 0x0000019868475310 Thread added: 0x000001987c82d870
Event: 0.058 Thread 0x0000019868475310 Thread added: 0x000001987c86e580
Event: 0.100 Thread 0x0000019868475310 Thread added: 0x000001987e2c0790
Event: 0.292 Thread 0x000001987c86e580 Thread added: 0x000001987e4a59a0
Event: 0.452 Thread 0x0000019868475310 Thread added: 0x000001987e4ff530
Event: 0.608 Thread 0x000001987c86e580 Thread added: 0x000001987e4f9c30


Dynamic libraries:
0x00007ff6d47b0000 - 0x00007ff6d47be000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ffe80050000 - 0x00007ffe80248000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffe7fb30000 - 0x00007ffe7fbf2000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffe7d7c0000 - 0x00007ffe7dab6000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffe7dce0000 - 0x00007ffe7dde0000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffe5e7d0000 - 0x00007ffe5e7e8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ffe7e080000 - 0x00007ffe7e21d000 	C:\WINDOWS\System32\USER32.dll
0x00007ffe7dde0000 - 0x00007ffe7de02000 	C:\WINDOWS\System32\win32u.dll
0x00007ffe7f810000 - 0x00007ffe7f83b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffe7db70000 - 0x00007ffe7dc8a000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffe7d720000 - 0x00007ffe7d7bd000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe5cbf0000 - 0x00007ffe5cc0e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffe65d80000 - 0x00007ffe6601a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5794_none_60bcd33171f2783c\COMCTL32.dll
0x00007ffe7f170000 - 0x00007ffe7f20e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffe7fe10000 - 0x00007ffe7fe3f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffe777e0000 - 0x00007ffe777ec000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffe4f5f0000 - 0x00007ffe4f67d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007ffdd4700000 - 0x00007ffdd5490000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ffe7f760000 - 0x00007ffe7f80f000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffe7f0d0000 - 0x00007ffe7f16f000 	C:\WINDOWS\System32\sechost.dll
0x00007ffe7f8a0000 - 0x00007ffe7f9c3000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffe7d6f0000 - 0x00007ffe7d717000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffe7f2c0000 - 0x00007ffe7f32b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffe7cc80000 - 0x00007ffe7cccb000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffe6d7c0000 - 0x00007ffe6d7e7000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffe74cf0000 - 0x00007ffe74cfa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffe7caf0000 - 0x00007ffe7cb02000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffe7b4f0000 - 0x00007ffe7b502000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffe6d7a0000 - 0x00007ffe6d7aa000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ffe74aa0000 - 0x00007ffe74ca1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffe56040000 - 0x00007ffe56074000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffe7dff0000 - 0x00007ffe7e072000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffe78cd0000 - 0x00007ffe78cdf000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ffe5cbd0000 - 0x00007ffe5cbef000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ffe7e350000 - 0x00007ffe7eabe000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffe7b710000 - 0x00007ffe7beb3000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffe7f330000 - 0x00007ffe7f683000 	C:\WINDOWS\System32\combase.dll
0x00007ffe7d120000 - 0x00007ffe7d14b000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ffe7f690000 - 0x00007ffe7f75d000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffe7f210000 - 0x00007ffe7f2bd000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffe7fac0000 - 0x00007ffe7fb15000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffe7d620000 - 0x00007ffe7d645000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffe5ca90000 - 0x00007ffe5caa8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ffe5bbc0000 - 0x00007ffe5bbd0000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ffe76610000 - 0x00007ffe7671a000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffe7ce80000 - 0x00007ffe7ceec000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffe5bb10000 - 0x00007ffe5bb26000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ffe5cab0000 - 0x00007ffe5cac0000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5794_none_60bcd33171f2783c;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\1eb1b3ffae57f2c6129ade188dcbd886\redhat.java\ss_ws --pipe=\\.\pipe\lsp-cce31334cece12711e1c5847512e99c1-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-11
PATH=C:\app\tshep\product\21c\dbhomeXE\bin;C:\app\tshep\product\18.0.0\dbhomeXE\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files (x86)\dotnet\;C:\Program Files\Java\jdk-11\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\xampp\php;C:\Program Files\Java\glassfish4\bin;C:\Users\<USER>\Downloads\apache-maven-3.9.9\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.12;C:\Program Files (x86)\cloudflared\;C:\Program Files\Redis\;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\anaconda3;C:\Users\<USER>\anaconda3\Library\mingw-w64\bin;C:\Users\<USER>\anaconda3\Library\usr\bin;C:\Users\<USER>\anaconda3\Library\bin;C:\Users\<USER>\anaconda3\Scripts;C:\Users\<USER>\anaconda3\bin;C:\Users\<USER>\anaconda3\condabin;C:\app\tshep\product\21c\dbhomeXE\bin;C:\app\tshep\product\18.0.0\dbhomeXE\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\dotnet;C:\Program Files (x86)\dotnet;C:\Program Files\Java\jdk-11\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs;C:\ProgramData\chocolatey\bin;C:\xampp\php;C:\Program Files\Java\glassfish4\bin;C:\Users\<USER>\Downloads\apache-maven-3.9.9\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.12;C:\Program Files (x86)\cloudflared;C:\Program Files\Redis;C:\Users\<USER>\scoop\shims;C:\app\tshep\product\21c\dbhomeXE\bin;C:\app\tshep\product\18.0.0\dbhomeXE\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\dotnet;C:\Program Files (x86)\dotnet;C:\Program Files\Java\jdk-11\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs;C:\ProgramData\chocolatey\bin;C:\xampp\php;C:\Program Files\Java\glassfish4\bin;C:\Users\<USER>\Downloads\apache-maven-3.9.9\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.12;C:\Program Files (x86)\cloudflared;C:\Program Files\Redis;C:\Users\<USER>\Downloads\ffmpeg-2025-03-24-git-cbbc927a67-full_build\ffmpeg-2025-03-24-git-cbbc927a67-full_build\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\Downloads\ffmpeg-2025-03-24-git-cbbc927a67-full_build\ffmpeg-2025-03-24-git-cbbc927a67-full_build\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools
USERNAME=tshep
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 140 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5794)
OS uptime: 0 days 0:39 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 140 stepping 1 microcode 0x9a, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ibt, cet_ss, avx512_ifma
Processor Information for processor 0
  Max Mhz: 2419, Current Mhz: 2419, Mhz Limit: 2419
Processor Information for processor 1
  Max Mhz: 2419, Current Mhz: 2419, Mhz Limit: 2419
Processor Information for processor 2
  Max Mhz: 2419, Current Mhz: 2419, Mhz Limit: 2419
Processor Information for processor 3
  Max Mhz: 2419, Current Mhz: 2419, Mhz Limit: 2419
Processor Information for processor 4
  Max Mhz: 2419, Current Mhz: 2419, Mhz Limit: 2419
Processor Information for processor 5
  Max Mhz: 2419, Current Mhz: 907, Mhz Limit: 2419
Processor Information for processor 6
  Max Mhz: 2419, Current Mhz: 2419, Mhz Limit: 2419
Processor Information for processor 7
  Max Mhz: 2419, Current Mhz: 2419, Mhz Limit: 2419

Memory: 4k page, system-wide physical 7975M (1533M free)
TotalPageFile size 11429M (AvailPageFile size 3M)
current process WorkingSet (physical memory assigned to process): 83M, peak: 83M
current process commit charge ("private bytes"): 203M, peak: 203M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
