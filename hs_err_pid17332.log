#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 32784 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=17332, tid=8808
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\********************************\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\********************************\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-a9d20c496d97431fa1a083e1f1779fa7-sock

Host: 11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz, 8 cores, 7G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5794)
Time: Sun Jun  1 15:09:52 2025 South Africa Standard Time elapsed time: 22.568210 seconds (0d 0h 0m 22s)

---------------  T H R E A D  ---------------

Current thread (0x000002d17cb7ea80):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=8808, stack(0x000000fc9e300000,0x000000fc9e400000) (1024K)]


Current CompileTask:
C2:22568 3176   !   4       java.io.BufferedInputStream::read (69 bytes)

Stack: [0x000000fc9e300000,0x000000fc9e400000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0xc51a5]
V  [jvm.dll+0x6a96fc]
V  [jvm.dll+0x6573a1]
V  [jvm.dll+0x65ca8d]
V  [jvm.dll+0x65d9c6]
V  [jvm.dll+0x65bd5e]
V  [jvm.dll+0x247975]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002d17e6a0780, length=19, elements={
0x000002d1777c4c60, 0x000002d17cb71e40, 0x000002d17cb73740, 0x000002d17cb763b0,
0x000002d17cb772c0, 0x000002d17cb781d0, 0x000002d17cb7bc40, 0x000002d17cb7ea80,
0x000002d17e58cf50, 0x000002d177831fc0, 0x000002d17e868f50, 0x000002d17ef29d70,
0x000002d17e904d10, 0x000002d17ee3cdf0, 0x000002d17ee3c760, 0x000002d17ee3ba40,
0x000002d15468b1f0, 0x000002d17ee3b3b0, 0x000002d17ee3c0d0
}

Java Threads: ( => current thread )
  0x000002d1777c4c60 JavaThread "main"                              [_thread_blocked, id=9816, stack(0x000000fc9d900000,0x000000fc9da00000) (1024K)]
  0x000002d17cb71e40 JavaThread "Reference Handler"          daemon [_thread_blocked, id=12176, stack(0x000000fc9dd00000,0x000000fc9de00000) (1024K)]
  0x000002d17cb73740 JavaThread "Finalizer"                  daemon [_thread_blocked, id=2380, stack(0x000000fc9de00000,0x000000fc9df00000) (1024K)]
  0x000002d17cb763b0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=6080, stack(0x000000fc9df00000,0x000000fc9e000000) (1024K)]
  0x000002d17cb772c0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=5384, stack(0x000000fc9e000000,0x000000fc9e100000) (1024K)]
  0x000002d17cb781d0 JavaThread "Service Thread"             daemon [_thread_blocked, id=13120, stack(0x000000fc9e100000,0x000000fc9e200000) (1024K)]
  0x000002d17cb7bc40 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=17008, stack(0x000000fc9e200000,0x000000fc9e300000) (1024K)]
=>0x000002d17cb7ea80 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=8808, stack(0x000000fc9e300000,0x000000fc9e400000) (1024K)]
  0x000002d17e58cf50 JavaThread "C1 CompilerThread0"         daemon [_thread_in_vm, id=16512, stack(0x000000fc9e400000,0x000000fc9e500000) (1024K)]
  0x000002d177831fc0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=16008, stack(0x000000fc9e500000,0x000000fc9e600000) (1024K)]
  0x000002d17e868f50 JavaThread "Notification Thread"        daemon [_thread_blocked, id=8980, stack(0x000000fc9e600000,0x000000fc9e700000) (1024K)]
  0x000002d17ef29d70 JavaThread "Active Thread: Equinox Container: b5216fc7-6be5-477b-b931-7e829059de8c"        [_thread_blocked, id=11188, stack(0x000000fc9f500000,0x000000fc9f600000) (1024K)]
  0x000002d17e904d10 JavaThread "Framework Event Dispatcher: Equinox Container: b5216fc7-6be5-477b-b931-7e829059de8c" daemon [_thread_blocked, id=8768, stack(0x000000fc9e700000,0x000000fc9e800000) (1024K)]
  0x000002d17ee3cdf0 JavaThread "Start Level: Equinox Container: b5216fc7-6be5-477b-b931-7e829059de8c" daemon [_thread_in_Java, id=4660, stack(0x000000fc9e800000,0x000000fc9e900000) (1024K)]
  0x000002d17ee3c760 JavaThread "Refresh Thread: Equinox Container: b5216fc7-6be5-477b-b931-7e829059de8c" daemon [_thread_blocked, id=9452, stack(0x000000fc9f800000,0x000000fc9f900000) (1024K)]
  0x000002d17ee3ba40 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=13056, stack(0x000000fc9f700000,0x000000fc9f800000) (1024K)]
  0x000002d15468b1f0 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=2092, stack(0x000000fc9ef00000,0x000000fc9f000000) (1024K)]
  0x000002d17ee3b3b0 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=12520, stack(0x000000fc9f000000,0x000000fc9f100000) (1024K)]
  0x000002d17ee3c0d0 JavaThread "SCR Component Registry"     daemon [_thread_blocked, id=15228, stack(0x000000fc9f100000,0x000000fc9f200000) (1024K)]
Total: 19

Other Threads:
  0x000002d177883bb0 VMThread "VM Thread"                           [id=18104, stack(0x000000fc9dc00000,0x000000fc9dd00000) (1024K)]
  0x000002d17ca9c950 WatcherThread "VM Periodic Task Thread"        [id=11404, stack(0x000000fc9db00000,0x000000fc9dc00000) (1024K)]
  0x000002d1777e52c0 WorkerThread "GC Thread#0"                     [id=6024, stack(0x000000fc9da00000,0x000000fc9db00000) (1024K)]
  0x000002d17eed02c0 WorkerThread "GC Thread#1"                     [id=17316, stack(0x000000fc9e900000,0x000000fc9ea00000) (1024K)]
  0x000002d17eed0660 WorkerThread "GC Thread#2"                     [id=6944, stack(0x000000fc9ea00000,0x000000fc9eb00000) (1024K)]
  0x000002d17eed0a00 WorkerThread "GC Thread#3"                     [id=12720, stack(0x000000fc9eb00000,0x000000fc9ec00000) (1024K)]
  0x000002d17eed0da0 WorkerThread "GC Thread#4"                     [id=13916, stack(0x000000fc9ec00000,0x000000fc9ed00000) (1024K)]
  0x000002d17eed1140 WorkerThread "GC Thread#5"                     [id=5052, stack(0x000000fc9ed00000,0x000000fc9ee00000) (1024K)]
  0x000002d17eed14e0 WorkerThread "GC Thread#6"                     [id=6392, stack(0x000000fc9ee00000,0x000000fc9ef00000) (1024K)]
  0x000002d17cbac450 WorkerThread "GC Thread#7"                     [id=8460, stack(0x000000fc9f600000,0x000000fc9f700000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  22586 3176   !   4       java.io.BufferedInputStream::read (69 bytes)
C2 CompilerThread1  22586 3208       4       java.lang.String::startsWith (123 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002d10f000000-0x000002d10fba0000-0x000002d10fba0000), size 12189696, SharedBaseAddress: 0x000002d10f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002d110000000-0x000002d150000000, reserved size: 1073741824
Narrow klass base: 0x000002d10f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 7975M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 29184K, used 10308K [0x00000000eab00000, 0x00000000ed080000, 0x0000000100000000)
  eden space 25088K, 24% used [0x00000000eab00000,0x00000000eb1141f8,0x00000000ec380000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7fcf30,0x00000000ec800000)
  to   space 6656K, 0% used [0x00000000eca00000,0x00000000eca00000,0x00000000ed080000)
 ParOldGen       total 68608K, used 10493K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 15% used [0x00000000c0000000,0x00000000c0a3f510,0x00000000c4300000)
 Metaspace       used 12968K, committed 13376K, reserved 1114112K
  class space    used 1295K, committed 1472K, reserved 1048576K

Card table byte_map: [0x000002d177170000,0x000002d177380000] _byte_map_base: 0x000002d176b70000

Marking Bits: (ParMarkBitMap*) 0x00007ffdd53b31f0
 Begin Bits: [0x000002d17a9c0000, 0x000002d17b9c0000)
 End Bits:   [0x000002d17b9c0000, 0x000002d17c9c0000)

Polling page: 0x000002d175770000

Metaspace:

Usage:
  Non-class:     11.40 MB used.
      Class:      1.27 MB used.
       Both:     12.67 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      11.62 MB ( 18%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.44 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      13.06 MB (  1%) committed. 

Chunk freelists:
   Non-Class:  3.73 MB
       Class:  14.58 MB
        Both:  18.31 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 304.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 209.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 633.
num_chunk_merges: 0.
num_chunk_splits: 435.
num_chunks_enlarged: 304.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=1464Kb max_used=1464Kb free=118535Kb
 bounds [0x000002d107ad0000, 0x000002d107d40000, 0x000002d10f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=6611Kb max_used=6611Kb free=113388Kb
 bounds [0x000002d100000000, 0x000002d100680000, 0x000002d107530000]
CodeHeap 'non-nmethods': size=5760Kb used=1250Kb max_used=1278Kb free=4509Kb
 bounds [0x000002d107530000, 0x000002d1077a0000, 0x000002d107ad0000]
 total_blobs=3727 nmethods=3222 adapters=411
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 22.561 Thread 0x000002d17e58cf50 nmethod 3179 0x000002d10064aa10 code [0x000002d10064b040, 0x000002d10064f170]
Event: 22.562 Thread 0x000002d17e58cf50 3184       3       java.io.BufferedInputStream::implRead (112 bytes)
Event: 22.562 Thread 0x000002d17e58cf50 nmethod 3184 0x000002d100650510 code [0x000002d100650740, 0x000002d100650e88]
Event: 22.562 Thread 0x000002d17e58cf50 3175  s    3       org.eclipse.core.internal.registry.RegistryObjectManager::add (49 bytes)
Event: 22.562 Thread 0x000002d17e58cf50 nmethod 3175 0x000002d100651110 code [0x000002d1006512e0, 0x000002d100651820]
Event: 22.562 Thread 0x000002d17e58cf50 3161       1       org.eclipse.core.internal.registry.ReferenceMap$SoftRef::getNext (5 bytes)
Event: 22.562 Thread 0x000002d17e58cf50 nmethod 3161 0x000002d107c3ad10 code [0x000002d107c3aea0, 0x000002d107c3af68]
Event: 22.562 Thread 0x000002d17e58cf50 3186       1       org.eclipse.core.internal.registry.ReferenceMap$SoftRef::setNext (6 bytes)
Event: 22.563 Thread 0x000002d17e58cf50 nmethod 3186 0x000002d107c3b010 code [0x000002d107c3b1a0, 0x000002d107c3b280]
Event: 22.563 Thread 0x000002d17e58cf50 3180       3       org.eclipse.core.internal.registry.ExtensionsParser::translate (13 bytes)
Event: 22.563 Thread 0x000002d17e58cf50 nmethod 3180 0x000002d100651a10 code [0x000002d100651bc0, 0x000002d100651e48]
Event: 22.563 Thread 0x000002d17e58cf50 3181       3       org.eclipse.core.internal.registry.ExtensionRegistry::translate (19 bytes)
Event: 22.563 Thread 0x000002d17e58cf50 nmethod 3181 0x000002d100651f90 code [0x000002d100652140, 0x000002d100652370]
Event: 22.563 Thread 0x000002d17e58cf50 3182       3       org.eclipse.core.internal.registry.osgi.RegistryStrategyOSGI::translate (7 bytes)
Event: 22.563 Thread 0x000002d17e58cf50 nmethod 3182 0x000002d100652490 code [0x000002d100652640, 0x000002d100652780]
Event: 22.563 Thread 0x000002d17e58cf50 3183   !   3       org.eclipse.core.internal.runtime.ResourceTranslator::getResourceString (121 bytes)
Event: 22.564 Thread 0x000002d15468b1f0 nmethod 3173 0x000002d107c3b310 code [0x000002d107c3b500, 0x000002d107c3baa0]
Event: 22.564 Thread 0x000002d15468b1f0 3185 %     4       com.sun.org.apache.xerces.internal.impl.io.UTF8Reader::read @ 113 (1443 bytes)
Event: 22.564 Thread 0x000002d17e58cf50 nmethod 3183 0x000002d100652810 code [0x000002d100652b20, 0x000002d100653a08]
Event: 22.564 Thread 0x000002d17e58cf50 3190       3       org.eclipse.core.internal.registry.ExtensionsParser::endElement (557 bytes)

GC Heap History (10 events):
Event: 1.076 GC heap before
{Heap before GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 25600K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 0K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0000000,0x00000000c4300000)
 Metaspace       used 4515K, committed 4736K, reserved 1114112K
  class space    used 479K, committed 576K, reserved 1048576K
}
Event: 1.093 GC heap after
{Heap after GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 3270K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 79% used [0x00000000ec400000,0x00000000ec731870,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 0K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0000000,0x00000000c4300000)
 Metaspace       used 4515K, committed 4736K, reserved 1114112K
  class space    used 479K, committed 576K, reserved 1048576K
}
Event: 8.070 GC heap before
{Heap before GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 28870K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 79% used [0x00000000ec400000,0x00000000ec731870,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 0K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0000000,0x00000000c4300000)
 Metaspace       used 8320K, committed 8576K, reserved 1114112K
  class space    used 851K, committed 960K, reserved 1048576K
}
Event: 8.078 GC heap after
{Heap after GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 4078K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfbbd0,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 176K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c002c000,0x00000000c4300000)
 Metaspace       used 8320K, committed 8576K, reserved 1114112K
  class space    used 851K, committed 960K, reserved 1048576K
}
Event: 8.752 GC heap before
{Heap before GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 29678K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfbbd0,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 176K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c002c000,0x00000000c4300000)
 Metaspace       used 8757K, committed 9088K, reserved 1114112K
  class space    used 882K, committed 1024K, reserved 1048576K
}
Event: 8.763 GC heap after
{Heap after GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 4089K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7fe7d8,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 5119K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 7% used [0x00000000c0000000,0x00000000c04ffe58,0x00000000c4300000)
 Metaspace       used 8757K, committed 9088K, reserved 1114112K
  class space    used 882K, committed 1024K, reserved 1048576K
}
Event: 10.879 GC heap before
{Heap before GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 29689K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7fe7d8,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 5119K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 7% used [0x00000000c0000000,0x00000000c04ffe58,0x00000000c4300000)
 Metaspace       used 9959K, committed 10304K, reserved 1114112K
  class space    used 1011K, committed 1152K, reserved 1048576K
}
Event: 10.885 GC heap after
{Heap after GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 4064K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbf8198,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 9733K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 14% used [0x00000000c0000000,0x00000000c0981500,0x00000000c4300000)
 Metaspace       used 9959K, committed 10304K, reserved 1114112K
  class space    used 1011K, committed 1152K, reserved 1048576K
}
Event: 22.415 GC heap before
{Heap before GC invocations=5 (full 0):
 PSYoungGen      total 29696K, used 29664K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbf8198,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 9733K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 14% used [0x00000000c0000000,0x00000000c0981500,0x00000000c4300000)
 Metaspace       used 12407K, committed 12864K, reserved 1114112K
  class space    used 1260K, committed 1472K, reserved 1048576K
}
Event: 22.419 GC heap after
{Heap after GC invocations=5 (full 0):
 PSYoungGen      total 29184K, used 4083K [0x00000000eab00000, 0x00000000ed080000, 0x0000000100000000)
  eden space 25088K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec380000)
  from space 4096K, 99% used [0x00000000ec400000,0x00000000ec7fcf30,0x00000000ec800000)
  to   space 6656K, 0% used [0x00000000eca00000,0x00000000eca00000,0x00000000ed080000)
 ParOldGen       total 68608K, used 10493K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 15% used [0x00000000c0000000,0x00000000c0a3f510,0x00000000c4300000)
 Metaspace       used 12407K, committed 12864K, reserved 1114112K
  class space    used 1260K, committed 1472K, reserved 1048576K
}

Dll operation events (9 events):
Event: 0.012 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.117 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.162 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.177 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.180 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.183 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.204 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.305 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 8.604 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll

Deoptimization events (20 events):
Event: 22.218 Thread 0x000002d17ee3cdf0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002d107c06014 relative=0x00000000000005f4
Event: 22.218 Thread 0x000002d17ee3cdf0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002d107c06014 method=org.eclipse.osgi.framework.util.CaseInsensitiveDictionaryMap.findCommonKeyIndex(Ljava/lang/String;)Lorg/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$CaseInsensit
Event: 22.218 Thread 0x000002d17ee3cdf0 DEOPT PACKING pc=0x000002d107c06014 sp=0x000000fc9e8fe390
Event: 22.218 Thread 0x000002d17ee3cdf0 DEOPT UNPACKING pc=0x000002d107586da2 sp=0x000000fc9e8fe320 mode 2
Event: 22.403 Thread 0x000002d17ee3cdf0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002d107b7f728 relative=0x0000000000001528
Event: 22.403 Thread 0x000002d17ee3cdf0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002d107b7f728 method=java.io.WinNTFileSystem.normalize(Ljava/lang/String;II)Ljava/lang/String; @ 100 c2
Event: 22.403 Thread 0x000002d17ee3cdf0 DEOPT PACKING pc=0x000002d107b7f728 sp=0x000000fc9e8f9020
Event: 22.403 Thread 0x000002d17ee3cdf0 DEOPT UNPACKING pc=0x000002d107586da2 sp=0x000000fc9e8f8f60 mode 2
Event: 22.460 Thread 0x000002d17ee3cdf0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002d107c0bf90 relative=0x00000000000011f0
Event: 22.460 Thread 0x000002d17ee3cdf0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002d107c0bf90 method=java.util.Properties.loadConvert([CIILjava/lang/StringBuilder;)Ljava/lang/String; @ 491 c2
Event: 22.460 Thread 0x000002d17ee3cdf0 DEOPT PACKING pc=0x000002d107c0bf90 sp=0x000000fc9e8f74a0
Event: 22.460 Thread 0x000002d17ee3cdf0 DEOPT UNPACKING pc=0x000002d107586da2 sp=0x000000fc9e8f7490 mode 2
Event: 22.469 Thread 0x000002d17ee3cdf0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002d107bea9a4 relative=0x0000000000000a44
Event: 22.469 Thread 0x000002d17ee3cdf0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002d107bea9a4 method=java.io.BufferedInputStream.implRead([BII)I @ 100 c2
Event: 22.469 Thread 0x000002d17ee3cdf0 DEOPT PACKING pc=0x000002d107bea9a4 sp=0x000000fc9e8f8450
Event: 22.469 Thread 0x000002d17ee3cdf0 DEOPT UNPACKING pc=0x000002d107586da2 sp=0x000000fc9e8f8428 mode 2
Event: 22.471 Thread 0x000002d17ee3cdf0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002d107bea950 relative=0x00000000000009f0
Event: 22.471 Thread 0x000002d17ee3cdf0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002d107bea950 method=java.io.BufferedInputStream.implRead([BII)I @ 55 c2
Event: 22.471 Thread 0x000002d17ee3cdf0 DEOPT PACKING pc=0x000002d107bea950 sp=0x000000fc9e8f8450
Event: 22.471 Thread 0x000002d17ee3cdf0 DEOPT UNPACKING pc=0x000002d107586da2 sp=0x000000fc9e8f8428 mode 2

Classes loaded (20 events):
Event: 22.333 Loading class java/io/CharConversionException done
Event: 22.333 Loading class com/sun/org/apache/xerces/internal/impl/io/MalformedByteSequenceException done
Event: 22.345 Loading class java/util/concurrent/CompletionStage
Event: 22.346 Loading class java/util/concurrent/CompletionStage done
Event: 22.347 Loading class java/util/Timer
Event: 22.347 Loading class java/util/Timer done
Event: 22.348 Loading class java/util/TaskQueue
Event: 22.348 Loading class java/util/TaskQueue done
Event: 22.348 Loading class java/util/TimerThread
Event: 22.348 Loading class java/util/TimerThread done
Event: 22.348 Loading class java/util/Timer$ThreadReaper
Event: 22.348 Loading class java/util/Timer$ThreadReaper done
Event: 22.370 Loading class java/util/concurrent/locks/ReentrantLock$FairSync
Event: 22.370 Loading class java/util/concurrent/locks/ReentrantLock$FairSync done
Event: 22.458 Loading class java/lang/IllegalCallerException
Event: 22.458 Loading class java/lang/IllegalCallerException done
Event: 22.462 Loading class java/lang/invoke/DirectMethodHandle$1
Event: 22.462 Loading class java/lang/invoke/DirectMethodHandle$1 done
Event: 22.465 Loading class org/xml/sax/SAXParseException
Event: 22.465 Loading class org/xml/sax/SAXParseException done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 1.160 Thread 0x000002d1777c4c60 Implicit null exception at 0x000002d107b3333d to 0x000002d107b333c1
Event: 1.215 Thread 0x000002d1777c4c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaeda6d0}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x00000000eaeda6d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.216 Thread 0x000002d1777c4c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaee20c0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x00000000eaee20c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.217 Thread 0x000002d1777c4c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaeeff88}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000eaeeff88) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.225 Thread 0x000002d1777c4c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaf19578}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eaf19578) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.227 Thread 0x000002d1777c4c60 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000eaf21a78}: Found class java.lang.Object, but interface was expected> (0x00000000eaf21a78) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 1.227 Thread 0x000002d1777c4c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaf25c18}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000eaf25c18) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.228 Thread 0x000002d1777c4c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaf2caf0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000eaf2caf0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.330 Thread 0x000002d1777c4c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb1f0b80}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000000eb1f0b80) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.760 Thread 0x000002d1777c4c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ebf65418}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int, java.lang.Object)'> (0x00000000ebf65418) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.782 Thread 0x000002d1777c4c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ec04a220}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000ec04a220) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 8.594 Thread 0x000002d1777c4c60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ebf71a08}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ebf71a08) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 8.595 Thread 0x000002d1777c4c60 Exception <a 'java/lang/UnsatisfiedLinkError'{0x00000000ebf7b128}: 'void org.eclipse.equinox.launcher.JNIBridge._update_splash()'> (0x00000000ebf7b128) 
thrown [s\src\hotspot\share\prims\nativeLookup.cpp, line 415]
Event: 9.072 Thread 0x000002d17ee3cdf0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb917840}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000eb917840) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 9.103 Thread 0x000002d17ee3cdf0 Exception <a 'java/lang/NoClassDefFoundError'{0x00000000eb9bb3f0}: jakarta/servlet/ServletContainerInitializer> (0x00000000eb9bb3f0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 301]
Event: 9.146 Thread 0x000002d17ee3cdf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000ebb406b8}: sun/util/logging/resources/spi/loggingProvider> (0x00000000ebb406b8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 9.183 Thread 0x000002d17ee3cdf0 Exception <a 'java/io/IOException'{0x00000000ebcb3308}> (0x00000000ebcb3308) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 22.254 Thread 0x000002d17ee3cdf0 Exception <a 'java/lang/NullPointerException'{0x00000000ebb67de0}> (0x00000000ebb67de0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1372]
Event: 22.254 Thread 0x000002d17ee3cdf0 Exception <a 'java/lang/NullPointerException'{0x00000000ebb680c0}> (0x00000000ebb680c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1372]
Event: 22.453 Thread 0x000002d17ee3cdf0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eac2aa60}: 'void java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eac2aa60) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 9.194 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 9.194 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 10.200 Executing VM operation: Cleanup
Event: 10.200 Executing VM operation: Cleanup done
Event: 10.879 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 10.885 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 13.905 Executing VM operation: Cleanup
Event: 13.905 Executing VM operation: Cleanup done
Event: 14.908 Executing VM operation: Cleanup
Event: 14.908 Executing VM operation: Cleanup done
Event: 18.927 Executing VM operation: Cleanup
Event: 18.927 Executing VM operation: Cleanup done
Event: 20.947 Executing VM operation: Cleanup
Event: 20.947 Executing VM operation: Cleanup done
Event: 21.960 Executing VM operation: Cleanup
Event: 21.961 Executing VM operation: Cleanup done
Event: 22.370 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 22.370 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 22.415 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 22.419 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 8.055 Thread 0x000002d1777c4c60 Thread added: 0x000002d17ee3cdf0
Event: 8.085 Thread 0x000002d17e58cf50 Thread added: 0x000002d154078bc0
Event: 8.593 Thread 0x000002d1777c4c60 Thread added: 0x000002d17ee3c760
Event: 8.735 Thread 0x000002d17ee3c760 Thread added: 0x000002d17ee3b3b0
Event: 8.735 Thread 0x000002d17ee3c760 Thread added: 0x000002d17ee3e830
Event: 8.748 Thread 0x000002d17e58cf50 Thread added: 0x000002d15426ee20
Event: 9.670 Thread 0x000002d15426ee20 Thread exited: 0x000002d15426ee20
Event: 9.670 Thread 0x000002d154078bc0 Thread exited: 0x000002d154078bc0
Event: 9.968 Thread 0x000002d17ee3cdf0 Thread added: 0x000002d17ee3ba40
Event: 18.816 Thread 0x000002d17ee43390 Thread exited: 0x000002d17ee43390
Event: 18.816 Thread 0x000002d15410fe50 Thread exited: 0x000002d15410fe50
Event: 18.816 Thread 0x000002d17ee49f10 Thread exited: 0x000002d17ee49f10
Event: 18.816 Thread 0x000002d17ee494a0 Thread exited: 0x000002d17ee494a0
Event: 18.816 Thread 0x000002d15400a0b0 Thread exited: 0x000002d15400a0b0
Event: 18.816 Thread 0x000002d17ee3e830 Thread exited: 0x000002d17ee3e830
Event: 18.825 Thread 0x000002d17ee47a00 Thread exited: 0x000002d17ee47a00
Event: 18.825 Thread 0x000002d17ee3b3b0 Thread exited: 0x000002d17ee3b3b0
Event: 22.183 Thread 0x000002d17e58cf50 Thread added: 0x000002d15468b1f0
Event: 22.220 Thread 0x000002d17ee3cdf0 Thread added: 0x000002d17ee3b3b0
Event: 22.348 Thread 0x000002d17ee3cdf0 Thread added: 0x000002d17ee3c0d0


Dynamic libraries:
0x00007ff6d47b0000 - 0x00007ff6d47be000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ffe80050000 - 0x00007ffe80248000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffe7fb30000 - 0x00007ffe7fbf2000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffe7d7c0000 - 0x00007ffe7dab6000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffe7dce0000 - 0x00007ffe7dde0000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffe5cbf0000 - 0x00007ffe5cc0e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffe5e7d0000 - 0x00007ffe5e7e8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ffe7e080000 - 0x00007ffe7e21d000 	C:\WINDOWS\System32\USER32.dll
0x00007ffe65d80000 - 0x00007ffe6601a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5794_none_60bcd33171f2783c\COMCTL32.dll
0x00007ffe7dde0000 - 0x00007ffe7de02000 	C:\WINDOWS\System32\win32u.dll
0x00007ffe7f170000 - 0x00007ffe7f20e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffe7f810000 - 0x00007ffe7f83b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffe7db70000 - 0x00007ffe7dc8a000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffe7d720000 - 0x00007ffe7d7bd000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe7fe10000 - 0x00007ffe7fe3f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffe777e0000 - 0x00007ffe777ec000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffe4f5f0000 - 0x00007ffe4f67d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007ffdd4700000 - 0x00007ffdd5490000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ffe7f760000 - 0x00007ffe7f80f000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffe7f0d0000 - 0x00007ffe7f16f000 	C:\WINDOWS\System32\sechost.dll
0x00007ffe7f8a0000 - 0x00007ffe7f9c3000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffe7d6f0000 - 0x00007ffe7d717000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffe7f2c0000 - 0x00007ffe7f32b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffe7cc80000 - 0x00007ffe7cccb000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffe6d7c0000 - 0x00007ffe6d7e7000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffe74cf0000 - 0x00007ffe74cfa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffe7caf0000 - 0x00007ffe7cb02000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffe7b4f0000 - 0x00007ffe7b502000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffe6d7a0000 - 0x00007ffe6d7aa000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ffe74aa0000 - 0x00007ffe74ca1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffe56040000 - 0x00007ffe56074000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffe7dff0000 - 0x00007ffe7e072000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffe5cab0000 - 0x00007ffe5cabf000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ffe5cbd0000 - 0x00007ffe5cbef000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ffe7e350000 - 0x00007ffe7eabe000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffe7b710000 - 0x00007ffe7beb3000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffe7f330000 - 0x00007ffe7f683000 	C:\WINDOWS\System32\combase.dll
0x00007ffe7d120000 - 0x00007ffe7d14b000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ffe7f690000 - 0x00007ffe7f75d000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffe7f210000 - 0x00007ffe7f2bd000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffe7fac0000 - 0x00007ffe7fb15000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffe7d620000 - 0x00007ffe7d645000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffe5ca90000 - 0x00007ffe5caa8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ffe5bbc0000 - 0x00007ffe5bbd0000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ffe76610000 - 0x00007ffe7671a000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffe7ce80000 - 0x00007ffe7ceec000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffe5bb10000 - 0x00007ffe5bb26000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ffe50100000 - 0x00007ffe50110000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ffe4a510000 - 0x00007ffe4a555000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ffe7e220000 - 0x00007ffe7e34b000 	C:\WINDOWS\System32\ole32.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5794_none_60bcd33171f2783c;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\********************************\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\********************************\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-a9d20c496d97431fa1a083e1f1779fa7-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\********************************\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-11
PATH=C:\app\tshep\product\21c\dbhomeXE\bin;C:\app\tshep\product\18.0.0\dbhomeXE\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files (x86)\dotnet\;C:\Program Files\Java\jdk-11\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\xampp\php;C:\Program Files\Java\glassfish4\bin;C:\Users\<USER>\Downloads\apache-maven-3.9.9\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.12;C:\Program Files (x86)\cloudflared\;C:\Program Files\Redis\;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\anaconda3;C:\Users\<USER>\anaconda3\Library\mingw-w64\bin;C:\Users\<USER>\anaconda3\Library\usr\bin;C:\Users\<USER>\anaconda3\Library\bin;C:\Users\<USER>\anaconda3\Scripts;C:\Users\<USER>\anaconda3\bin;C:\Users\<USER>\anaconda3\condabin;C:\app\tshep\product\21c\dbhomeXE\bin;C:\app\tshep\product\18.0.0\dbhomeXE\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\dotnet;C:\Program Files (x86)\dotnet;C:\Program Files\Java\jdk-11\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs;C:\ProgramData\chocolatey\bin;C:\xampp\php;C:\Program Files\Java\glassfish4\bin;C:\Users\<USER>\Downloads\apache-maven-3.9.9\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.12;C:\Program Files (x86)\cloudflared;C:\Program Files\Redis;C:\Users\<USER>\scoop\shims;C:\app\tshep\product\21c\dbhomeXE\bin;C:\app\tshep\product\18.0.0\dbhomeXE\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\dotnet;C:\Program Files (x86)\dotnet;C:\Program Files\Java\jdk-11\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs;C:\ProgramData\chocolatey\bin;C:\xampp\php;C:\Program Files\Java\glassfish4\bin;C:\Users\<USER>\Downloads\apache-maven-3.9.9\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.12;C:\Program Files (x86)\cloudflared;C:\Program Files\Redis;C:\Users\<USER>\Downloads\ffmpeg-2025-03-24-git-cbbc927a67-full_build\ffmpeg-2025-03-24-git-cbbc927a67-full_build\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\Downloads\ffmpeg-2025-03-24-git-cbbc927a67-full_build\ffmpeg-2025-03-24-git-cbbc927a67-full_build\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools
USERNAME=tshep
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 140 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5794)
OS uptime: 0 days 0:39 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 140 stepping 1 microcode 0x9a, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ibt, cet_ss, avx512_ifma
Processor Information for the first 8 processors :
  Max Mhz: 2419, Current Mhz: 2419, Mhz Limit: 2419

Memory: 4k page, system-wide physical 7975M (1373M free)
TotalPageFile size 11429M (AvailPageFile size 0M)
current process WorkingSet (physical memory assigned to process): 118M, peak: 118M
current process commit charge ("private bytes"): 224M, peak: 224M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
