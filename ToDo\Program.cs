using System.Text.Json.Serialization;

var builder = WebApplication.CreateBuilder(args);

// Add services
builder.Services.AddSingleton<TodoService>();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseDeveloperExceptionPage();
}

// Todo endpoints
var todos = app.MapGroup("/todos");

// GET /todos - Get all todos with optional status filter
todos.MapGet("/", (TodoService service, string? status) =>
{
    if (string.IsNullOrEmpty(status))
    {
        return Results.Ok(service.GetAllTodos());
    }

    if (Enum.TryParse<TodoStatus>(status, true, out var statusEnum))
    {
        return Results.Ok(service.GetTodosByStatus(statusEnum));
    }

    return Results.BadRequest("Invalid status. Use 'Pending' or 'Completed'.");
});

// GET /todos/{id} - Get todo by id
todos.MapGet("/{id:int}", (TodoService service, int id) =>
{
    var todo = service.GetTodoById(id);
    return todo is not null ? Results.Ok(todo) : Results.NotFound();
});

// POST /todos - Create new todo
todos.MapPost("/", (TodoService service, CreateTodoRequest request) =>
{
    if (string.IsNullOrWhiteSpace(request.Title))
    {
        return Results.BadRequest("Title is required.");
    }

    var todo = service.CreateTodo(request.Title, request.Description ?? "");
    return Results.Created($"/todos/{todo.Id}", todo);
});

// PUT /todos/{id} - Update existing todo
todos.MapPut("/{id:int}", (TodoService service, int id, UpdateTodoRequest request) =>
{
    var existingTodo = service.GetTodoById(id);
    if (existingTodo is null)
    {
        return Results.NotFound();
    }

    var updatedTodo = service.UpdateTodo(id, request.Title, request.Description, request.Status);
    return updatedTodo is not null ? Results.Ok(updatedTodo) : Results.NotFound();
});

// DELETE /todos/{id} - Delete todo
todos.MapDelete("/{id:int}", (TodoService service, int id) =>
{
    var deleted = service.DeleteTodo(id);
    return deleted ? Results.NoContent() : Results.NotFound();
});

// GET /todos/stats - Get completion statistics
todos.MapGet("/stats", (TodoService service) =>
{
    var stats = service.GetCompletionStats();
    return Results.Ok(stats);
});

app.Run();

// Models
public class Todo
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TodoStatus Status { get; set; } = TodoStatus.Pending;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; }
}

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum TodoStatus
{
    Pending,
    Completed
}

// Request/Response models
public record CreateTodoRequest(string Title, string? Description);
public record UpdateTodoRequest(string? Title, string? Description, TodoStatus? Status);
public record CompletionStats(int TotalTasks, int CompletedTasks, double CompletionPercentage);

// Service
public class TodoService
{
    private readonly List<Todo> _todos = new();
    private int _nextId = 1;

    public IEnumerable<Todo> GetAllTodos() => _todos;

    public IEnumerable<Todo> GetTodosByStatus(TodoStatus status) =>
        _todos.Where(t => t.Status == status);

    public Todo? GetTodoById(int id) => _todos.FirstOrDefault(t => t.Id == id);

    public Todo CreateTodo(string title, string description)
    {
        var todo = new Todo
        {
            Id = _nextId++,
            Title = title,
            Description = description,
            Status = TodoStatus.Pending,
            CreatedAt = DateTime.UtcNow
        };

        _todos.Add(todo);
        return todo;
    }

    public Todo? UpdateTodo(int id, string? title, string? description, TodoStatus? status)
    {
        var todo = GetTodoById(id);
        if (todo is null) return null;

        if (!string.IsNullOrWhiteSpace(title))
            todo.Title = title;

        if (description is not null)
            todo.Description = description;

        if (status.HasValue)
            todo.Status = status.Value;

        todo.UpdatedAt = DateTime.UtcNow;
        return todo;
    }

    public bool DeleteTodo(int id)
    {
        var todo = GetTodoById(id);
        if (todo is null) return false;

        _todos.Remove(todo);
        return true;
    }

    public CompletionStats GetCompletionStats()
    {
        var totalTasks = _todos.Count;
        var completedTasks = _todos.Count(t => t.Status == TodoStatus.Completed);
        var completionPercentage = totalTasks == 0 ? 0.0 : (double)completedTasks / totalTasks * 100;

        return new CompletionStats(totalTasks, completedTasks, Math.Round(completionPercentage, 2));
    }
}