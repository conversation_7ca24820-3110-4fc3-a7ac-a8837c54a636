/*
 * Todo REST API - A minimal web API built with ASP.NET Core
 *
 * This application demonstrates key computer science concepts:
 * - RESTful API design (Representational State Transfer)
 * - HTTP protocol usage (GET, POST, PUT, DELETE methods)
 * - JSON serialization/deserialization
 * - Dependency Injection pattern
 * - CRUD operations (Create, Read, Update, Delete)
 * - Data validation and error handling
 * - In-memory data storage using collections
 */

using System.Text.Json.Serialization;

// Create a WebApplication builder - this is the factory pattern in action
// The builder pattern allows us to configure the application step by step
var builder = WebApplication.CreateBuilder(args);

// DEPENDENCY INJECTION: Register services in the DI container
// AddSingleton means only one instance of TodoService will be created for the entire application lifetime
// This is the Singleton design pattern - ensures we have one shared data store
builder.Services.AddSingleton<TodoService>();

// Build the application - this creates the actual web application instance
var app = builder.Build();

// MIDDLEWARE PIPELINE: Configure how HTTP requests are processed
// Middleware follows the Chain of Responsibility pattern
// Each middleware can process the request and pass it to the next one
if (app.Environment.IsDevelopment())
{
    // This middleware shows detailed error pages during development
    // It's only active in development environment for security reasons
    app.UseDeveloperExceptionPage();
}

// ROUTE GROUPING: Create a group of related endpoints
// This is an organizational pattern that keeps related routes together
// All routes in this group will start with "/todos"
var todos = app.MapGroup("/todos");

// ==================== API ENDPOINTS ====================
// Each endpoint follows REST conventions:
// - GET for reading data (safe, idempotent)
// - POST for creating new resources (not idempotent)
// - PUT for updating existing resources (idempotent)
// - DELETE for removing resources (idempotent)

// ENDPOINT 1: GET /todos - Retrieve all todos with optional filtering
// Query parameters: ?status=pending or ?status=completed
// HTTP Method: GET (safe operation - doesn't modify data)
todos.MapGet("/", (TodoService service, string? status) =>
{
    // Check if no filter is provided - return all todos
    if (string.IsNullOrEmpty(status))
    {
        // Results.Ok() returns HTTP 200 status with JSON body
        return Results.Ok(service.GetAllTodos());
    }

    // Try to parse the status string into our enum
    // Enum.TryParse is safer than Enum.Parse because it doesn't throw exceptions
    // The 'true' parameter makes it case-insensitive
    if (Enum.TryParse<TodoStatus>(status, true, out var statusEnum))
    {
        // Filter todos by the requested status
        return Results.Ok(service.GetTodosByStatus(statusEnum));
    }

    // Return HTTP 400 Bad Request for invalid status values
    // This follows the principle of failing fast with clear error messages
    return Results.BadRequest("Invalid status. Use 'Pending' or 'Completed'.");
});

// ENDPOINT 2: GET /todos/{id} - Retrieve a specific todo by ID
// Route constraint: {id:int} ensures the parameter is an integer
// HTTP Method: GET (safe operation)
todos.MapGet("/{id:int}", (TodoService service, int id) =>
{
    // Attempt to find the todo by ID
    var todo = service.GetTodoById(id);

    // Use ternary operator for concise conditional logic
    // 'is not null' is C# pattern matching - more readable than '!= null'
    // Returns HTTP 200 with todo data, or HTTP 404 if not found
    return todo is not null ? Results.Ok(todo) : Results.NotFound();
});

// ENDPOINT 3: POST /todos - Create a new todo
// HTTP Method: POST (not idempotent - creates new resources)
// Request body is automatically deserialized from JSON to CreateTodoRequest object
todos.MapPost("/", (TodoService service, CreateTodoRequest request) =>
{
    // INPUT VALIDATION: Check if required fields are provided
    // string.IsNullOrWhiteSpace checks for null, empty string, or only whitespace
    if (string.IsNullOrWhiteSpace(request.Title))
    {
        // Return HTTP 400 Bad Request for validation errors
        // This follows the principle of early validation
        return Results.BadRequest("Title is required.");
    }

    // Create the todo using the service layer
    // The null-coalescing operator (??) provides a default value if Description is null
    var todo = service.CreateTodo(request.Title, request.Description ?? "");

    // Return HTTP 201 Created with Location header pointing to the new resource
    // This follows REST conventions for resource creation
    return Results.Created($"/todos/{todo.Id}", todo);
});

// ENDPOINT 4: PUT /todos/{id} - Update an existing todo
// HTTP Method: PUT (idempotent - same request produces same result)
// Supports partial updates - only provided fields are updated
todos.MapPut("/{id:int}", (TodoService service, int id, UpdateTodoRequest request) =>
{
    // DEFENSIVE PROGRAMMING: Check if the resource exists before attempting update
    var existingTodo = service.GetTodoById(id);
    if (existingTodo is null)
    {
        // Return HTTP 404 if the todo doesn't exist
        return Results.NotFound();
    }

    // Attempt to update the todo
    var updatedTodo = service.UpdateTodo(id, request.Title, request.Description, request.Status);

    // Double-check that the update was successful
    // This handles edge cases where the todo might be deleted between checks
    return updatedTodo is not null ? Results.Ok(updatedTodo) : Results.NotFound();
});

// ENDPOINT 5: DELETE /todos/{id} - Delete a todo
// HTTP Method: DELETE (idempotent - multiple deletes have same effect)
todos.MapDelete("/{id:int}", (TodoService service, int id) =>
{
    // Attempt to delete the todo
    var deleted = service.DeleteTodo(id);

    // Return HTTP 204 No Content for successful deletion
    // Return HTTP 404 Not Found if the todo doesn't exist
    // No Content is appropriate because there's no meaningful data to return after deletion
    return deleted ? Results.NoContent() : Results.NotFound();
});

// ENDPOINT 6: GET /todos/stats - Get completion statistics
// This endpoint demonstrates computed/derived data
// HTTP Method: GET (safe operation)
todos.MapGet("/stats", (TodoService service) =>
{
    // Calculate and return completion statistics
    var stats = service.GetCompletionStats();
    return Results.Ok(stats);
});

// Start the web server and begin listening for HTTP requests
// This is a blocking call that keeps the application running
app.Run();

// ==================== DATA MODELS ====================
// These classes define the structure of our data and API contracts

/// <summary>
/// Todo Entity - Represents a single todo item in our system
/// This is our main domain model that encapsulates all the data for a todo
/// </summary>
public class Todo
{
    /// <summary>
    /// Unique identifier for the todo
    /// Primary key - used for lookups, updates, and deletions
    /// Auto-generated by the service when creating new todos
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// The title/name of the todo (required field)
    /// This is the main description of what needs to be done
    /// Cannot be null or empty - validated at the API level
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Optional detailed description of the todo
    /// Provides additional context or instructions
    /// Can be empty string but not null (due to default value)
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Current status of the todo (Pending or Completed)
    /// Uses enum for type safety and to prevent invalid values
    /// Defaults to Pending when a new todo is created
    /// </summary>
    public TodoStatus Status { get; set; } = TodoStatus.Pending;

    /// <summary>
    /// Timestamp when the todo was created
    /// Automatically set to current UTC time when object is instantiated
    /// Immutable after creation - provides audit trail
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Timestamp when the todo was last updated (nullable)
    /// Null when todo has never been updated
    /// Set to current UTC time whenever the todo is modified
    /// Provides audit trail for changes
    /// </summary>
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// Enumeration representing the possible states of a todo
/// JsonConverter attribute ensures this serializes as string instead of number
/// This makes the API more user-friendly and self-documenting
/// </summary>
[JsonConverter(typeof(JsonStringEnumConverter))]
public enum TodoStatus
{
    /// <summary>
    /// Todo is not yet completed - default state
    /// </summary>
    Pending,

    /// <summary>
    /// Todo has been finished/completed
    /// </summary>
    Completed
}

// ==================== REQUEST/RESPONSE MODELS ====================
// These models define the API contract - what data clients send and receive
// Using records provides immutability and value equality by default

/// <summary>
/// Request model for creating a new todo
/// Record type provides immutability and automatic equality comparison
/// Only includes fields that can be set during creation
/// </summary>
/// <param name="Title">Required title for the todo</param>
/// <param name="Description">Optional description (can be null)</param>
public record CreateTodoRequest(string Title, string? Description);

/// <summary>
/// Request model for updating an existing todo
/// All fields are optional to support partial updates
/// Null values mean "don't change this field"
/// </summary>
/// <param name="Title">New title (optional)</param>
/// <param name="Description">New description (optional)</param>
/// <param name="Status">New status (optional)</param>
public record UpdateTodoRequest(string? Title, string? Description, TodoStatus? Status);

/// <summary>
/// Response model for completion statistics
/// Provides computed data about todo completion rates
/// Demonstrates derived/calculated data in APIs
/// </summary>
/// <param name="TotalTasks">Total number of todos in the system</param>
/// <param name="CompletedTasks">Number of todos marked as completed</param>
/// <param name="CompletionPercentage">Percentage of completed todos (0-100, rounded to 2 decimal places)</param>
public record CompletionStats(int TotalTasks, int CompletedTasks, double CompletionPercentage);

// ==================== BUSINESS LOGIC SERVICE ====================
// This class implements the Repository pattern and contains all business logic
// It acts as a data access layer and encapsulates todo operations

/// <summary>
/// TodoService - Business logic layer for todo operations
///
/// This class demonstrates several important computer science concepts:
/// - Repository Pattern: Abstracts data access logic
/// - Encapsulation: Hides internal data structure from external code
/// - Single Responsibility: Only handles todo-related operations
/// - In-memory storage: Uses collections for simple data persistence
///
/// In a real application, this would typically interface with a database
/// </summary>
public class TodoService
{
    // ==================== PRIVATE FIELDS ====================

    /// <summary>
    /// In-memory storage for todos using a List collection
    /// List&lt;T&gt; provides:
    /// - Dynamic sizing (grows/shrinks as needed)
    /// - Index-based access O(1)
    /// - Sequential search O(n)
    /// - Insertion/deletion operations
    ///
    /// 'readonly' means the reference cannot be changed after initialization
    /// but the contents of the list can still be modified
    /// </summary>
    private readonly List<Todo> _todos = new();

    /// <summary>
    /// Counter for generating unique IDs
    /// Starts at 1 and increments for each new todo
    /// In a real database, this would be handled by auto-increment primary keys
    ///
    /// Note: This is not thread-safe in a multi-threaded environment
    /// For production, you'd use atomic operations or database-generated IDs
    /// </summary>
    private int _nextId = 1;

    // ==================== PUBLIC METHODS (API) ====================

    /// <summary>
    /// Retrieves all todos in the system
    ///
    /// Returns IEnumerable&lt;Todo&gt; instead of List&lt;Todo&gt; to:
    /// - Hide implementation details (encapsulation)
    /// - Prevent external modification of internal collection
    /// - Provide a more flexible interface
    ///
    /// Time Complexity: O(1) - just returns reference to collection
    /// Space Complexity: O(1) - no additional memory allocation
    /// </summary>
    /// <returns>All todos in the system</returns>
    public IEnumerable<Todo> GetAllTodos() => _todos;

    /// <summary>
    /// Filters todos by their completion status
    ///
    /// Uses LINQ (Language Integrated Query) for filtering:
    /// - Where() method applies a predicate function to each element
    /// - Lambda expression (t => t.Status == status) defines the filter condition
    /// - Returns IEnumerable for deferred execution (lazy evaluation)
    ///
    /// Time Complexity: O(n) - must examine each todo
    /// Space Complexity: O(k) where k is the number of matching todos
    /// </summary>
    /// <param name="status">The status to filter by (Pending or Completed)</param>
    /// <returns>Todos matching the specified status</returns>
    public IEnumerable<Todo> GetTodosByStatus(TodoStatus status) =>
        _todos.Where(t => t.Status == status);

    /// <summary>
    /// Finds a specific todo by its unique ID
    ///
    /// Uses LINQ FirstOrDefault() method:
    /// - Searches through the collection sequentially
    /// - Returns the first matching element or null if not found
    /// - More efficient than Where().First() because it stops at first match
    ///
    /// Time Complexity: O(n) - worst case searches entire list
    /// Space Complexity: O(1) - returns reference to existing object
    ///
    /// Note: In a real application with a database, this would be O(log n) or O(1)
    /// with proper indexing on the ID field
    /// </summary>
    /// <param name="id">The unique identifier of the todo</param>
    /// <returns>The todo with matching ID, or null if not found</returns>
    public Todo? GetTodoById(int id) => _todos.FirstOrDefault(t => t.Id == id);

    /// <summary>
    /// Creates a new todo and adds it to the collection
    ///
    /// This method demonstrates:
    /// - Object initialization with object initializer syntax
    /// - Automatic ID generation using post-increment operator
    /// - Setting default values and timestamps
    /// - Adding to collection and returning the created object
    ///
    /// Time Complexity: O(1) - adding to end of list is constant time
    /// Space Complexity: O(1) - creates one new object
    /// </summary>
    /// <param name="title">The title of the todo (required)</param>
    /// <param name="description">The description of the todo (optional)</param>
    /// <returns>The newly created todo with assigned ID and timestamps</returns>
    public Todo CreateTodo(string title, string description)
    {
        // Create new todo object using object initializer syntax
        // This is more readable than setting properties one by one
        var todo = new Todo
        {
            Id = _nextId++,              // Post-increment: use current value, then increment
            Title = title,               // Set from parameter
            Description = description,   // Set from parameter
            Status = TodoStatus.Pending, // Default status for new todos
            CreatedAt = DateTime.UtcNow  // Current timestamp in UTC
            // UpdatedAt remains null since this is a new todo
        };

        // Add to our in-memory collection
        _todos.Add(todo);

        // Return the created todo (useful for getting the assigned ID)
        return todo;
    }

    /// <summary>
    /// Updates an existing todo with new values
    ///
    /// This method demonstrates:
    /// - Partial updates (only change provided fields)
    /// - Null checking and defensive programming
    /// - Conditional assignment based on parameter values
    /// - Timestamp tracking for audit purposes
    ///
    /// Time Complexity: O(n) - due to GetTodoById lookup
    /// Space Complexity: O(1) - modifies existing object in place
    /// </summary>
    /// <param name="id">ID of the todo to update</param>
    /// <param name="title">New title (null means don't change)</param>
    /// <param name="description">New description (null means don't change)</param>
    /// <param name="status">New status (null means don't change)</param>
    /// <returns>The updated todo, or null if todo with given ID doesn't exist</returns>
    public Todo? UpdateTodo(int id, string? title, string? description, TodoStatus? status)
    {
        // First, find the todo to update
        var todo = GetTodoById(id);

        // Defensive programming: check if todo exists
        if (todo is null) return null;

        // Conditional updates: only change fields that are provided
        // This allows for partial updates where client only sends changed fields

        if (!string.IsNullOrWhiteSpace(title))
            todo.Title = title;

        // Note: We check 'is not null' instead of IsNullOrWhiteSpace for description
        // because we want to allow setting description to empty string
        if (description is not null)
            todo.Description = description;

        // HasValue checks if the nullable enum has a value
        if (status.HasValue)
            todo.Status = status.Value; // .Value extracts the actual enum value

        // Update the timestamp to track when this todo was last modified
        todo.UpdatedAt = DateTime.UtcNow;

        return todo;
    }

    /// <summary>
    /// Deletes a todo from the collection
    ///
    /// This method demonstrates:
    /// - Lookup before deletion (defensive programming)
    /// - Boolean return to indicate success/failure
    /// - Collection removal operations
    ///
    /// Time Complexity: O(n) - due to GetTodoById lookup + List.Remove()
    /// Space Complexity: O(1) - no additional memory allocation
    /// </summary>
    /// <param name="id">ID of the todo to delete</param>
    /// <returns>True if todo was found and deleted, false if todo doesn't exist</returns>
    public bool DeleteTodo(int id)
    {
        // Find the todo first
        var todo = GetTodoById(id);

        // Check if todo exists
        if (todo is null) return false;

        // Remove from collection
        // List.Remove() returns bool indicating if item was found and removed
        _todos.Remove(todo);

        return true;
    }

    /// <summary>
    /// Calculates completion statistics for all todos
    ///
    /// This method demonstrates:
    /// - Aggregate operations on collections
    /// - Mathematical calculations (percentage)
    /// - Handling edge cases (division by zero)
    /// - LINQ Count() with predicates
    /// - Floating-point arithmetic and rounding
    ///
    /// Time Complexity: O(n) - must examine each todo to count completed ones
    /// Space Complexity: O(1) - only stores a few calculated values
    /// </summary>
    /// <returns>Statistics about todo completion rates</returns>
    public CompletionStats GetCompletionStats()
    {
        // Count total todos - this is O(1) for List<T>.Count
        var totalTasks = _todos.Count;

        // Count completed todos using LINQ - this is O(n)
        // The lambda expression (t => t.Status == TodoStatus.Completed) is a predicate
        var completedTasks = _todos.Count(t => t.Status == TodoStatus.Completed);

        // Calculate percentage, handling division by zero edge case
        // Ternary operator provides concise conditional logic
        // Cast to double to ensure floating-point division (not integer division)
        var completionPercentage = totalTasks == 0 ? 0.0 : (double)completedTasks / totalTasks * 100;

        // Return new record with calculated values
        // Math.Round() ensures we don't have excessive decimal places
        return new CompletionStats(totalTasks, completedTasks, Math.Round(completionPercentage, 2));
    }
}