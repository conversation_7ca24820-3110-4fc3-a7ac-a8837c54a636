# Computer Science Concepts in Todo REST API

This document explains the key computer science concepts demonstrated in this Todo REST API project, written for computer science graduates.

## 🏗️ Software Architecture Patterns

### 1. **Layered Architecture**
The application follows a layered architecture pattern:
- **Presentation Layer**: HTTP endpoints and request/response handling
- **Business Logic Layer**: TodoService class containing domain logic
- **Data Access Layer**: In-memory List<Todo> storage (simulates database)

**Benefits**: Separation of concerns, maintainability, testability

### 2. **Repository Pattern**
The `TodoService` class implements the Repository pattern:
- Abstracts data access logic from business logic
- Provides a consistent interface for data operations
- Makes the code more testable by allowing mock implementations

### 3. **Dependency Injection (DI)**
```csharp
builder.Services.AddSingleton<TodoService>();
```
- **Inversion of Control**: Dependencies are provided rather than created
- **Loose Coupling**: Classes don't directly instantiate their dependencies
- **Testability**: Easy to inject mock objects for testing

## 🌐 Web Development Concepts

### 1. **RESTful API Design**
Follows REST (Representational State Transfer) principles:
- **Resources**: Todos are resources identified by URLs
- **HTTP Methods**: GET (read), POST (create), PUT (update), DELETE (delete)
- **Stateless**: Each request contains all necessary information
- **Uniform Interface**: Consistent URL patterns and HTTP status codes

### 2. **HTTP Protocol Understanding**
- **Status Codes**: 200 OK, 201 Created, 204 No Content, 400 Bad Request, 404 Not Found
- **Headers**: Content-Type, Location header for created resources
- **Methods**: Idempotent (GET, PUT, DELETE) vs Non-idempotent (POST)

### 3. **JSON Serialization/Deserialization**
- Automatic conversion between C# objects and JSON
- Custom serialization with `JsonStringEnumConverter` for enums
- Content negotiation and media types

## 📊 Data Structures and Algorithms

### 1. **List<T> Collection**
```csharp
private readonly List<Todo> _todos = new();
```
**Properties**:
- Dynamic array implementation
- **Access**: O(1) by index
- **Search**: O(n) linear search
- **Insertion**: O(1) at end, O(n) at arbitrary position
- **Deletion**: O(n) due to shifting elements

### 2. **LINQ (Language Integrated Query)**
```csharp
_todos.Where(t => t.Status == status)
_todos.FirstOrDefault(t => t.Id == id)
_todos.Count(t => t.Status == TodoStatus.Completed)
```
**Concepts**:
- **Functional Programming**: Higher-order functions, lambda expressions
- **Lazy Evaluation**: Deferred execution until enumeration
- **Predicate Functions**: Boolean-returning functions for filtering

### 3. **Time Complexity Analysis**
- **GetAllTodos()**: O(1) - returns reference
- **GetTodosByStatus()**: O(n) - filters all items
- **GetTodoById()**: O(n) - linear search
- **CreateTodo()**: O(1) - append to list
- **UpdateTodo()**: O(n) - due to lookup
- **DeleteTodo()**: O(n) - lookup + removal
- **GetCompletionStats()**: O(n) - counts all items

## 🎯 Object-Oriented Programming

### 1. **Encapsulation**
```csharp
private readonly List<Todo> _todos = new();
public IEnumerable<Todo> GetAllTodos() => _todos;
```
- **Data Hiding**: Private fields protect internal state
- **Interface Abstraction**: Public methods provide controlled access
- **Immutable Returns**: IEnumerable prevents external modification

### 2. **Polymorphism**
- **Interface Segregation**: IEnumerable<T> vs List<T>
- **Method Overloading**: Different parameter combinations
- **Nullable Types**: Todo? indicates possible null return

### 3. **Inheritance and Composition**
- **Record Types**: Inherit from Object with value semantics
- **Enum Types**: Inherit from System.Enum
- **Composition**: TodoService contains List<Todo>

## 🔧 Language Features (C#)

### 1. **Modern C# Syntax**
```csharp
// Pattern matching
return todo is not null ? Results.Ok(todo) : Results.NotFound();

// Null-coalescing operator
var todo = service.CreateTodo(request.Title, request.Description ?? "");

// Object initializers
var todo = new Todo { Id = _nextId++, Title = title };

// Expression-bodied members
public IEnumerable<Todo> GetAllTodos() => _todos;
```

### 2. **Type Safety**
- **Nullable Reference Types**: string? indicates nullable
- **Enum Types**: TodoStatus prevents invalid values
- **Generic Types**: List<Todo>, IEnumerable<Todo>
- **Record Types**: Immutable data containers

### 3. **Memory Management**
- **Garbage Collection**: Automatic memory management
- **Reference Types**: Objects stored on heap
- **Value Types**: Enums and primitives on stack
- **String Interning**: Efficient string storage

## 🔄 Functional Programming Concepts

### 1. **Lambda Expressions**
```csharp
t => t.Status == status           // Predicate function
t => t.Status == TodoStatus.Completed  // Boolean expression
```
- **Anonymous Functions**: Functions without explicit names
- **Closures**: Capture variables from enclosing scope
- **Expression Trees**: Compile to efficient code

### 2. **Immutability**
```csharp
public record CreateTodoRequest(string Title, string? Description);
```
- **Record Types**: Immutable by default
- **Value Equality**: Structural comparison instead of reference
- **Functional Style**: Reduces side effects and bugs

### 3. **Higher-Order Functions**
- **Where()**: Takes predicate function as parameter
- **Count()**: Takes predicate function as parameter
- **FirstOrDefault()**: Takes predicate function as parameter

## 🛡️ Error Handling and Validation

### 1. **Defensive Programming**
```csharp
if (todo is null) return null;
if (string.IsNullOrWhiteSpace(request.Title)) return Results.BadRequest();
```
- **Null Checks**: Prevent null reference exceptions
- **Input Validation**: Validate data before processing
- **Early Returns**: Fail fast with clear error messages

### 2. **Exception Safety**
- **Enum.TryParse()**: Safe parsing without exceptions
- **Nullable Types**: Explicit handling of missing values
- **Validation at Boundaries**: Check inputs at API level

### 3. **HTTP Error Handling**
- **Status Codes**: Meaningful error responses
- **Error Messages**: Clear, actionable feedback
- **Consistent Format**: Uniform error response structure

## 🔄 Concurrency Considerations

### 1. **Thread Safety Issues**
```csharp
private int _nextId = 1;  // Not thread-safe!
```
- **Race Conditions**: Multiple threads accessing shared state
- **Atomic Operations**: Need for thread-safe ID generation
- **Collection Safety**: List<T> is not thread-safe

### 2. **Potential Solutions**
- **Interlocked.Increment()**: Atomic counter operations
- **ConcurrentCollection<T>**: Thread-safe collections
- **Locking**: Synchronization primitives
- **Database IDs**: Let database handle ID generation

## 📈 Performance Considerations

### 1. **Big O Analysis**
- Most operations are O(n) due to linear search
- Could be improved with Dictionary<int, Todo> for O(1) lookups
- Trade-off between memory usage and access speed

### 2. **Memory Efficiency**
- **Object Pooling**: Reuse objects to reduce GC pressure
- **String Interning**: Efficient string storage
- **Lazy Loading**: Load data only when needed

### 3. **Scalability Concerns**
- In-memory storage doesn't scale beyond single machine
- No persistence across application restarts
- Limited by available RAM

## 🧪 Testing Concepts

### 1. **Unit Testing**
- Test individual methods in isolation
- Mock dependencies for focused testing
- Arrange-Act-Assert pattern

### 2. **Integration Testing**
- Test complete request/response cycles
- Verify HTTP status codes and JSON responses
- End-to-end workflow validation

### 3. **Test Coverage**
- All public methods tested
- Error conditions and edge cases
- Input validation scenarios

## 🚀 Production Considerations

### 1. **Database Integration**
- Replace List<Todo> with Entity Framework or similar ORM
- Implement proper indexing for performance
- Add connection pooling and retry logic

### 2. **Security**
- Add authentication and authorization
- Input sanitization and validation
- Rate limiting and CORS policies

### 3. **Monitoring and Logging**
- Structured logging for debugging
- Performance metrics and monitoring
- Health checks and diagnostics

This Todo API serves as an excellent learning project that demonstrates fundamental computer science concepts in a practical, real-world context.
