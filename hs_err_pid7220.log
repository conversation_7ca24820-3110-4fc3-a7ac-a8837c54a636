#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#
#  , pid=7220
#
# ]V  [jvm.dll+0x3c3d08]
V  [jvm.dll+0x470db5]
V  [jvm.dll+0x47c0fd]
C  0x000002049d46c96b

The last pc belongs to native method entry point (kind = native) (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.lang.Class.getDeclaredConstructors0(Z)[Ljava/lang/reflect/Constructor;+0 java.base@21.0.7
j  java.lang.Class.privateGetDeclaredConstructors(Z)[Ljava/lang/reflect/Constructor;+52 java.base@21.0.7
j  java.lang.Class.getConstructors()[Ljava/lang/reflect/Constructor;+20 java.base@21.0.7
j  org.apache.felix.scr.impl.inject.internal.ComponentConstructorImpl.<init>(Lorg/apache/felix/scr/impl/metadata/ComponentMetadata;Ljava/lang/Class;Lorg/apache/felix/scr/impl/logger/ComponentLogger;)V+212
j  org.apache.felix.scr.impl.inject.internal.ComponentMethodsImpl.initComponentMethods(Lorg/apache/felix/scr/impl/metadata/ComponentMetadata;Ljava/lang/Class;Lorg/apache/felix/scr/impl/logger/ComponentLogger;)V+306
j  org.apache.felix.scr.impl.manager.AbstractComponentManager.initDependencyManagers(Lorg/apache/felix/scr/impl/manager/ComponentContextImpl;)V+76
j  org.apache.felix.scr.impl.manager.AbstractComponentManager.collectDependencies(Lorg/apache/felix/scr/impl/manager/ComponentContextImpl;)Z+2
j  org.apache.felix.scr.impl.manager.SingleComponentManager.getServiceInternal(Lorg/osgi/framework/ServiceRegistration;)Z+25
j  org.apache.felix.scr.impl.manager.AbstractComponentManager.activateInternal()V+438
j  org.apache.felix.scr.impl.manager.AbstractComponentManager.enableInternal()V+179
j  org.apache.felix.scr.impl.manager.AbstractComponentManager.enable(Z)Lorg/osgi/util/promise/Promise;+12
j  org.apache.felix.scr.impl.manager.ConfigurableComponentHolder.enableComponents(Z)Lorg/osgi/util/promise/Promise;+275
j  org.apache.felix.scr.impl.BundleComponentActivator.initialEnable()V+107
j  org.apache.felix.scr.impl.Activator.loadComponents(Lorg/osgi/framework/Bundle;)V+392
j  org.apache.felix.scr.impl.Activator.access$200(Lorg/apache/felix/scr/impl/Activator;Lorg/osgi/framework/Bundle;)V+2
j  org.apache.felix.scr.impl.Activator$ScrExtension.start()V+72
j  org.apache.felix.scr.impl.AbstractExtender.createExtension(Lorg/osgi/framework/Bundle;)V+71
j  org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(Lorg/osgi/framework/Bundle;Lorg/osgi/framework/BundleEvent;Lorg/osgi/framework/Bundle;)V+115
j  org.apache.felix.scr.impl.AbstractExtender.addingBundle(Lorg/osgi/framework/Bundle;Lorg/osgi/framework/BundleEvent;)Lorg/osgi/framework/Bundle;+4
j  org.apache.felix.scr.impl.AbstractExtender.addingBundle(Lorg/osgi/framework/Bundle;Lorg/osgi/framework/BundleEvent;)Ljava/lang/Object;+3
j  org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(Lorg/osgi/framework/Bundle;Lorg/osgi/framework/BundleEvent;)Ljava/lang/Object;+9
j  org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+9
j  org.osgi.util.tracker.AbstractTracked.trackAdding(Ljava/lang/Object;Ljava/lang/Object;)V+8
j  org.osgi.util.tracker.AbstractTracked.track(Ljava/lang/Object;Ljava/lang/Object;)V+83
j  org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(Lorg/osgi/framework/BundleEvent;)V+35
J 1833 c1 org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V (577 bytes) @ 0x000002049625b744 [0x000002049625a280+0x00000000000014c4]
j  org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(Ljava/util/Set;Lorg/eclipse/osgi/framework/eventmgr/EventDispatcher;ILjava/lang/Object;)V+48
j  org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ILjava/lang/Object;)V+67
j  org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(Lorg/osgi/framework/BundleEvent;)V+547
j  org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(Lorg/osgi/framework/BundleEvent;)V+8
j  org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(ILorg/osgi/framework/Bundle;Lorg/osgi/framework/Bundle;)V+15
j  org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(Lorg/eclipse/osgi/container/ModuleContainerAdaptor$ModuleEvent;Lorg/eclipse/osgi/container/Module;Lorg/eclipse/osgi/container/Module;)V+29
j  org.eclipse.osgi.container.Module.publishEvent(Lorg/eclipse/osgi/container/ModuleContainerAdaptor$ModuleEvent;)V+13
j  org.eclipse.osgi.container.Module.start([Lorg/eclipse/osgi/container/Module$StartOptions;)V+588
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run()V+83
j  org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(Ljava/lang/Runnable;)V+1
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ILjava/util/List;Z)V+193
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ILjava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V+4
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(Lorg/eclipse/osgi/container/Module;I[Lorg/osgi/framework/FrameworkListener;)V+358
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(Lorg/eclipse/osgi/container/Module;[Lorg/osgi/framework/FrameworkListener;ILjava/lang/Integer;)V+32
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V+15
j  org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(Ljava/util/Set;Lorg/eclipse/osgi/framework/eventmgr/EventDispatcher;ILjava/lang/Object;)V+48
j  org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run()V+26
v  ~StubRoutines::call_stub 0x000002049d4610e7
native method entry point (kind = native)  [0x000002049d46c540, 0x000002049d46ce88]  2376 bytes
[MachCode]
  0x000002049d46c540: 488b 4b08 | 0fb7 492e | 584c 8d74 | ccf8 6800 | 0000 0068 | 0000 0000 | 5055 488b | ec41 5568 
  0x000002049d46c560: 0000 0000 | 4c8b 6b08 | 4d8d 6d38 | 5348 8b53 | 0848 8b52 | 0848 8b52 | 1848 8b52 | 7048 8b12 
  0x000002049d46c580: 5248 8b53 | 1048 85d2 | 0f84 0700 | 0000 4881 | c208 0100 | 0052 488b | 5308 488b | 5208 488b 
  0x000002049d46c5a0: 5210 5249 | 8bc6 482b | c548 c1e8 | 0350 6800 | 0000 0068 | 0000 0000 | 4889 2424 | 41c6 8771 
  0x000002049d46c5c0: 0400 0001 | 488b 4310 | 4885 c074 | 208b 88cc | 0000 0083 | c102 8988 | cc00 0000 | 2388 e000 
  0x000002049d46c5e0: 0000 0f84 | f207 0000 | e9cf 0000 | 0048 8b43 | 1848 85c0 | 0f85 b000 | 0000 e805 | 0000 00e9 
  0x000002049d46c600: 9900 0000 | 488b d348 | 8d44 2408 | 4c89 6dc0 | 498b cfc5 | f877 4989 | afa8 0300 | 0049 8987 
  0x000002049d46c620: 9803 0000 | 4883 ec20 | 40f6 c40f | 0f84 1900 | 0000 4883 | ec08 48b8 | 9051 add4 | fd7f 0000 
  0x000002049d46c640: ffd0 4883 | c408 e90c | 0000 0048 | b890 51ad | d4fd 7f00 | 00ff d048 | 83c4 2049 | c787 9803 
  0x000002049d46c660: 0000 0000 | 0000 49c7 | 87a8 0300 | 0000 0000 | 0049 c787 | a003 0000 | 0000 0000 | c5f8 7749 
  0x000002049d46c680: 837f 0800 | 0f84 0500 | 0000 e971 | 48ff ff4c | 8b6d c04c | 8b75 c84e | 8d74 f500 | c348 8b43 
  0x000002049d46c6a0: 1848 85c0 | 0f84 1200 | 0000 8b48 | 0883 c102 | 8948 0823 | 481c 0f84 | 1e07 0000 | 493b a7e0 
  0x000002049d46c6c0: 0400 000f | 8748 0000 | 0089 8424 | 00f0 ffff | 8984 2400 | e0ff ff89 | 8424 00d0 | ffff 8984 
  0x000002049d46c6e0: 2400 c0ff | ff89 8424 | 00b0 ffff | 8984 2400 | a0ff ff89 | 8424 0090 | ffff 8984 | 2400 80ff 
  0x000002049d46c700: ff49 3ba7 | d804 0000 | 7607 4989 | a7e0 0400 | 0041 c687 | 7104 0000 | 0049 ba3d | 5b39 d5fd 
  0x000002049d46c720: 7f00 0041 | 803a 000f | 843e 0000 | 0048 8b55 | e849 8bcf | 4883 ec20 | 40f6 c40f | 0f84 1900 
  0x000002049d46c740: 0000 4883 | ec08 48b8 | 504b e3d4 | fd7f 0000 | ffd0 4883 | c408 e90c | 0000 0048 | b850 4be3 
  0x000002049d46c760: d4fd 7f00 | 00ff d048 | 83c4 2048 | 8b5d e84c | 8b5b 0845 | 0fb7 5b2e | 41c1 e303 | 492b e348 
  0x000002049d46c780: 83ec 2048 | 83e4 f04c | 8b5b 604d | 85db 0f85 | ab00 0000 | e805 0000 | 00e9 9900 | 0000 488b 
  0x000002049d46c7a0: d348 8d44 | 2408 4c89 | 6dc0 498b | cfc5 f877 | 4989 afa8 | 0300 0049 | 8987 9803 | 0000 4883 
  0x000002049d46c7c0: ec20 40f6 | c40f 0f84 | 1900 0000 | 4883 ec08 | 48b8 a078 | add4 fd7f | 0000 ffd0 | 4883 c408 
  0x000002049d46c7e0: e90c 0000 | 0048 b8a0 | 78ad d4fd | 7f00 00ff | d048 83c4 | 2049 c787 | 9803 0000 | 0000 0000 
  0x000002049d46c800: 49c7 87a8 | 0300 0000 | 0000 0049 | c787 a003 | 0000 0000 | 0000 c5f8 | 7749 837f | 0800 0f84 
  0x000002049d46c820: 0500 0000 | e9d7 46ff | ff4c 8b6d | c04c 8b75 | c84e 8d74 | f500 c348 | 8b5d e84c | 8b5b 6041 
  0x000002049d46c840: ffd3 488b | 5de8 4889 | 4518 448b | 5b28 41f6 | c308 0f84 | 1b00 0000 | 4c8b 5b08 | 4d8b 5b08 
  0x000002049d46c860: 4d8b 5b18 | 4d8b 5b70 | 4d8b 1b4c | 895d 1048 | 8d55 1048 | 8b43 5849 | baa0 a0e3 | d4fd 7f00 
  0x000002049d46c880: 0049 3bc2 | 0f85 ab00 | 0000 e805 | 0000 00e9 | 9900 0000 | 488b d348 | 8d44 2408 | 4c89 6dc0 
  0x000002049d46c8a0: 498b cfc5 | f877 4989 | afa8 0300 | 0049 8987 | 9803 0000 | 4883 ec20 | 40f6 c40f | 0f84 1900 
  0x000002049d46c8c0: 0000 4883 | ec08 48b8 | a078 add4 | fd7f 0000 | ffd0 4883 | c408 e90c | 0000 0048 | b8a0 78ad 
  0x000002049d46c8e0: d4fd 7f00 | 00ff d048 | 83c4 2049 | c787 9803 | 0000 0000 | 0000 49c7 | 87a8 0300 | 0000 0000 
  0x000002049d46c900: 0049 c787 | a003 0000 | 0000 0000 | c5f8 7749 | 837f 0800 | 0f84 0500 | 0000 e9e1 | 45ff ff4c 
  0x000002049d46c920: 8b6d c04c | 8b75 c84e | 8d74 f500 | c348 8b5d | e848 8b43 | 5849 8d8f | b803 0000 | c5f8 7749 
  0x000002049d46c940: 89af a803 | 0000 49ba | 3cc9 469d | 0402 0000 | 4d89 97a0 | 0300 0049 | 89a7 9803 | 0000 41c7 
  0x000002049d46c960: 8744 0400 | 0004 0000 | 00ff d0c5 | f877 4883 | ec10 c5fb | 1104 2448 | 83ec 1048 | 8904 2448 
  0x000002049d46c980: c744 2408 | 0000 0000 | 41c7 8744 | 0400 0005 | 0000 00f0 | 8344 24c0 | 0049 3baf | 4804 0000 
  0x000002049d46c9a0: 0f87 0e00 | 0000 4183 | bf40 0400 | 0000 0f84 | 2000 0000 | 498b cf4c | 8be4 4883 | ec20 4883 
  0x000002049d46c9c0: e4f0 48b8 | 00d1 aed4 | fd7f 0000 | ffd0 498b | e44d 33e4 | 41c7 8744 | 0400 0008 | 0000 0049 
  0x000002049d46c9e0: c787 9803 | 0000 0000 | 0000 49c7 | 87a8 0300 | 0000 0000 | 0049 c787 | a003 0000 | 0000 0000 
  0x000002049d46ca00: c5f8 774d | 8b9f 2804 | 0000 41c7 | 8300 0100 | 0000 0000 | 0049 bbda | a046 9d04 | 0200 004c 
  0x000002049d46ca20: 3b5d 180f | 854b 0000 | 0048 8b04 | 2448 83c4 | 1048 85c0 | 0f84 2500 | 0000 a803 | 0f85 0800 
  0x000002049d46ca40: 0000 488b | 00e9 1500 | 0000 a801 | 0f85 0900 | 0000 488b | 40fe e904 | 0000 0048 | 8b40 ff48 
  0x000002049d46ca60: 8945 1048 | 83ec 1048 | 8904 2448 | c744 2408 | 0000 0000 | 4183 bfc0 | 0400 0002 | 0f85 bf00 
  0x000002049d46ca80: 0000 4881 | ec80 0000 | 0048 8944 | 2478 4889 | 4c24 7048 | 8954 2468 | 4889 5c24 | 6048 896c 
  0x000002049d46caa0: 2450 4889 | 7424 4848 | 897c 2440 | 4c89 4424 | 384c 894c | 2430 4c89 | 5424 284c | 895c 2420 
  0x000002049d46cac0: 4c89 6424 | 184c 896c | 2410 4c89 | 7424 084c | 893c 244c | 8be4 4883 | ec20 4883 | e4f0 48b8 
  0x000002049d46cae0: 3083 e3d4 | fd7f 0000 | ffd0 498b | e44c 8b3c | 244c 8b74 | 2408 4c8b | 6c24 104c | 8b64 2418 
  0x000002049d46cb00: 4c8b 5c24 | 204c 8b54 | 2428 4c8b | 4c24 304c | 8b44 2438 | 488b 7c24 | 4048 8b74 | 2448 488b 
  0x000002049d46cb20: 6c24 5048 | 8b5c 2460 | 488b 5424 | 6848 8b4c | 2470 488b | 4424 7848 | 81c4 8000 | 0000 4d33 
  0x000002049d46cb40: e448 8b5d | e84c 8b6b | 084d 8d6d | 3849 837f | 0800 0f84 | bb00 0000 | e805 0000 | 00e9 9600 
  0x000002049d46cb60: 0000 488d | 4424 084c | 896d c049 | 8bcf c5f8 | 7749 89af | a803 0000 | 4989 8798 | 0300 0048 
  0x000002049d46cb80: 83ec 2040 | f6c4 0f0f | 8419 0000 | 0048 83ec | 0848 b840 | 96ad d4fd | 7f00 00ff | d048 83c4 
  0x000002049d46cba0: 08e9 0c00 | 0000 48b8 | 4096 add4 | fd7f 0000 | ffd0 4883 | c420 49c7 | 8798 0300 | 0000 0000 
  0x000002049d46cbc0: 0049 c787 | a803 0000 | 0000 0000 | 49c7 87a0 | 0300 0000 | 0000 00c5 | f877 4983 | 7f08 000f 
  0x000002049d46cbe0: 8405 0000 | 00e9 1643 | ffff 4c8b | 6dc0 4c8b | 75c8 4e8d | 74f5 00c3 | 48b9 907f | 0fd5 fd7f 
  0x000002049d46cc00: 0000 4883 | e4f0 48b8 | e044 d2d4 | fd7f 0000 | ffd0 f444 | 8b5b 2841 | f6c3 200f | 8444 0100 
  0x000002049d46cc20: 0048 8d55 | a84c 8b5a | 084d 85db | 0f85 bb00 | 0000 e805 | 0000 00e9 | 9600 0000 | 488d 4424 
  0x000002049d46cc40: 084c 896d | c049 8bcf | c5f8 7749 | 89af a803 | 0000 4989 | 8798 0300 | 0048 83ec | 2040 f6c4 
  0x000002049d46cc60: 0f0f 8419 | 0000 0048 | 83ec 0848 | b870 95ad | d4fd 7f00 | 00ff d048 | 83c4 08e9 | 0c00 0000 
  0x000002049d46cc80: 48b8 7095 | add4 fd7f | 0000 ffd0 | 4883 c420 | 49c7 8798 | 0300 0000 | 0000 0049 | c787 a803 
  0x000002049d46cca0: 0000 0000 | 0000 49c7 | 87a0 0300 | 0000 0000 | 00c5 f877 | 4983 7f08 | 000f 8405 | 0000 00e9 
  0x000002049d46ccc0: 3c42 ffff | 4c8b 6dc0 | 4c8b 75c8 | 4e8d 74f5 | 00c3 48b9 | 907f 0fd5 | fd7f 0000 | 4883 e4f0 
  0x000002049d46cce0: 48b8 e044 | d2d4 fd7f | 0000 ffd0 | f44c 896d | c048 8d02 | 4c8b 4a08 | 48c7 4208 | 0000 0000 
  0x000002049d46cd00: 4c8b 004d | 85c0 0f84 | 0b00 0000 | f04d 0fb1 | 010f 850c | 0000 0049 | ff8f 4805 | 0000 e93e 
  0x000002049d46cd20: 0000 004c | 894a 0848 | 8bca 4883 | ec20 40f6 | c40f 0f84 | 1900 0000 | 4883 ec08 | 48b8 9068 
  0x000002049d46cd40: add4 fd7f | 0000 ffd0 | 4883 c408 | e90c 0000 | 0048 b890 | 68ad d4fd | 7f00 00ff | d048 83c4 
  0x000002049d46cd60: 204c 8b6d | c049 ba3d | 5b39 d5fd | 7f00 0041 | 803a 000f | 843e 0000 | 0048 8b55 | e849 8bcf 
  0x000002049d46cd80: 4883 ec20 | 40f6 c40f | 0f84 1900 | 0000 4883 | ec08 48b8 | 504b e3d4 | fd7f 0000 | ffd0 4883 
  0x000002049d46cda0: c408 e90c | 0000 0048 | b850 4be3 | d4fd 7f00 | 00ff d048 | 83c4 2048 | 8b04 2448 | 83c4 10c5 
  0x000002049d46cdc0: fb10 0424 | 4883 c410 | 4c8b 5d18 | 41ff d34c | 8b5d f8c9 | 5f49 8be3 | ffe7 ba00 | 0000 00e8 
  0x000002049d46cde0: 0500 0000 | e996 0000 | 0048 8d44 | 2408 4c89 | 6dc0 498b | cfc5 f877 | 4989 afa8 | 0300 0049 
  0x000002049d46ce00: 8987 9803 | 0000 4883 | ec20 40f6 | c40f 0f84 | 1900 0000 | 4883 ec08 | 48b8 505c | add4 fd7f 
  0x000002049d46ce20: 0000 ffd0 | 4883 c408 | e90c 0000 | 0048 b850 | 5cad d4fd | 7f00 00ff | d048 83c4 | 2049 c787 
  0x000002049d46ce40: 9803 0000 | 0000 0000 | 49c7 87a8 | 0300 0000 | 0000 0049 | c787 a003 | 0000 0000 | 0000 c5f8 
  0x000002049d46ce60: 7749 837f | 0800 0f84 | 0500 0000 | e98f 40ff | ff4c 8b6d | c04c 8b75 | c84e 8d74 | f500 c348 
  0x000002049d46ce80: 8b5d e8e9 | 34f8 ffff 
[/MachCode]

Compiled method (c1) 2001 1833   !   3       org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent (577 bytes)
 total in heap  [0x0000020496259810,0x0000020496263110] = 39168
 relocation     [0x0000020496259970,0x000002049625a280] = 2320
 main code      [0x000002049625a280,0x000002049625f9e8] = 22376
 stub code      [0x000002049625f9e8,0x000002049625fd68] = 896
 oops           [0x000002049625fd68,0x000002049625fdb0] = 72
 metadata       [0x000002049625fdb0,0x000002049625fec8] = 280
 scopes data    [0x000002049625fec8,0x0000020496260d00] = 3640
 scopes pcs     [0x0000020496260d00,0x0000020496261b80] = 3712
 dependencies   [0x0000020496261b80,0x0000020496261bd8] = 88
 handler table  [0x0000020496261bd8,0x0000020496262fa0] = 5064
 nul chk table  [0x0000020496262fa0,0x0000020496263110] = 368

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl'
  # this:     rdx:rdx   = 'org/eclipse/osgi/internal/framework/BundleContextImpl'
  # parm0:    r8:r8     = 'java/lang/Object'
  # parm1:    r9:r9     = 'java/lang/Object'
  # parm2:    rdi       = int
  # parm3:    rsi:rsi   = 'java/lang/Object'
  #           [sp+0x1a0]  (sp of caller)
  0x000002049625a280: 448b 5208 | 49bb 0000 | 00a7 0402 | 0000 4d03 | d34c 3bd0 

  0x000002049625a294: ;   {runtime_call ic_miss_stub}
  0x000002049625a294: 0f85 e643 | 2507 660f | 1f44 0000 
[Verified Entry Point]
  0x000002049625a2a0: 8984 2400 | 80ff ff55 | 4881 ec90 | 0100 0090 | 4181 7f20 | 0100 0000 

  0x000002049625a2b8: ;   {runtime_call StubRoutines (final stubs)}
  0x000002049625a2b8: 7405 e841 | 3524 0748 | 8994 2480 | 0000 0089 | bc24 8800 | 0000 4889 | b424 9800 

  0x000002049625a2d4: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625a2d4: 0000 48bb | b855 7aea | 0402 0000 | 8b83 cc00 | 0000 83c0 | 0289 83cc | 0000 0025 | fe07 0000 
  0x000002049625a2f4: 85c0 0f84 | 4d4a 0000 | 4c89 8c24 | 9000 0000 

  0x000002049625a304: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625a304: 488b da48 | b8b8 557a | ea04 0200 

  0x000002049625a310: ;   {metadata('org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625a310: 0049 ba80 | d30b a804 | 0200 004c | 8990 2001 | 0000 4883 | 8028 0100 | 0001 488b | da48 8bd3 
  0x000002049625a330: 0f1f 8000 

  0x000002049625a334: ;   {optimized virtual_call}
  0x000002049625a334: 0000 00e8 

  0x000002049625a338: ; ImmutableOopMap {[128]=Oop [144]=Oop [152]=Oop }
                      ;*invokevirtual setContextFinder {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@1 (line 960)
  0x000002049625a338: a4e9 ffff 

  0x000002049625a33c: ;   {other}
  0x000002049625a33c: 0f1f 8400 | 2c0b 0000 | 4889 8424 | b000 0000 | 488b 9424 | 8000 0000 

  0x000002049625a354: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625a354: 488b f248 | bfb8 557a | ea04 0200 

  0x000002049625a360: ;   {metadata('org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625a360: 0049 ba80 | d30b a804 | 0200 004c | 8997 5801 | 0000 4883 | 8760 0100 | 0001 0fbe | 720c 83e6 
  0x000002049625a380: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625a380: 0185 f648 | beb8 557a | ea04 0200 | 0048 c7c7 | 8001 0000 | 7507 48c7 | c790 0100 | 0048 8b0c 
  0x000002049625a3a0: 3e48 8d49 | 0148 890c | 3e0f 852a | 0100 008b | 7210 483b | 0648 8bfe 

  0x000002049625a3b8: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625a3b8: 48b9 b855 | 7aea 0402 | 0000 8b7f | 0849 ba00 | 0000 a704 | 0200 0049 | 03fa 483b | b9b0 0100 
  0x000002049625a3d8: 0075 0d48 | 8381 b801 | 0000 01e9 | 6000 0000 | 483b b9c0 | 0100 0075 | 0d48 8381 | c801 0000 
  0x000002049625a3f8: 01e9 4a00 | 0000 4883 | b9b0 0100 | 0000 7517 | 4889 b9b0 | 0100 0048 | c781 b801 | 0000 0100 
  0x000002049625a418: 0000 e929 | 0000 0048 | 83b9 c001 | 0000 0075 | 1748 89b9 | c001 0000 | 48c7 81c8 | 0100 0001 
  0x000002049625a438: 0000 00e9 | 0800 0000 | 4883 81a0 | 0100 0001 

  0x000002049625a448: ;   {metadata(method data for {method} {0x00000204ea5e07b0} 'getBundleId' '()J' in 'org/eclipse/osgi/internal/framework/EquinoxBundle')}
  0x000002049625a448: 48bf 58dd | 79ea 0402 | 0000 8b8f | cc00 0000 | 83c1 0289 | 8fcc 0000 | 0081 e1fe | ff1f 0085 
  0x000002049625a468: c90f 8400 | 4900 008b | 7610 483b | 0648 8bfe 

  0x000002049625a478: ;   {metadata(method data for {method} {0x00000204ea5e07b0} 'getBundleId' '()J' in 'org/eclipse/osgi/internal/framework/EquinoxBundle')}
  0x000002049625a478: 48b9 58dd | 79ea 0402 | 0000 4883 | 8110 0100 | 0001 8b76 | 1848 3b06 

  0x000002049625a490: ;   {metadata(method data for {method} {0x00000204ea5e07b0} 'getBundleId' '()J' in 'org/eclipse/osgi/internal/framework/EquinoxBundle')}
  0x000002049625a490: 488b fe48 | b958 dd79 | ea04 0200 | 0048 8381 | 4801 0000 | 0148 8b7e | 1048 83ff 

  0x000002049625a4ac: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625a4ac: 0048 beb8 | 557a ea04 | 0200 0048 | c7c7 d801 | 0000 7507 | 48c7 c7e8 | 0100 0048 | 8b0c 3e48 
  0x000002049625a4cc: 8d49 0148 | 890c 3e0f | 859c 3600 | 008b bc24 | 8800 0000 

  0x000002049625a4e0: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625a4e0: 48be b855 | 7aea 0402 | 0000 48c7 | c100 0200 | 0083 ff01 | 488b d948 | c7c1 1002 | 0000 480f 
  0x000002049625a500: 45cb 83ff | 0248 8bd9 | 48c7 c120 | 0200 0048 | 0f45 cb83 | ff03 488b | d948 c7c1 | 3002 0000 
  0x000002049625a520: 480f 45cb | 83ff 0448 | 8bd9 48c7 | c140 0200 | 0048 0f45 | cb48 8b1c | 0e49 c7c2 | 0100 0000 
  0x000002049625a540: 4903 da48 | 891c 0e83 | ff01 0f84 | 2000 0000 | 83ff 020f | 8417 0000 | 0083 ff03 | 0f84 ff23 
  0x000002049625a560: 0000 83ff | 040f 84fe | 1100 00e9 | 0a38 0000 | 4c8b 8c24 | 9000 0000 | 4d85 c975 

  0x000002049625a57c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625a57c: 1648 b9b8 | 557a ea04 | 0200 0080 | 8951 0200 | 0001 e9ea 

  0x000002049625a590: ;   {metadata('org/osgi/framework/BundleListener')}
  0x000002049625a590: 0000 0049 | b8b8 3608 | a804 0200 | 0041 8b49 | 0849 ba00 | 0000 a704 | 0200 0049 | 03ca 4c3b 
  0x000002049625a5b0: 4120 0f84 | 1b00 0000 | 493b c80f | 8412 0000 | 0051 4150 

  0x000002049625a5c4: ;   {runtime_call slow_subtype_check Runtime1 stub}
  0x000002049625a5c4: e837 7330 | 0759 5985 | c90f 848e 

  0x000002049625a5d0: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625a5d0: 0000 0048 | b9b8 557a | ea04 0200 | 0045 8b41 | 0849 ba00 | 0000 a704 | 0200 004d | 03c2 4c3b 
  0x000002049625a5f0: 8168 0200 | 0075 0d48 | 8381 7002 | 0000 01e9 | 7900 0000 | 4c3b 8178 | 0200 0075 | 0d48 8381 
  0x000002049625a610: 8002 0000 | 01e9 6300 | 0000 4883 | b968 0200 | 0000 7517 | 4c89 8168 | 0200 0048 | c781 7002 
  0x000002049625a630: 0000 0100 | 0000 e942 | 0000 0048 | 83b9 7802 | 0000 0075 | 174c 8981 | 7802 0000 | 48c7 8180 
  0x000002049625a650: 0200 0001 | 0000 00e9 | 2100 0000 | e91c 0000 

  0x000002049625a660: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625a660: 0048 b9b8 | 557a ea04 | 0200 0048 | 83a9 5802 | 0000 01e9 | 2247 0000 | e900 0000 | 0049 8bd9 
  0x000002049625a680: 8b72 180f | be76 1185 

  0x000002049625a688: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625a688: f648 beb8 | 557a ea04 | 0200 0048 | c7c1 9002 | 0000 7407 | 48c7 c1a0 | 0200 004c | 8b04 0e4d 
  0x000002049625a6a8: 8d40 014c | 8904 0e0f | 84ef 0e00 | 0066 6690 

  0x000002049625a6b8: ;   {no_reloc}
  0x000002049625a6b8: e9fa 4600 | 0000 0000 | 0000 498b | 87b8 0100 | 0048 8d78 | 1849 3bbf | c801 0000 | 0f87 e746 
  0x000002049625a6d8: 0000 4989 | bfb8 0100 | 0048 c700 | 0100 0000 | 488b ca49 | ba00 0000 | a704 0200 | 0049 2bca 
  0x000002049625a6f8: 8948 0848 | 33c9 8948 | 0c48 33c9 | 4889 4810 | 4889 8424 | a000 0000 

  0x000002049625a710: ; implicit exception: dispatches to 0x000002049625edce
  0x000002049625a710: 483b 0348 

  0x000002049625a714: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625a714: 8bd3 48be | b855 7aea | 0402 0000 | 4883 86b0 | 0200 0001 | 8b53 0849 | ba00 0000 | a704 0200 
  0x000002049625a734: 0049 03d2 | 488b 5270 | 488b 1248 | 3b02 488b 

  0x000002049625a744: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625a744: f248 bfb8 | 557a ea04 | 0200 0048 | 8387 e802 

  0x000002049625a754: ;   {metadata(method data for {method} {0x00000204a7465ac0} 'getName' '()Ljava/lang/String;' in 'java/lang/Class')}
  0x000002049625a754: 0000 0148 | be00 7208 | ea04 0200 | 008b becc | 0000 0083 | c702 89be | cc00 0000 | 81e7 feff 
  0x000002049625a774: 1f00 85ff | 0f84 5a46 | 0000 8b72 | 2c48 85f6 

  0x000002049625a784: ;   {metadata(method data for {method} {0x00000204a7465ac0} 'getName' '()Ljava/lang/String;' in 'java/lang/Class')}
  0x000002049625a784: 48bf 0072 | 08ea 0402 | 0000 48c7 | c110 0100 | 0074 0748 | c7c1 2001 | 0000 4c8b | 2c0f 4d8d 
  0x000002049625a7a4: 6d01 4c89 | 2c0f 0f84 | 1d00 0000 

  0x000002049625a7b0: ;   {metadata(method data for {method} {0x00000204a7465ac0} 'getName' '()Ljava/lang/String;' in 'java/lang/Class')}
  0x000002049625a7b0: 48ba 0072 | 08ea 0402 | 0000 ff82 | 3001 0000 | 4889 9c24 | a800 0000 | e932 0000 | 0048 899c 
  0x000002049625a7d0: 24a8 0000 | 004c 8bc2 

  0x000002049625a7d8: ;   {metadata(method data for {method} {0x00000204a7465ac0} 'getName' '()Ljava/lang/String;' in 'java/lang/Class')}
  0x000002049625a7d8: 49b9 0072 | 08ea 0402 | 0000 4983 | 8148 0100 | 0001 0f1f 

  0x000002049625a7ec: ;   {optimized virtual_call}
  0x000002049625a7ec: 4400 00e8 

  0x000002049625a7f0: ; ImmutableOopMap {[128]=Oop [152]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual initClassName {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.Class::getName@14
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@81 (line 970)
  0x000002049625a7f0: 8c43 2507 

  0x000002049625a7f4: ;   {other}
  0x000002049625a7f4: 0f1f 8400 | e40f 0001 

  0x000002049625a7fc: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625a7fc: 488b f048 | bab8 557a | ea04 0200 | 0048 8382 | 2003 0000 

  0x000002049625a810: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625a810: 0148 ba08 | a00e ea04 | 0200 008b | bacc 0000 | 0083 c702 | 89ba cc00 | 0000 81e7 | feff 1f00 
  0x000002049625a830: 85ff 0f84 | c145 0000 

  0x000002049625a838: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625a838: 4885 f648 | ba08 a00e | ea04 0200 | 0048 c7c7 | 1001 0000 | 7507 48c7 | c720 0100 | 0048 8b1c 
  0x000002049625a858: 3a48 8d5b | 0148 891c | 3a0f 851f 

  0x000002049625a864: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625a864: 0000 0048 | ba08 a00e | ea04 0200 | 00ff 8230 

  0x000002049625a874: ;   {oop("null"{0x00000000c0004000})}
  0x000002049625a874: 0100 0048 | be00 4000 | c000 0000 | 00e9 b900 | 0000 483b | 0648 8bd6 

  0x000002049625a88c: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625a88c: 48bf 08a0 | 0eea 0402 | 0000 8b52 | 0849 ba00 | 0000 a704 | 0200 0049 | 03d2 483b | 9758 0100 
  0x000002049625a8ac: 0075 0d48 | 8387 6001 | 0000 01e9 | 6000 0000 | 483b 9768 | 0100 0075 | 0d48 8387 | 7001 0000 
  0x000002049625a8cc: 01e9 4a00 | 0000 4883 | bf58 0100 | 0000 7517 | 4889 9758 | 0100 0048 | c787 6001 | 0000 0100 
  0x000002049625a8ec: 0000 e929 | 0000 0048 | 83bf 6801 | 0000 0075 | 1748 8997 | 6801 0000 | 48c7 8770 | 0100 0001 
  0x000002049625a90c: 0000 00e9 | 0800 0000 | 4883 8748 | 0100 0001 | 488b d666 | 0f1f 4400 | 0048 b8ff | ffff ffff 
  0x000002049625a92c: ;   {virtual_call}
  0x000002049625a92c: ffff ffe8 

  0x000002049625a930: ; ImmutableOopMap {[128]=Oop [152]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual toString {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::valueOf@11
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@84 (line 970)
  0x000002049625a930: 4c47 2507 

  0x000002049625a934: ;   {other}
  0x000002049625a934: 0f1f 8400 | 2411 0002 | 488b f048 | 8b84 24a0 | 0000 004c 

  0x000002049625a948: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625a948: 8bc0 48bf | b855 7aea | 0402 0000 | 4883 8730 | 0300 0001 

  0x000002049625a95c: ;   {metadata(method data for {method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625a95c: 49b8 c8f6 | 2fea 0402 | 0000 418b | b8cc 0000 | 0083 c702 | 4189 b8cc | 0000 0081 | e7fe ff1f 
  0x000002049625a97c: 0085 ff0f | 849a 4400 | 004c 8bc0 

  0x000002049625a988: ;   {metadata(method data for {method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625a988: 48bf c8f6 | 2fea 0402 | 0000 4883 | 8710 0100 | 0001 4c8b | c648 8bd0 | 0f1f 8000 

  0x000002049625a9a4: ;   {optimized virtual_call}
  0x000002049625a9a4: 0000 00e8 

  0x000002049625a9a8: ; ImmutableOopMap {[128]=Oop [152]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::<init>@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@87 (line 970)
  0x000002049625a9a8: d441 2507 

  0x000002049625a9ac: ;   {other}
  0x000002049625a9ac: 0f1f 8400 | 9c11 0003 | 488b 8424 | a000 0000 

  0x000002049625a9bc: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625a9bc: 49b8 b855 | 7aea 0402 | 0000 4983 | 8040 0300 

  0x000002049625a9cc: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625a9cc: 0001 49b8 | 60b2 00ea | 0402 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x000002049625a9ec: ff1f 0085 | d20f 8449 | 4400 0048 | 8b84 24a0 

  0x000002049625a9fc: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625a9fc: 0000 0049 | b860 b200 | ea04 0200 | 0049 8380 | 1001 0000 

  0x000002049625aa10: ;   {oop("@"{0x00000000c0006bf0})}
  0x000002049625aa10: 0149 b8f0 | 6b00 c000 | 0000 0048 | 8b94 24a0 | 0000 000f 

  0x000002049625aa24: ;   {optimized virtual_call}
  0x000002049625aa24: 1f40 00e8 

  0x000002049625aa28: ; ImmutableOopMap {[128]=Oop [152]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokespecial append {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@93 (line 970)
  0x000002049625aa28: 5441 2507 

  0x000002049625aa2c: ;   {other}
  0x000002049625aa2c: 0f1f 8400 | 1c12 0004 

  0x000002049625aa34: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625aa34: 48ba b855 | 7aea 0402 | 0000 4883 | 8278 0300 | 0001 488b | 9424 a800 

  0x000002049625aa4c: ;   {static_call}
  0x000002049625aa4c: 0000 90e8 

  0x000002049625aa50: ; ImmutableOopMap {[128]=Oop [152]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokestatic identityHashCode {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@98 (line 971)
  0x000002049625aa50: 2c4b 2507 

  0x000002049625aa54: ;   {other}
  0x000002049625aa54: 0f1f 8400 | 4412 0005 

  0x000002049625aa5c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625aa5c: 48ba b855 | 7aea 0402 | 0000 4883 | 8288 0300 

  0x000002049625aa6c: ;   {metadata(method data for {method} {0x00000204a74126c8} 'toHexString' '(I)Ljava/lang/String;' in 'java/lang/Integer')}
  0x000002049625aa6c: 0001 48ba | a05c 2fea | 0402 0000 | 448b 82cc | 0000 0041 | 83c0 0244 | 8982 cc00 | 0000 4181 
  0x000002049625aa8c: e0fe ff1f | 0045 85c0 | 0f84 c743 

  0x000002049625aa98: ;   {metadata(method data for {method} {0x00000204a74126c8} 'toHexString' '(I)Ljava/lang/String;' in 'java/lang/Integer')}
  0x000002049625aa98: 0000 48ba | a05c 2fea | 0402 0000 | 4883 8210 | 0100 0001 | 488b d041 | b804 0000 

  0x000002049625aab4: ;   {static_call}
  0x000002049625aab4: 0066 90e8 

  0x000002049625aab8: ; ImmutableOopMap {[128]=Oop [152]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokestatic toUnsignedString0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.Integer::toHexString@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@101 (line 971)
  0x000002049625aab8: c44a 2507 

  0x000002049625aabc: ;   {other}
  0x000002049625aabc: 0f1f 8400 | ac12 0006 | 4c8b 8424 | a000 0000 

  0x000002049625aacc: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625aacc: 48ba b855 | 7aea 0402 | 0000 4883 | 8298 0300 

  0x000002049625aadc: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625aadc: 0001 49b8 | 60b2 00ea | 0402 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x000002049625aafc: ff1f 0085 | d20f 847b | 4300 004c | 8b84 24a0 

  0x000002049625ab0c: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625ab0c: 0000 0048 | ba60 b200 | ea04 0200 | 0048 8382 | 1001 0000 | 014c 8bc0 | 488b 9424 | a000 0000 
  0x000002049625ab2c: ;   {optimized virtual_call}
  0x000002049625ab2c: 6666 90e8 

  0x000002049625ab30: ; ImmutableOopMap {[128]=Oop [152]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokespecial append {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@104 (line 971)
  0x000002049625ab30: 4c40 2507 

  0x000002049625ab34: ;   {other}
  0x000002049625ab34: 0f1f 8400 | 2413 0007 | 488b 8424 | a000 0000 

  0x000002049625ab44: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625ab44: 48ba b855 | 7aea 0402 | 0000 4883 | 82d0 0300 

  0x000002049625ab54: ;   {metadata(method data for {method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625ab54: 0001 48ba | a8ba 00ea | 0402 0000 | 8bb2 cc00 | 0000 83c6 | 0289 b2cc | 0000 0081 | e6fe ff1f 
  0x000002049625ab74: 0085 f60f | 8426 4300 

  0x000002049625ab7c: ;   {metadata('java/lang/String')}
  0x000002049625ab7c: 0048 ba40 | e700 a704 | 0200 0049 | 8b87 b801 | 0000 488d | 7818 493b | bfc8 0100 | 000f 8725 
  0x000002049625ab9c: 4300 0049 | 89bf b801 | 0000 48c7 | 0001 0000 | 0048 8bca | 49ba 0000 | 00a7 0402 | 0000 492b 
  0x000002049625abbc: ca89 4808 | 4833 c989 | 480c 4833 | c948 8948 | 1048 8984 | 24b8 0000 | 004c 8bc0 

  0x000002049625abd8: ;   {metadata(method data for {method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625abd8: 49b9 a8ba | 00ea 0402 | 0000 4983 | 8110 0100 

  0x000002049625abe8: ;   {metadata(method data for {method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625abe8: 0001 49b8 | f0bb 00ea | 0402 0000 | 458b 88cc | 0000 0041 | 83c1 0245 | 8988 cc00 | 0000 4181 
  0x000002049625ac08: e1fe ff1f | 0045 85c9 | 0f84 bb42 | 0000 4c8b 

  0x000002049625ac18: ;   {metadata(method data for {method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625ac18: c049 b9f0 | bb00 ea04 | 0200 0049 | 8381 1001 | 0000 014c | 8b84 24a0 

  0x000002049625ac30: ;   {oop(nullptr)}
  0x000002049625ac30: 0000 0049 | b900 0000 | 0000 0000 | 0048 8bd0 | 0f1f 8000 

  0x000002049625ac44: ;   {optimized virtual_call}
  0x000002049625ac44: 0000 00e8 

  0x000002049625ac48: ; ImmutableOopMap {[128]=Oop [152]=Oop [168]=Oop [176]=Oop [184]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::<init>@3
                      ; - java.lang.StringBuilder::toString@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@107 (line 970)
  0x000002049625ac48: 343f 2507 

  0x000002049625ac4c: ;   {other}
  0x000002049625ac4c: 0f1f 8400 | 3c14 0008 | 488b 9424 | 8000 0000 | 8b5a 1848 | 899c 24c8 | 0000 0090 

  0x000002049625ac68: ;   {no_reloc}
  0x000002049625ac68: e994 4200 | 0000 0000 | 0000 498b | 87b8 0100 | 0048 8d78 | 1849 3bbf | c801 0000 | 0f87 8142 
  0x000002049625ac88: 0000 4989 | bfb8 0100 | 0048 c700 | 0100 0000 | 488b ca49 | ba00 0000 | a704 0200 | 0049 2bca 
  0x000002049625aca8: 8948 0848 | 33c9 8948 | 0c48 33c9 | 4889 4810 | 4889 8424 | c000 0000 

  0x000002049625acc0: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625acc0: 4c8b c048 | bab8 557a | ea04 0200 | 0048 8382 | 0804 0000 

  0x000002049625acd4: ;   {metadata(method data for {method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625acd4: 0149 b8c8 | f62f ea04 | 0200 0041 | 8b90 cc00 | 0000 83c2 | 0241 8990 | cc00 0000 | 81e2 feff 
  0x000002049625acf4: 1f00 85d2 | 0f84 1a42 | 0000 4c8b 

  0x000002049625ad00: ;   {metadata(method data for {method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625ad00: c048 bac8 | f62f ea04 | 0200 0048 | 8382 1001 

  0x000002049625ad10: ;   {oop("dispatchBundleEvent["{0x00000000eabca240})}
  0x000002049625ad10: 0000 0149 | b840 a2bc | ea00 0000 | 0048 8bd0 | 0f1f 8000 

  0x000002049625ad24: ;   {optimized virtual_call}
  0x000002049625ad24: 0000 00e8 

  0x000002049625ad28: ; ImmutableOopMap {[128]=Oop [152]=Oop [168]=Oop [176]=Oop [184]=Oop [192]=Oop [200]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::<init>@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@126 (line 972)
  0x000002049625ad28: 543e 2507 

  0x000002049625ad2c: ;   {other}
  0x000002049625ad2c: 0f1f 8400 | 1c15 0009 | 488b 9424 | 8000 0000 | 8b72 1048 | 8b84 24c0 

  0x000002049625ad44: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625ad44: 0000 0048 | bfb8 557a | ea04 0200 | 0048 8387 | 1804 0000 

  0x000002049625ad58: ;   {metadata(method data for {method} {0x00000204a745dad0} 'append' '(Ljava/lang/Object;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625ad58: 0148 bf68 | 9e0e ea04 | 0200 008b | 9fcc 0000 | 0083 c302 | 899f cc00 | 0000 81e3 | feff 1f00 
  0x000002049625ad78: 85db 0f84 | b941 0000 

  0x000002049625ad80: ;   {metadata(method data for {method} {0x00000204a745dad0} 'append' '(Ljava/lang/Object;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625ad80: 48bf 689e | 0eea 0402 | 0000 4883 | 8710 0100 

  0x000002049625ad90: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625ad90: 0001 48bf | 08a0 0eea | 0402 0000 | 8b9f cc00 | 0000 83c3 | 0289 9fcc | 0000 0081 | e3fe ff1f 
  0x000002049625adb0: 0085 db0f | 84a1 4100 | 0048 85f6 

  0x000002049625adbc: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625adbc: 48bf 08a0 | 0eea 0402 | 0000 48c7 | c310 0100 | 0075 0748 | c7c3 2001 | 0000 488b | 041f 488d 
  0x000002049625addc: 4001 4889 | 041f 0f85 | 1f00 0000 

  0x000002049625ade8: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625ade8: 48be 08a0 | 0eea 0402 | 0000 ff86 | 3001 0000 

  0x000002049625adf8: ;   {oop("null"{0x00000000c0004000})}
  0x000002049625adf8: 48be 0040 | 00c0 0000 | 0000 e9b0 | 0000 0048 | 3b06 488b 

  0x000002049625ae0c: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625ae0c: fe48 bb08 | a00e ea04 | 0200 008b | 7f08 49ba | 0000 00a7 | 0402 0000 | 4903 fa48 | 3bbb 5801 
  0x000002049625ae2c: 0000 750d | 4883 8360 | 0100 0001 | e960 0000 | 0048 3bbb | 6801 0000 | 750d 4883 | 8370 0100 
  0x000002049625ae4c: 0001 e94a | 0000 0048 | 83bb 5801 | 0000 0075 | 1748 89bb | 5801 0000 | 48c7 8360 | 0100 0001 
  0x000002049625ae6c: 0000 00e9 | 2900 0000 | 4883 bb68 | 0100 0000 | 7517 4889 | bb68 0100 | 0048 c783 | 7001 0000 
  0x000002049625ae8c: 0100 0000 | e908 0000 | 0048 8383 | 4801 0000 | 0148 8bd6 | 0f1f 8000 

  0x000002049625aea4: ;   {optimized virtual_call}
  0x000002049625aea4: 0000 00e8 

  0x000002049625aea8: ; ImmutableOopMap {[128]=Oop [152]=Oop [168]=Oop [176]=Oop [184]=Oop [192]=Oop [200]=Oop }
                      ;*invokevirtual toString {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::valueOf@11
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@133 (line 972)
  0x000002049625aea8: d43c 2507 

  0x000002049625aeac: ;   {other}
  0x000002049625aeac: 0f1f 8400 | 9c16 000a | 488b f048 | 8b84 24c0 

  0x000002049625aebc: ;   {metadata(method data for {method} {0x00000204a745dad0} 'append' '(Ljava/lang/Object;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625aebc: 0000 0049 | b868 9e0e | ea04 0200 | 0049 8380 | 2001 0000 

  0x000002049625aed0: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625aed0: 0149 b860 | b200 ea04 | 0200 0041 | 8b90 cc00 | 0000 83c2 | 0241 8990 | cc00 0000 | 81e2 feff 
  0x000002049625aef0: 1f00 85d2 | 0f84 8640 | 0000 488b | 8424 c000 

  0x000002049625af00: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625af00: 0000 49b8 | 60b2 00ea | 0402 0000 | 4983 8010 | 0100 0001 | 4c8b c648 | 8b94 24c0 

  0x000002049625af1c: ;   {optimized virtual_call}
  0x000002049625af1c: 0000 00e8 

  0x000002049625af20: ; ImmutableOopMap {[128]=Oop [152]=Oop [168]=Oop [176]=Oop [184]=Oop [192]=Oop [200]=Oop }
                      ;*invokespecial append {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::append@2
                      ; - java.lang.StringBuilder::append@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@133 (line 972)
  0x000002049625af20: 5c3c 2507 

  0x000002049625af24: ;   {other}
  0x000002049625af24: 0f1f 8400 | 1417 000b | 488b 8424 | c000 0000 

  0x000002049625af34: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625af34: 4c8b c048 | bab8 557a | ea04 0200 | 0048 8382 | 5004 0000 

  0x000002049625af48: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625af48: 0149 b860 | b200 ea04 | 0200 0041 | 8b90 cc00 | 0000 83c2 | 0241 8990 | cc00 0000 | 81e2 feff 
  0x000002049625af68: 1f00 85d2 | 0f84 2f40 | 0000 4c8b 

  0x000002049625af74: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625af74: c048 ba60 | b200 ea04 | 0200 0048 | 8382 1001 

  0x000002049625af84: ;   {oop("]("{0x00000000eabca280})}
  0x000002049625af84: 0000 0149 | b880 a2bc | ea00 0000 | 0048 8bd0 

  0x000002049625af94: ;   {optimized virtual_call}
  0x000002049625af94: 6666 90e8 

  0x000002049625af98: ; ImmutableOopMap {[128]=Oop [152]=Oop [168]=Oop [176]=Oop [184]=Oop [192]=Oop [200]=Oop }
                      ;*invokespecial append {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@139 (line 972)
  0x000002049625af98: e43b 2507 

  0x000002049625af9c: ;   {other}
  0x000002049625af9c: 0f1f 8400 | 8c17 000c | 488b 8424 | c000 0000 

  0x000002049625afac: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625afac: 49b8 b855 | 7aea 0402 | 0000 4983 | 8088 0400 

  0x000002049625afbc: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625afbc: 0001 49b8 | 60b2 00ea | 0402 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x000002049625afdc: ff1f 0085 | d20f 84db | 3f00 0048 | 8b84 24c0 

  0x000002049625afec: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625afec: 0000 0049 | b860 b200 | ea04 0200 | 0049 8380 | 1001 0000 | 014c 8b84 | 24b8 0000 | 0048 8b94 
  0x000002049625b00c: 24c0 0000 | 0066 0f1f 

  0x000002049625b014: ;   {optimized virtual_call}
  0x000002049625b014: 4400 00e8 

  0x000002049625b018: ; ImmutableOopMap {[128]=Oop [152]=Oop [168]=Oop [176]=Oop [192]=Oop [200]=Oop }
                      ;*invokespecial append {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@144 (line 972)
  0x000002049625b018: 643b 2507 

  0x000002049625b01c: ;   {other}
  0x000002049625b01c: 0f1f 8400 | 0c18 000d | 488b 8424 | c000 0000 

  0x000002049625b02c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625b02c: 49b8 b855 | 7aea 0402 | 0000 4983 | 80c0 0400 

  0x000002049625b03c: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625b03c: 0001 49b8 | 60b2 00ea | 0402 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x000002049625b05c: ff1f 0085 | d20f 847c | 3f00 0048 | 8b84 24c0 

  0x000002049625b06c: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625b06c: 0000 0049 | b860 b200 | ea04 0200 | 0049 8380 | 1001 0000 

  0x000002049625b080: ;   {oop(")"{0x00000000c00004b0})}
  0x000002049625b080: 0149 b8b0 | 0400 c000 | 0000 0048 | 8b94 24c0 | 0000 000f 

  0x000002049625b094: ;   {optimized virtual_call}
  0x000002049625b094: 1f40 00e8 

  0x000002049625b098: ; ImmutableOopMap {[128]=Oop [152]=Oop [168]=Oop [176]=Oop [192]=Oop [200]=Oop }
                      ;*invokespecial append {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@150 (line 972)
  0x000002049625b098: e43a 2507 

  0x000002049625b09c: ;   {other}
  0x000002049625b09c: 0f1f 8400 | 8c18 000e | 488b 8424 | c000 0000 

  0x000002049625b0ac: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625b0ac: 48ba b855 | 7aea 0402 | 0000 4883 | 82f8 0400 

  0x000002049625b0bc: ;   {metadata(method data for {method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625b0bc: 0001 48ba | a8ba 00ea | 0402 0000 | 8bb2 cc00 | 0000 83c6 | 0289 b2cc | 0000 0081 | e6fe ff1f 
  0x000002049625b0dc: 0085 f60f | 841f 3f00 

  0x000002049625b0e4: ;   {metadata('java/lang/String')}
  0x000002049625b0e4: 0048 ba40 | e700 a704 | 0200 0049 | 8b87 b801 | 0000 488d | 7818 493b | bfc8 0100 | 000f 871e 
  0x000002049625b104: 3f00 0049 | 89bf b801 | 0000 48c7 | 0001 0000 | 0048 8bca | 49ba 0000 | 00a7 0402 | 0000 492b 
  0x000002049625b124: ca89 4808 | 4833 c989 | 480c 4833 | c948 8948 | 104c 8bc0 

  0x000002049625b138: ;   {metadata(method data for {method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625b138: 49b9 a8ba | 00ea 0402 | 0000 4983 | 8110 0100 

  0x000002049625b148: ;   {metadata(method data for {method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625b148: 0001 49b8 | f0bb 00ea | 0402 0000 | 458b 88cc | 0000 0041 | 83c1 0245 | 8988 cc00 | 0000 4181 
  0x000002049625b168: e1fe ff1f | 0045 85c9 | 0f84 bc3e | 0000 4c8b 

  0x000002049625b178: ;   {metadata(method data for {method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625b178: c049 b9f0 | bb00 ea04 | 0200 0049 | 8381 1001 | 0000 014c | 8b84 24c0 

  0x000002049625b190: ;   {oop(nullptr)}
  0x000002049625b190: 0000 0049 | b900 0000 | 0000 0000 | 0048 8bd0 | 4889 8424 | d000 0000 | 0f1f 8000 

  0x000002049625b1ac: ;   {optimized virtual_call}
  0x000002049625b1ac: 0000 00e8 

  0x000002049625b1b0: ; ImmutableOopMap {[128]=Oop [152]=Oop [168]=Oop [176]=Oop [200]=Oop [208]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::<init>@3
                      ; - java.lang.StringBuilder::toString@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@153 (line 972)
  0x000002049625b1b0: cc39 2507 

  0x000002049625b1b4: ;   {other}
  0x000002049625b1b4: 0f1f 8400 | a419 000f | 488b 9c24 | c800 0000 

  0x000002049625b1c4: ; implicit exception: dispatches to 0x000002049625f053
  0x000002049625b1c4: 483b 034c 

  0x000002049625b1c8: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625b1c8: 8bc3 49b9 | b855 7aea | 0402 0000 

  0x000002049625b1d4: ;   {metadata('org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625b1d4: 49ba 0000 | 09a8 0402 | 0000 4d89 | 9140 0500 | 0049 8381 | 4805 0000 

  0x000002049625b1ec: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625b1ec: 0149 b8c0 | 5078 ea04 | 0200 0045 | 8b88 cc00 | 0000 4183 | c102 4589 | 88cc 0000 | 0041 81e1 
  0x000002049625b20c: feff 1f00 | 4585 c90f | 843f 3e00 | 008b 5320 

  0x000002049625b21c: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625b21c: 4885 d249 | b8c0 5078 | ea04 0200 | 0049 c7c1 | 1001 0000 | 7407 49c7 | c120 0100 | 004b 8b34 
  0x000002049625b23c: 0848 8d76 | 014b 8934 | 080f 84ff | 0200 0048 | 85d2 0f84 | 2900 0000 

  0x000002049625b254: ;   {metadata('org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625b254: 48bf 884a | 09a8 0402 | 0000 8b72 | 0849 ba00 | 0000 a704 | 0200 0049 | 03f2 483b | 7e38 0f85 
  0x000002049625b274: 013e 0000 | e900 0000 | 004c 8bc2 

  0x000002049625b280: ; implicit exception: dispatches to 0x000002049625f07e
  0x000002049625b280: 483b 024c 

  0x000002049625b284: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625b284: 8bc2 49b9 | c050 78ea | 0402 0000 

  0x000002049625b290: ;   {metadata('org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625b290: 49ba 884a | 09a8 0402 | 0000 4d89 | 9140 0100 | 0049 8381 | 4801 0000 

  0x000002049625b2a8: ;   {metadata(method data for {method} {0x00000204ea507238} 'getLogger' '(Ljava/lang/String;)Lorg/osgi/service/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625b2a8: 0149 b800 | 5378 ea04 | 0200 0045 | 8b88 cc00 | 0000 4183 | c102 4589 | 88cc 0000 | 0041 81e1 
  0x000002049625b2c8: feff 1f00 | 4585 c90f | 84ae 3d00 | 004c 8bc2 

  0x000002049625b2d8: ;   {metadata(method data for {method} {0x00000204ea507238} 'getLogger' '(Ljava/lang/String;)Lorg/osgi/service/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625b2d8: 49b9 0053 | 78ea 0402 

  0x000002049625b2e0: ;   {metadata('org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625b2e0: 0000 49ba | 884a 09a8 | 0402 0000 | 4d89 9120 | 0100 0049 | 8381 2801 

  0x000002049625b2f8: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625b2f8: 0000 0149 | b890 5478 | ea04 0200 | 0045 8b88 | cc00 0000 | 4183 c102 | 4589 88cc | 0000 0041 
  0x000002049625b318: 81e1 feff | 1f00 4585 | c90f 847d | 3d00 004c 

  0x000002049625b328: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625b328: 8bc2 49b9 | 9054 78ea | 0402 0000 

  0x000002049625b334: ;   {metadata('org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625b334: 49ba 884a | 09a8 0402 | 0000 4d89 | 9120 0100 | 0049 8381 | 2801 0000 

  0x000002049625b34c: ;   {oop("org.eclipse.osgi/debug/events"{0x00000000c0064c78})}
  0x000002049625b34c: 0149 b878 | 4c06 c000 

  0x000002049625b354: ;   {oop(a 'java/lang/Class'{0x00000000ec830698} = 'org/osgi/service/log/Logger')}
  0x000002049625b354: 0000 0049 | b998 0683 | ec00 0000 | 0066 0f1f 

  0x000002049625b364: ;   {optimized virtual_call}
  0x000002049625b364: 4400 00e8 

  0x000002049625b368: ; ImmutableOopMap {[128]=Oop [152]=Oop [168]=Oop [176]=Oop [208]=Oop }
                      ;*invokevirtual getLogger {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@4 (line 73)
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@2 (line 1)
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@156 (line 972)
  0x000002049625b368: 1438 2507 

  0x000002049625b36c: ;   {other}
  0x000002049625b36c: 0f1f 8400 | 5c1b 0010 | 4885 c075 

  0x000002049625b378: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625b378: 1648 be90 | 5478 ea04 | 0200 0080 | 8e41 0100 | 0001 e9e7 

  0x000002049625b38c: ;   {metadata('org/eclipse/equinox/log/Logger')}
  0x000002049625b38c: 0000 0048 | bf70 1f09 | a804 0200 | 008b 7008 | 49ba 0000 | 00a7 0402 | 0000 4903 | f248 3b7e 
  0x000002049625b3ac: 200f 841a | 0000 0048 | 3bf7 0f84 | 1100 0000 

  0x000002049625b3bc: ;   {runtime_call slow_subtype_check Runtime1 stub}
  0x000002049625b3bc: 5657 e83d | 6530 075e | 5e85 f60f | 848d 0000 

  0x000002049625b3cc: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625b3cc: 0048 be90 | 5478 ea04 | 0200 008b | 7808 49ba | 0000 00a7 | 0402 0000 | 4903 fa48 | 3bbe 5801 
  0x000002049625b3ec: 0000 750d | 4883 8660 | 0100 0001 | e979 0000 | 0048 3bbe | 6801 0000 | 750d 4883 | 8670 0100 
  0x000002049625b40c: 0001 e963 | 0000 0048 | 83be 5801 | 0000 0075 | 1748 89be | 5801 0000 | 48c7 8660 | 0100 0001 
  0x000002049625b42c: 0000 00e9 | 4200 0000 | 4883 be68 | 0100 0000 | 7517 4889 | be68 0100 | 0048 c786 | 7001 0000 
  0x000002049625b44c: 0100 0000 | e921 0000 | 00e9 1c00 

  0x000002049625b458: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625b458: 0000 48be | 9054 78ea | 0402 0000 | 4883 ae48 | 0100 0001 | e954 3c00 | 00e9 0000 | 0000 488b 
  0x000002049625b478: d048 3b02 

  0x000002049625b47c: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625b47c: 4c8b c248 | bec0 5078 | ea04 0200 | 0045 8b40 | 0849 ba00 | 0000 a704 | 0200 004d | 03c2 4c3b 
  0x000002049625b49c: 8678 0100 | 0075 0d48 | 8386 8001 | 0000 01e9 | 6000 0000 | 4c3b 8688 | 0100 0075 | 0d48 8386 
  0x000002049625b4bc: 9001 0000 | 01e9 4a00 | 0000 4883 | be78 0100 | 0000 7517 | 4c89 8678 | 0100 0048 | c786 8001 
  0x000002049625b4dc: 0000 0100 | 0000 e929 | 0000 0048 | 83be 8801 | 0000 0075 | 174c 8986 | 8801 0000 | 48c7 8690 
  0x000002049625b4fc: 0100 0001 | 0000 00e9 | 0800 0000 | 4883 8668 | 0100 0001 | 4c8b 8424 | d000 0000 | 0f1f 4400 
  0x000002049625b51c: 0048 b8ff | ffff ffff 

  0x000002049625b524: ;   {virtual_call}
  0x000002049625b524: ffff ffe8 

  0x000002049625b528: ; ImmutableOopMap {[128]=Oop [152]=Oop [168]=Oop [176]=Oop }
                      ;*invokeinterface trace {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@17 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@156 (line 972)
  0x000002049625b528: 543b 2507 

  0x000002049625b52c: ;   {other}
  0x000002049625b52c: 0f1f 8400 | 1c1d 0011 

  0x000002049625b534: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625b534: 49b8 c050 | 78ea 0402 | 0000 41ff | 80a0 0100 | 00e9 5200 | 0000 488b | 8424 d000 

  0x000002049625b550: ;   {oop(a 'java/lang/Class'{0x00000000ec827d58} = 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625b550: 0000 49b8 | 587d 82ec | 0000 0000 | 418b 90c8 | 0000 0048 | 3b02 4c8b 

  0x000002049625b568: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625b568: c248 bec0 | 5078 ea04 

  0x000002049625b570: ;   {metadata('java/io/PrintStream')}
  0x000002049625b570: 0200 0049 | ba60 a300 | a704 0200 | 004c 8996 | c801 0000 | 4883 86d0 | 0100 0001 

  0x000002049625b58c: ;   {optimized virtual_call}
  0x000002049625b58c: 4c8b c0e8 

  0x000002049625b590: ; ImmutableOopMap {[128]=Oop [152]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual println {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@29 (line 232)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@156 (line 972)
  0x000002049625b590: ec35 2507 

  0x000002049625b594: ;   {other}
  0x000002049625b594: 0f1f 8400 | 841d 0012 | 488b 9c24 | a800 0000 | 488b b424 | 9800 0000 | 4885 f675 

  0x000002049625b5b0: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625b5b0: 1648 bfb8 | 557a ea04 | 0200 0080 | 8f61 0500 | 0001 e9cc 

  0x000002049625b5c4: ;   {metadata('org/osgi/framework/BundleEvent')}
  0x000002049625b5c4: 0000 0048 | b800 8008 | a804 0200 | 008b 5608 | 49ba 0000 | 00a7 0402 | 0000 4903 | d248 3bc2 
  0x000002049625b5e4: 0f85 8d00 

  0x000002049625b5e8: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625b5e8: 0000 48bf | b855 7aea | 0402 0000 | 8b46 0849 | ba00 0000 | a704 0200 | 0049 03c2 | 483b 8778 
  0x000002049625b608: 0500 0075 | 0d48 8387 | 8005 0000 | 01e9 7900 | 0000 483b | 8788 0500 | 0075 0d48 | 8387 9005 
  0x000002049625b628: 0000 01e9 | 6300 0000 | 4883 bf78 | 0500 0000 | 7517 4889 | 8778 0500 | 0048 c787 | 8005 0000 
  0x000002049625b648: 0100 0000 | e942 0000 | 0048 83bf | 8805 0000 | 0075 1748 | 8987 8805 | 0000 48c7 | 8790 0500 
  0x000002049625b668: 0001 0000 | 00e9 2100 | 0000 e91c 

  0x000002049625b674: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625b674: 0000 0048 | bfb8 557a | ea04 0200 | 0048 83af | 6805 0000 | 01e9 4a3a | 0000 e900 | 0000 004c 
  0x000002049625b694: 8bc6 483b | 0348 8bd3 

  0x000002049625b69c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625b69c: 48bf b855 | 7aea 0402 | 0000 8b52 | 0849 ba00 | 0000 a704 | 0200 0049 | 03d2 483b | 97b0 0500 
  0x000002049625b6bc: 0075 0d48 | 8387 b805 | 0000 01e9 | 6000 0000 | 483b 97c0 | 0500 0075 | 0d48 8387 | c805 0000 
  0x000002049625b6dc: 01e9 4a00 | 0000 4883 | bfb0 0500 | 0000 7517 | 4889 97b0 | 0500 0048 | c787 b805 | 0000 0100 
  0x000002049625b6fc: 0000 e929 | 0000 0048 | 83bf c005 | 0000 0075 | 1748 8997 | c005 0000 | 48c7 87c8 | 0500 0001 
  0x000002049625b71c: 0000 00e9 | 0800 0000 | 4883 87a0 | 0500 0001 | 488b d366 | 0f1f 4400 | 0048 b8ff | ffff ffff 
  0x000002049625b73c: ;   {virtual_call}
  0x000002049625b73c: ffff ffe8 

  0x000002049625b740: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop }
                      ;*invokeinterface bundleChanged {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@166 (line 975)
  0x000002049625b740: 5cff 2407 

  0x000002049625b744: ;   {other}
  0x000002049625b744: 0f1f 8400 | 341f 0013 

  0x000002049625b74c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625b74c: 48ba b855 | 7aea 0402 | 0000 ff82 | d805 0000 | 488b 8424 | b000 0000 | e90c 2400 | 004c 8b8c 
  0x000002049625b76c: 2490 0000 | 004d 85c9 

  0x000002049625b774: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625b774: 7516 48bf | b855 7aea | 0402 0000 | 808f 8109 | 0000 01e9 | e900 0000 

  0x000002049625b78c: ;   {metadata('org/osgi/framework/FrameworkListener')}
  0x000002049625b78c: 48b8 1884 | 08a8 0402 | 0000 418b | 7908 49ba | 0000 00a7 | 0402 0000 | 4903 fa48 | 3b47 200f 
  0x000002049625b7ac: 841a 0000 | 0048 3bf8 | 0f84 1100 | 0000 5750 

  0x000002049625b7bc: ;   {runtime_call slow_subtype_check Runtime1 stub}
  0x000002049625b7bc: e83f 6130 | 075f 5f85 | ff0f 848e 

  0x000002049625b7c8: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625b7c8: 0000 0048 | bfb8 557a | ea04 0200 | 0041 8b41 | 0849 ba00 | 0000 a704 | 0200 0049 | 03c2 483b 
  0x000002049625b7e8: 8798 0900 | 0075 0d48 | 8387 a009 | 0000 01e9 | 7900 0000 | 483b 87a8 | 0900 0075 | 0d48 8387 
  0x000002049625b808: b009 0000 | 01e9 6300 | 0000 4883 | bf98 0900 | 0000 7517 | 4889 8798 | 0900 0048 | c787 a009 
  0x000002049625b828: 0000 0100 | 0000 e942 | 0000 0048 | 83bf a809 | 0000 0075 | 1748 8987 | a809 0000 | 48c7 87b0 
  0x000002049625b848: 0900 0001 | 0000 00e9 | 2100 0000 | e91c 0000 

  0x000002049625b858: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625b858: 0048 bfb8 | 557a ea04 | 0200 0048 | 83af 8809 | 0000 01e9 | 7638 0000 | e900 0000 | 0049 8bd9 
  0x000002049625b878: 8b72 180f | be76 1185 

  0x000002049625b880: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625b880: f648 beb8 | 557a ea04 | 0200 0048 | c7c7 c009 | 0000 7407 | 48c7 c7d0 | 0900 0048 | 8b04 3e48 
  0x000002049625b8a0: 8d40 0148 | 8904 3e0f | 84ef 0e00 | 0066 6690 

  0x000002049625b8b0: ;   {no_reloc}
  0x000002049625b8b0: e94e 3800 | 0000 0000 | 0000 498b | 87b8 0100 | 0048 8d78 | 1849 3bbf | c801 0000 | 0f87 3b38 
  0x000002049625b8d0: 0000 4989 | bfb8 0100 | 0048 c700 | 0100 0000 | 488b ca49 | ba00 0000 | a704 0200 | 0049 2bca 
  0x000002049625b8f0: 8948 0848 | 33c9 8948 | 0c48 33c9 | 4889 4810 | 4889 8424 | d800 0000 

  0x000002049625b908: ; implicit exception: dispatches to 0x000002049625f11a
  0x000002049625b908: 483b 0348 

  0x000002049625b90c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625b90c: 8bd3 48be | b855 7aea | 0402 0000 | 4883 86e0 | 0900 0001 | 8b53 0849 | ba00 0000 | a704 0200 
  0x000002049625b92c: 0049 03d2 | 488b 5270 | 488b 1248 | 3b02 488b 

  0x000002049625b93c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625b93c: f248 bfb8 | 557a ea04 | 0200 0048 | 8387 180a 

  0x000002049625b94c: ;   {metadata(method data for {method} {0x00000204a7465ac0} 'getName' '()Ljava/lang/String;' in 'java/lang/Class')}
  0x000002049625b94c: 0000 0148 | be00 7208 | ea04 0200 | 008b becc | 0000 0083 | c702 89be | cc00 0000 | 81e7 feff 
  0x000002049625b96c: 1f00 85ff | 0f84 ae37 | 0000 8b72 | 2c48 85f6 

  0x000002049625b97c: ;   {metadata(method data for {method} {0x00000204a7465ac0} 'getName' '()Ljava/lang/String;' in 'java/lang/Class')}
  0x000002049625b97c: 48bf 0072 | 08ea 0402 | 0000 48c7 | c110 0100 | 0074 0748 | c7c1 2001 | 0000 4c8b | 0c0f 4d8d 
  0x000002049625b99c: 4901 4c89 | 0c0f 0f84 | 1d00 0000 

  0x000002049625b9a8: ;   {metadata(method data for {method} {0x00000204a7465ac0} 'getName' '()Ljava/lang/String;' in 'java/lang/Class')}
  0x000002049625b9a8: 48ba 0072 | 08ea 0402 | 0000 ff82 | 3001 0000 | 4889 9c24 | e000 0000 | e932 0000 | 0048 899c 
  0x000002049625b9c8: 24e0 0000 | 0048 8bfa 

  0x000002049625b9d0: ;   {metadata(method data for {method} {0x00000204a7465ac0} 'getName' '()Ljava/lang/String;' in 'java/lang/Class')}
  0x000002049625b9d0: 48b9 0072 | 08ea 0402 | 0000 4883 | 8148 0100 | 0001 0f1f 

  0x000002049625b9e4: ;   {optimized virtual_call}
  0x000002049625b9e4: 4400 00e8 

  0x000002049625b9e8: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [216]=Oop [224]=Oop }
                      ;*invokevirtual initClassName {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.Class::getName@14
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@321 (line 996)
  0x000002049625b9e8: 9431 2507 

  0x000002049625b9ec: ;   {other}
  0x000002049625b9ec: 0f1f 8400 | dc21 0014 

  0x000002049625b9f4: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625b9f4: 488b f048 | bab8 557a | ea04 0200 | 0048 8382 | 500a 0000 

  0x000002049625ba08: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625ba08: 0148 ba08 | a00e ea04 | 0200 008b | bacc 0000 | 0083 c702 | 89ba cc00 | 0000 81e7 | feff 1f00 
  0x000002049625ba28: 85ff 0f84 | 1537 0000 

  0x000002049625ba30: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625ba30: 4885 f648 | ba08 a00e | ea04 0200 | 0048 c7c7 | 1001 0000 | 7507 48c7 | c720 0100 | 0048 8b1c 
  0x000002049625ba50: 3a48 8d5b | 0148 891c | 3a0f 851f 

  0x000002049625ba5c: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625ba5c: 0000 0048 | ba08 a00e | ea04 0200 | 00ff 8230 

  0x000002049625ba6c: ;   {oop("null"{0x00000000c0004000})}
  0x000002049625ba6c: 0100 0048 | be00 4000 | c000 0000 | 00e9 b900 | 0000 483b | 0648 8bd6 

  0x000002049625ba84: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625ba84: 48bf 08a0 | 0eea 0402 | 0000 8b52 | 0849 ba00 | 0000 a704 | 0200 0049 | 03d2 483b | 9758 0100 
  0x000002049625baa4: 0075 0d48 | 8387 6001 | 0000 01e9 | 6000 0000 | 483b 9768 | 0100 0075 | 0d48 8387 | 7001 0000 
  0x000002049625bac4: 01e9 4a00 | 0000 4883 | bf58 0100 | 0000 7517 | 4889 9758 | 0100 0048 | c787 6001 | 0000 0100 
  0x000002049625bae4: 0000 e929 | 0000 0048 | 83bf 6801 | 0000 0075 | 1748 8997 | 6801 0000 | 48c7 8770 | 0100 0001 
  0x000002049625bb04: 0000 00e9 | 0800 0000 | 4883 8748 | 0100 0001 | 488b d666 | 0f1f 4400 | 0048 b8ff | ffff ffff 
  0x000002049625bb24: ;   {virtual_call}
  0x000002049625bb24: ffff ffe8 

  0x000002049625bb28: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [216]=Oop [224]=Oop }
                      ;*invokevirtual toString {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::valueOf@11
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@324 (line 996)
  0x000002049625bb28: 5435 2507 

  0x000002049625bb2c: ;   {other}
  0x000002049625bb2c: 0f1f 8400 | 1c23 0015 | 488b f048 | 8b84 24d8 | 0000 004c 

  0x000002049625bb40: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625bb40: 8bc0 48bf | b855 7aea | 0402 0000 | 4883 8760 | 0a00 0001 

  0x000002049625bb54: ;   {metadata(method data for {method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625bb54: 49b8 c8f6 | 2fea 0402 | 0000 418b | b8cc 0000 | 0083 c702 | 4189 b8cc | 0000 0081 | e7fe ff1f 
  0x000002049625bb74: 0085 ff0f | 84ee 3500 | 004c 8bc0 

  0x000002049625bb80: ;   {metadata(method data for {method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625bb80: 48bf c8f6 | 2fea 0402 | 0000 4883 | 8710 0100 | 0001 4c8b | c648 8bd0 | 0f1f 8000 

  0x000002049625bb9c: ;   {optimized virtual_call}
  0x000002049625bb9c: 0000 00e8 

  0x000002049625bba0: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [216]=Oop [224]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::<init>@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@327 (line 996)
  0x000002049625bba0: dc2f 2507 

  0x000002049625bba4: ;   {other}
  0x000002049625bba4: 0f1f 8400 | 9423 0016 | 488b 8424 | d800 0000 

  0x000002049625bbb4: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625bbb4: 49b8 b855 | 7aea 0402 | 0000 4983 | 8070 0a00 

  0x000002049625bbc4: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625bbc4: 0001 49b8 | 60b2 00ea | 0402 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x000002049625bbe4: ff1f 0085 | d20f 849d | 3500 0048 | 8b84 24d8 

  0x000002049625bbf4: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625bbf4: 0000 0049 | b860 b200 | ea04 0200 | 0049 8380 | 1001 0000 

  0x000002049625bc08: ;   {oop("@"{0x00000000c0006bf0})}
  0x000002049625bc08: 0149 b8f0 | 6b00 c000 | 0000 0048 | 8b94 24d8 | 0000 000f 

  0x000002049625bc1c: ;   {optimized virtual_call}
  0x000002049625bc1c: 1f40 00e8 

  0x000002049625bc20: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [216]=Oop [224]=Oop }
                      ;*invokespecial append {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@333 (line 996)
  0x000002049625bc20: 5c2f 2507 

  0x000002049625bc24: ;   {other}
  0x000002049625bc24: 0f1f 8400 | 1424 0017 

  0x000002049625bc2c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625bc2c: 48ba b855 | 7aea 0402 | 0000 4883 | 82a8 0a00 | 0001 488b | 9424 e000 

  0x000002049625bc44: ;   {static_call}
  0x000002049625bc44: 0000 90e8 

  0x000002049625bc48: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [216]=Oop [224]=Oop }
                      ;*invokestatic identityHashCode {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@338 (line 997)
  0x000002049625bc48: 3439 2507 

  0x000002049625bc4c: ;   {other}
  0x000002049625bc4c: 0f1f 8400 | 3c24 0018 

  0x000002049625bc54: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625bc54: 48ba b855 | 7aea 0402 | 0000 4883 | 82b8 0a00 

  0x000002049625bc64: ;   {metadata(method data for {method} {0x00000204a74126c8} 'toHexString' '(I)Ljava/lang/String;' in 'java/lang/Integer')}
  0x000002049625bc64: 0001 48ba | a05c 2fea | 0402 0000 | 448b 82cc | 0000 0041 | 83c0 0244 | 8982 cc00 | 0000 4181 
  0x000002049625bc84: e0fe ff1f | 0045 85c0 | 0f84 1b35 

  0x000002049625bc90: ;   {metadata(method data for {method} {0x00000204a74126c8} 'toHexString' '(I)Ljava/lang/String;' in 'java/lang/Integer')}
  0x000002049625bc90: 0000 48ba | a05c 2fea | 0402 0000 | 4883 8210 | 0100 0001 | 488b d041 | b804 0000 

  0x000002049625bcac: ;   {static_call}
  0x000002049625bcac: 0066 90e8 

  0x000002049625bcb0: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [216]=Oop [224]=Oop }
                      ;*invokestatic toUnsignedString0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.Integer::toHexString@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@341 (line 997)
  0x000002049625bcb0: cc38 2507 

  0x000002049625bcb4: ;   {other}
  0x000002049625bcb4: 0f1f 8400 | a424 0019 | 4c8b 8424 | d800 0000 

  0x000002049625bcc4: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625bcc4: 48ba b855 | 7aea 0402 | 0000 4883 | 82c8 0a00 

  0x000002049625bcd4: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625bcd4: 0001 49b8 | 60b2 00ea | 0402 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x000002049625bcf4: ff1f 0085 | d20f 84cf | 3400 004c | 8b84 24d8 

  0x000002049625bd04: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625bd04: 0000 0048 | ba60 b200 | ea04 0200 | 0048 8382 | 1001 0000 | 014c 8bc0 | 488b 9424 | d800 0000 
  0x000002049625bd24: ;   {optimized virtual_call}
  0x000002049625bd24: 6666 90e8 

  0x000002049625bd28: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [216]=Oop [224]=Oop }
                      ;*invokespecial append {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@344 (line 997)
  0x000002049625bd28: 542e 2507 

  0x000002049625bd2c: ;   {other}
  0x000002049625bd2c: 0f1f 8400 | 1c25 001a | 488b 8424 | d800 0000 

  0x000002049625bd3c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625bd3c: 48ba b855 | 7aea 0402 | 0000 4883 | 8200 0b00 

  0x000002049625bd4c: ;   {metadata(method data for {method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625bd4c: 0001 48ba | a8ba 00ea | 0402 0000 | 8bb2 cc00 | 0000 83c6 | 0289 b2cc | 0000 0081 | e6fe ff1f 
  0x000002049625bd6c: 0085 f60f | 847a 3400 

  0x000002049625bd74: ;   {metadata('java/lang/String')}
  0x000002049625bd74: 0048 ba40 | e700 a704 | 0200 0049 | 8b87 b801 | 0000 488d | 7818 493b | bfc8 0100 | 000f 8779 
  0x000002049625bd94: 3400 0049 | 89bf b801 | 0000 48c7 | 0001 0000 | 0048 8bca | 49ba 0000 | 00a7 0402 | 0000 492b 
  0x000002049625bdb4: ca89 4808 | 4833 c989 | 480c 4833 | c948 8948 | 1048 8984 | 24e8 0000 | 004c 8bc0 

  0x000002049625bdd0: ;   {metadata(method data for {method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625bdd0: 49b9 a8ba | 00ea 0402 | 0000 4983 | 8110 0100 

  0x000002049625bde0: ;   {metadata(method data for {method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625bde0: 0001 49b8 | f0bb 00ea | 0402 0000 | 458b 88cc | 0000 0041 | 83c1 0245 | 8988 cc00 | 0000 4181 
  0x000002049625be00: e1fe ff1f | 0045 85c9 | 0f84 0f34 | 0000 4c8b 

  0x000002049625be10: ;   {metadata(method data for {method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625be10: c049 b9f0 | bb00 ea04 | 0200 0049 | 8381 1001 | 0000 014c | 8b84 24d8 

  0x000002049625be28: ;   {oop(nullptr)}
  0x000002049625be28: 0000 0049 | b900 0000 | 0000 0000 | 0048 8bd0 | 0f1f 8000 

  0x000002049625be3c: ;   {optimized virtual_call}
  0x000002049625be3c: 0000 00e8 

  0x000002049625be40: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [224]=Oop [232]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::<init>@3
                      ; - java.lang.StringBuilder::toString@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@347 (line 996)
  0x000002049625be40: 3c2d 2507 

  0x000002049625be44: ;   {other}
  0x000002049625be44: 0f1f 8400 | 3426 001b | 488b 9424 | 8000 0000 | 8b5a 1848 | 899c 24f8 | 0000 0090 

  0x000002049625be60: ;   {no_reloc}
  0x000002049625be60: e9e8 3300 | 0000 0000 | 0000 498b | 87b8 0100 | 0048 8d78 | 1849 3bbf | c801 0000 | 0f87 d533 
  0x000002049625be80: 0000 4989 | bfb8 0100 | 0048 c700 | 0100 0000 | 488b ca49 | ba00 0000 | a704 0200 | 0049 2bca 
  0x000002049625bea0: 8948 0848 | 33c9 8948 | 0c48 33c9 | 4889 4810 | 4889 8424 | f000 0000 

  0x000002049625beb8: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625beb8: 4c8b c048 | bab8 557a | ea04 0200 | 0048 8382 | 380b 0000 

  0x000002049625becc: ;   {metadata(method data for {method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625becc: 0149 b8c8 | f62f ea04 | 0200 0041 | 8b90 cc00 | 0000 83c2 | 0241 8990 | cc00 0000 | 81e2 feff 
  0x000002049625beec: 1f00 85d2 | 0f84 6e33 | 0000 4c8b 

  0x000002049625bef8: ;   {metadata(method data for {method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625bef8: c048 bac8 | f62f ea04 | 0200 0048 | 8382 1001 

  0x000002049625bf08: ;   {oop("dispatchFrameworkEvent["{0x00000000eabca2f0})}
  0x000002049625bf08: 0000 0149 | b8f0 a2bc | ea00 0000 | 0048 8bd0 | 0f1f 8000 

  0x000002049625bf1c: ;   {optimized virtual_call}
  0x000002049625bf1c: 0000 00e8 

  0x000002049625bf20: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [224]=Oop [232]=Oop [240]=Oop [248]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::<init>@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@366 (line 999)
  0x000002049625bf20: 5c2c 2507 

  0x000002049625bf24: ;   {other}
  0x000002049625bf24: 0f1f 8400 | 1427 001c | 488b 9424 | 8000 0000 | 8b72 1048 | 8b84 24f0 

  0x000002049625bf3c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625bf3c: 0000 0048 | bfb8 557a | ea04 0200 | 0048 8387 | 480b 0000 

  0x000002049625bf50: ;   {metadata(method data for {method} {0x00000204a745dad0} 'append' '(Ljava/lang/Object;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625bf50: 0148 bf68 | 9e0e ea04 | 0200 008b | 9fcc 0000 | 0083 c302 | 899f cc00 | 0000 81e3 | feff 1f00 
  0x000002049625bf70: 85db 0f84 | 0d33 0000 

  0x000002049625bf78: ;   {metadata(method data for {method} {0x00000204a745dad0} 'append' '(Ljava/lang/Object;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625bf78: 48bf 689e | 0eea 0402 | 0000 4883 | 8710 0100 

  0x000002049625bf88: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625bf88: 0001 48bf | 08a0 0eea | 0402 0000 | 8b9f cc00 | 0000 83c3 | 0289 9fcc | 0000 0081 | e3fe ff1f 
  0x000002049625bfa8: 0085 db0f | 84f5 3200 | 0048 85f6 

  0x000002049625bfb4: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625bfb4: 48bf 08a0 | 0eea 0402 | 0000 48c7 | c310 0100 | 0075 0748 | c7c3 2001 | 0000 488b | 041f 488d 
  0x000002049625bfd4: 4001 4889 | 041f 0f85 | 1f00 0000 

  0x000002049625bfe0: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625bfe0: 48be 08a0 | 0eea 0402 | 0000 ff86 | 3001 0000 

  0x000002049625bff0: ;   {oop("null"{0x00000000c0004000})}
  0x000002049625bff0: 48be 0040 | 00c0 0000 | 0000 e9b0 | 0000 0048 | 3b06 488b 

  0x000002049625c004: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625c004: fe48 bb08 | a00e ea04 | 0200 008b | 7f08 49ba | 0000 00a7 | 0402 0000 | 4903 fa48 | 3bbb 5801 
  0x000002049625c024: 0000 750d | 4883 8360 | 0100 0001 | e960 0000 | 0048 3bbb | 6801 0000 | 750d 4883 | 8370 0100 
  0x000002049625c044: 0001 e94a | 0000 0048 | 83bb 5801 | 0000 0075 | 1748 89bb | 5801 0000 | 48c7 8360 | 0100 0001 
  0x000002049625c064: 0000 00e9 | 2900 0000 | 4883 bb68 | 0100 0000 | 7517 4889 | bb68 0100 | 0048 c783 | 7001 0000 
  0x000002049625c084: 0100 0000 | e908 0000 | 0048 8383 | 4801 0000 | 0148 8bd6 | 0f1f 8000 

  0x000002049625c09c: ;   {optimized virtual_call}
  0x000002049625c09c: 0000 00e8 

  0x000002049625c0a0: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [224]=Oop [232]=Oop [240]=Oop [248]=Oop }
                      ;*invokevirtual toString {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::valueOf@11
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@373 (line 999)
  0x000002049625c0a0: dc2a 2507 

  0x000002049625c0a4: ;   {other}
  0x000002049625c0a4: 0f1f 8400 | 9428 001d | 488b f048 | 8b84 24f0 

  0x000002049625c0b4: ;   {metadata(method data for {method} {0x00000204a745dad0} 'append' '(Ljava/lang/Object;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625c0b4: 0000 0049 | b868 9e0e | ea04 0200 | 0049 8380 | 2001 0000 

  0x000002049625c0c8: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625c0c8: 0149 b860 | b200 ea04 | 0200 0041 | 8b90 cc00 | 0000 83c2 | 0241 8990 | cc00 0000 | 81e2 feff 
  0x000002049625c0e8: 1f00 85d2 | 0f84 da31 | 0000 488b | 8424 f000 

  0x000002049625c0f8: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625c0f8: 0000 49b8 | 60b2 00ea | 0402 0000 | 4983 8010 | 0100 0001 | 4c8b c648 | 8b94 24f0 

  0x000002049625c114: ;   {optimized virtual_call}
  0x000002049625c114: 0000 00e8 

  0x000002049625c118: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [224]=Oop [232]=Oop [240]=Oop [248]=Oop }
                      ;*invokespecial append {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::append@2
                      ; - java.lang.StringBuilder::append@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@373 (line 999)
  0x000002049625c118: 642a 2507 

  0x000002049625c11c: ;   {other}
  0x000002049625c11c: 0f1f 8400 | 0c29 001e | 488b 8424 | f000 0000 

  0x000002049625c12c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625c12c: 4c8b c048 | bab8 557a | ea04 0200 | 0048 8382 | 800b 0000 

  0x000002049625c140: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625c140: 0149 b860 | b200 ea04 | 0200 0041 | 8b90 cc00 | 0000 83c2 | 0241 8990 | cc00 0000 | 81e2 feff 
  0x000002049625c160: 1f00 85d2 | 0f84 8331 | 0000 4c8b 

  0x000002049625c16c: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625c16c: c048 ba60 | b200 ea04 | 0200 0048 | 8382 1001 

  0x000002049625c17c: ;   {oop("]("{0x00000000eabca280})}
  0x000002049625c17c: 0000 0149 | b880 a2bc | ea00 0000 | 0048 8bd0 

  0x000002049625c18c: ;   {optimized virtual_call}
  0x000002049625c18c: 6666 90e8 

  0x000002049625c190: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [224]=Oop [232]=Oop [240]=Oop [248]=Oop }
                      ;*invokespecial append {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@379 (line 999)
  0x000002049625c190: ec29 2507 

  0x000002049625c194: ;   {other}
  0x000002049625c194: 0f1f 8400 | 8429 001f | 488b 8424 | f000 0000 

  0x000002049625c1a4: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625c1a4: 49b8 b855 | 7aea 0402 | 0000 4983 | 80b8 0b00 

  0x000002049625c1b4: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625c1b4: 0001 49b8 | 60b2 00ea | 0402 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x000002049625c1d4: ff1f 0085 | d20f 842f | 3100 0048 | 8b84 24f0 

  0x000002049625c1e4: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625c1e4: 0000 0049 | b860 b200 | ea04 0200 | 0049 8380 | 1001 0000 | 014c 8b84 | 24e8 0000 | 0048 8b94 
  0x000002049625c204: 24f0 0000 | 0066 0f1f 

  0x000002049625c20c: ;   {optimized virtual_call}
  0x000002049625c20c: 4400 00e8 

  0x000002049625c210: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [224]=Oop [240]=Oop [248]=Oop }
                      ;*invokespecial append {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@384 (line 999)
  0x000002049625c210: 6c29 2507 

  0x000002049625c214: ;   {other}
  0x000002049625c214: 0f1f 8400 | 042a 0020 | 488b 8424 | f000 0000 

  0x000002049625c224: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625c224: 49b8 b855 | 7aea 0402 | 0000 4983 | 80f0 0b00 

  0x000002049625c234: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625c234: 0001 49b8 | 60b2 00ea | 0402 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x000002049625c254: ff1f 0085 | d20f 84d0 | 3000 0048 | 8b84 24f0 

  0x000002049625c264: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625c264: 0000 0049 | b860 b200 | ea04 0200 | 0049 8380 | 1001 0000 

  0x000002049625c278: ;   {oop(")"{0x00000000c00004b0})}
  0x000002049625c278: 0149 b8b0 | 0400 c000 | 0000 0048 | 8b94 24f0 | 0000 000f 

  0x000002049625c28c: ;   {optimized virtual_call}
  0x000002049625c28c: 1f40 00e8 

  0x000002049625c290: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [224]=Oop [240]=Oop [248]=Oop }
                      ;*invokespecial append {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@390 (line 999)
  0x000002049625c290: ec28 2507 

  0x000002049625c294: ;   {other}
  0x000002049625c294: 0f1f 8400 | 842a 0021 | 488b 8424 | f000 0000 

  0x000002049625c2a4: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625c2a4: 48ba b855 | 7aea 0402 | 0000 4883 | 8228 0c00 

  0x000002049625c2b4: ;   {metadata(method data for {method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625c2b4: 0001 48ba | a8ba 00ea | 0402 0000 | 8bb2 cc00 | 0000 83c6 | 0289 b2cc | 0000 0081 | e6fe ff1f 
  0x000002049625c2d4: 0085 f60f | 8473 3000 

  0x000002049625c2dc: ;   {metadata('java/lang/String')}
  0x000002049625c2dc: 0048 ba40 | e700 a704 | 0200 0049 | 8b87 b801 | 0000 488d | 7818 493b | bfc8 0100 | 000f 8772 
  0x000002049625c2fc: 3000 0049 | 89bf b801 | 0000 48c7 | 0001 0000 | 0048 8bca | 49ba 0000 | 00a7 0402 | 0000 492b 
  0x000002049625c31c: ca89 4808 | 4833 c989 | 480c 4833 | c948 8948 | 104c 8bc0 

  0x000002049625c330: ;   {metadata(method data for {method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625c330: 49b9 a8ba | 00ea 0402 | 0000 4983 | 8110 0100 

  0x000002049625c340: ;   {metadata(method data for {method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625c340: 0001 49b8 | f0bb 00ea | 0402 0000 | 458b 88cc | 0000 0041 | 83c1 0245 | 8988 cc00 | 0000 4181 
  0x000002049625c360: e1fe ff1f | 0045 85c9 | 0f84 1030 | 0000 4c8b 

  0x000002049625c370: ;   {metadata(method data for {method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625c370: c049 b9f0 | bb00 ea04 | 0200 0049 | 8381 1001 | 0000 014c | 8b84 24f0 

  0x000002049625c388: ;   {oop(nullptr)}
  0x000002049625c388: 0000 0049 | b900 0000 | 0000 0000 | 0048 8bd0 | 4889 8424 | 0001 0000 | 0f1f 8000 

  0x000002049625c3a4: ;   {optimized virtual_call}
  0x000002049625c3a4: 0000 00e8 

  0x000002049625c3a8: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [224]=Oop [248]=Oop [256]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::<init>@3
                      ; - java.lang.StringBuilder::toString@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@393 (line 999)
  0x000002049625c3a8: d427 2507 

  0x000002049625c3ac: ;   {other}
  0x000002049625c3ac: 0f1f 8400 | 9c2b 0022 | 488b 9c24 | f800 0000 

  0x000002049625c3bc: ; implicit exception: dispatches to 0x000002049625f39f
  0x000002049625c3bc: 483b 034c 

  0x000002049625c3c0: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625c3c0: 8bc3 49b9 | b855 7aea | 0402 0000 

  0x000002049625c3cc: ;   {metadata('org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625c3cc: 49ba 0000 | 09a8 0402 | 0000 4d89 | 9170 0c00 | 0049 8381 | 780c 0000 

  0x000002049625c3e4: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625c3e4: 0149 b8c0 | 5078 ea04 | 0200 0045 | 8b88 cc00 | 0000 4183 | c102 4589 | 88cc 0000 | 0041 81e1 
  0x000002049625c404: feff 1f00 | 4585 c90f | 8493 2f00 | 008b 5320 

  0x000002049625c414: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625c414: 4885 d249 | b8c0 5078 | ea04 0200 | 0049 c7c1 | 1001 0000 | 7407 49c7 | c120 0100 | 004b 8b34 
  0x000002049625c434: 0848 8d76 | 014b 8934 | 080f 84ff | 0200 0048 | 85d2 0f84 | 2900 0000 

  0x000002049625c44c: ;   {metadata('org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625c44c: 48bf 884a | 09a8 0402 | 0000 8b72 | 0849 ba00 | 0000 a704 | 0200 0049 | 03f2 483b | 7e38 0f85 
  0x000002049625c46c: 552f 0000 | e900 0000 | 004c 8bc2 

  0x000002049625c478: ; implicit exception: dispatches to 0x000002049625f3ca
  0x000002049625c478: 483b 024c 

  0x000002049625c47c: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625c47c: 8bc2 49b9 | c050 78ea | 0402 0000 

  0x000002049625c488: ;   {metadata('org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625c488: 49ba 884a | 09a8 0402 | 0000 4d89 | 9140 0100 | 0049 8381 | 4801 0000 

  0x000002049625c4a0: ;   {metadata(method data for {method} {0x00000204ea507238} 'getLogger' '(Ljava/lang/String;)Lorg/osgi/service/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625c4a0: 0149 b800 | 5378 ea04 | 0200 0045 | 8b88 cc00 | 0000 4183 | c102 4589 | 88cc 0000 | 0041 81e1 
  0x000002049625c4c0: feff 1f00 | 4585 c90f | 8402 2f00 | 004c 8bc2 

  0x000002049625c4d0: ;   {metadata(method data for {method} {0x00000204ea507238} 'getLogger' '(Ljava/lang/String;)Lorg/osgi/service/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625c4d0: 49b9 0053 | 78ea 0402 

  0x000002049625c4d8: ;   {metadata('org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625c4d8: 0000 49ba | 884a 09a8 | 0402 0000 | 4d89 9120 | 0100 0049 | 8381 2801 

  0x000002049625c4f0: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625c4f0: 0000 0149 | b890 5478 | ea04 0200 | 0045 8b88 | cc00 0000 | 4183 c102 | 4589 88cc | 0000 0041 
  0x000002049625c510: 81e1 feff | 1f00 4585 | c90f 84d1 | 2e00 004c 

  0x000002049625c520: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625c520: 8bc2 49b9 | 9054 78ea | 0402 0000 

  0x000002049625c52c: ;   {metadata('org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625c52c: 49ba 884a | 09a8 0402 | 0000 4d89 | 9120 0100 | 0049 8381 | 2801 0000 

  0x000002049625c544: ;   {oop("org.eclipse.osgi/debug/events"{0x00000000c0064c78})}
  0x000002049625c544: 0149 b878 | 4c06 c000 

  0x000002049625c54c: ;   {oop(a 'java/lang/Class'{0x00000000ec830698} = 'org/osgi/service/log/Logger')}
  0x000002049625c54c: 0000 0049 | b998 0683 | ec00 0000 | 0066 0f1f 

  0x000002049625c55c: ;   {optimized virtual_call}
  0x000002049625c55c: 4400 00e8 

  0x000002049625c560: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [224]=Oop [256]=Oop }
                      ;*invokevirtual getLogger {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@4 (line 73)
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@2 (line 1)
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@396 (line 998)
  0x000002049625c560: 1c26 2507 

  0x000002049625c564: ;   {other}
  0x000002049625c564: 0f1f 8400 | 542d 0023 | 4885 c075 

  0x000002049625c570: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625c570: 1648 be90 | 5478 ea04 | 0200 0080 | 8e41 0100 | 0001 e9e7 

  0x000002049625c584: ;   {metadata('org/eclipse/equinox/log/Logger')}
  0x000002049625c584: 0000 0048 | bf70 1f09 | a804 0200 | 008b 7008 | 49ba 0000 | 00a7 0402 | 0000 4903 | f248 3b7e 
  0x000002049625c5a4: 200f 841a | 0000 0048 | 3bf7 0f84 | 1100 0000 

  0x000002049625c5b4: ;   {runtime_call slow_subtype_check Runtime1 stub}
  0x000002049625c5b4: 5657 e845 | 5330 075e | 5e85 f60f | 848d 0000 

  0x000002049625c5c4: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625c5c4: 0048 be90 | 5478 ea04 | 0200 008b | 7808 49ba | 0000 00a7 | 0402 0000 | 4903 fa48 | 3bbe 5801 
  0x000002049625c5e4: 0000 750d | 4883 8660 | 0100 0001 | e979 0000 | 0048 3bbe | 6801 0000 | 750d 4883 | 8670 0100 
  0x000002049625c604: 0001 e963 | 0000 0048 | 83be 5801 | 0000 0075 | 1748 89be | 5801 0000 | 48c7 8660 | 0100 0001 
  0x000002049625c624: 0000 00e9 | 4200 0000 | 4883 be68 | 0100 0000 | 7517 4889 | be68 0100 | 0048 c786 | 7001 0000 
  0x000002049625c644: 0100 0000 | e921 0000 | 00e9 1c00 

  0x000002049625c650: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625c650: 0000 48be | 9054 78ea | 0402 0000 | 4883 ae48 | 0100 0001 | e9a8 2d00 | 00e9 0000 | 0000 488b 
  0x000002049625c670: d048 3b02 

  0x000002049625c674: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625c674: 4c8b c248 | bec0 5078 | ea04 0200 | 0045 8b40 | 0849 ba00 | 0000 a704 | 0200 004d | 03c2 4c3b 
  0x000002049625c694: 8678 0100 | 0075 0d48 | 8386 8001 | 0000 01e9 | 6000 0000 | 4c3b 8688 | 0100 0075 | 0d48 8386 
  0x000002049625c6b4: 9001 0000 | 01e9 4a00 | 0000 4883 | be78 0100 | 0000 7517 | 4c89 8678 | 0100 0048 | c786 8001 
  0x000002049625c6d4: 0000 0100 | 0000 e929 | 0000 0048 | 83be 8801 | 0000 0075 | 174c 8986 | 8801 0000 | 48c7 8690 
  0x000002049625c6f4: 0100 0001 | 0000 00e9 | 0800 0000 | 4883 8668 | 0100 0001 | 4c8b 8424 | 0001 0000 | 0f1f 4400 
  0x000002049625c714: 0048 b8ff | ffff ffff 

  0x000002049625c71c: ;   {virtual_call}
  0x000002049625c71c: ffff ffe8 

  0x000002049625c720: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [224]=Oop }
                      ;*invokeinterface trace {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@17 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@396 (line 998)
  0x000002049625c720: 5c29 2507 

  0x000002049625c724: ;   {other}
  0x000002049625c724: 0f1f 8400 | 142f 0024 

  0x000002049625c72c: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625c72c: 49b8 c050 | 78ea 0402 | 0000 41ff | 80a0 0100 | 00e9 5200 | 0000 488b | 8424 0001 

  0x000002049625c748: ;   {oop(a 'java/lang/Class'{0x00000000ec827d58} = 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625c748: 0000 49b8 | 587d 82ec | 0000 0000 | 418b 90c8 | 0000 0048 | 3b02 4c8b 

  0x000002049625c760: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625c760: c248 bec0 | 5078 ea04 

  0x000002049625c768: ;   {metadata('java/io/PrintStream')}
  0x000002049625c768: 0200 0049 | ba60 a300 | a704 0200 | 004c 8996 | c801 0000 | 4883 86d0 | 0100 0001 

  0x000002049625c784: ;   {optimized virtual_call}
  0x000002049625c784: 4c8b c0e8 

  0x000002049625c788: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [224]=Oop }
                      ;*invokevirtual println {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@29 (line 232)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@396 (line 998)
  0x000002049625c788: f423 2507 

  0x000002049625c78c: ;   {other}
  0x000002049625c78c: 0f1f 8400 | 7c2f 0025 | 488b 9c24 | e000 0000 | 488b b424 | 9800 0000 | 4885 f675 

  0x000002049625c7a8: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625c7a8: 1648 bfb8 | 557a ea04 | 0200 0080 | 8f91 0c00 | 0001 e9cc 

  0x000002049625c7bc: ;   {metadata('org/osgi/framework/FrameworkEvent')}
  0x000002049625c7bc: 0000 0048 | b880 5611 | a804 0200 | 008b 5608 | 49ba 0000 | 00a7 0402 | 0000 4903 | d248 3bc2 
  0x000002049625c7dc: 0f85 8d00 

  0x000002049625c7e0: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625c7e0: 0000 48bf | b855 7aea | 0402 0000 | 8b46 0849 | ba00 0000 | a704 0200 | 0049 03c2 | 483b 87a8 
  0x000002049625c800: 0c00 0075 | 0d48 8387 | b00c 0000 | 01e9 7900 | 0000 483b | 87b8 0c00 | 0075 0d48 | 8387 c00c 
  0x000002049625c820: 0000 01e9 | 6300 0000 | 4883 bfa8 | 0c00 0000 | 7517 4889 | 87a8 0c00 | 0048 c787 | b00c 0000 
  0x000002049625c840: 0100 0000 | e942 0000 | 0048 83bf | b80c 0000 | 0075 1748 | 8987 b80c | 0000 48c7 | 87c0 0c00 
  0x000002049625c860: 0001 0000 | 00e9 2100 | 0000 e91c 

  0x000002049625c86c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625c86c: 0000 0048 | bfb8 557a | ea04 0200 | 0048 83af | 980c 0000 | 01e9 9e2b | 0000 e900 | 0000 004c 
  0x000002049625c88c: 8bc6 483b | 0348 8bd3 

  0x000002049625c894: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625c894: 48bf b855 | 7aea 0402 | 0000 8b52 | 0849 ba00 | 0000 a704 | 0200 0049 | 03d2 483b | 97e0 0c00 
  0x000002049625c8b4: 0075 0d48 | 8387 e80c | 0000 01e9 | 6000 0000 | 483b 97f0 | 0c00 0075 | 0d48 8387 | f80c 0000 
  0x000002049625c8d4: 01e9 4a00 | 0000 4883 | bfe0 0c00 | 0000 7517 | 4889 97e0 | 0c00 0048 | c787 e80c | 0000 0100 
  0x000002049625c8f4: 0000 e929 | 0000 0048 | 83bf f00c | 0000 0075 | 1748 8997 | f00c 0000 | 48c7 87f8 | 0c00 0001 
  0x000002049625c914: 0000 00e9 | 0800 0000 | 4883 87d0 | 0c00 0001 | 488b d366 | 0f1f 4400 | 0048 b8ff | ffff ffff 
  0x000002049625c934: ;   {virtual_call}
  0x000002049625c934: ffff ffe8 

  0x000002049625c938: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop }
                      ;*invokeinterface frameworkEvent {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@406 (line 1002)
  0x000002049625c938: 4427 2507 

  0x000002049625c93c: ;   {other}
  0x000002049625c93c: 0f1f 8400 | 2c31 0026 

  0x000002049625c944: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625c944: 48ba b855 | 7aea 0402 | 0000 ff82 | 080d 0000 | 488b 8424 | b000 0000 | e914 1200 | 0048 8bb4 
  0x000002049625c964: 2498 0000 | 004c 8b8c | 2490 0000 | 0048 85f6 

  0x000002049625c974: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625c974: 7516 48b8 | b855 7aea | 0402 0000 | 8088 e905 | 0000 01e9 | cd00 0000 

  0x000002049625c98c: ;   {metadata('org/osgi/framework/ServiceEvent')}
  0x000002049625c98c: 48b9 7817 | 10a8 0402 | 0000 8b46 | 0849 ba00 | 0000 a704 | 0200 0049 | 03c2 483b | 4840 0f85 
  0x000002049625c9ac: 8d00 0000 

  0x000002049625c9b0: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625c9b0: 48b8 b855 | 7aea 0402 | 0000 8b4e | 0849 ba00 | 0000 a704 | 0200 0049 | 03ca 483b | 8800 0600 
  0x000002049625c9d0: 0075 0d48 | 8380 0806 | 0000 01e9 | 7900 0000 | 483b 8810 | 0600 0075 | 0d48 8380 | 1806 0000 
  0x000002049625c9f0: 01e9 6300 | 0000 4883 | b800 0600 | 0000 7517 | 4889 8800 | 0600 0048 | c780 0806 | 0000 0100 
  0x000002049625ca10: 0000 e942 | 0000 0048 | 83b8 1006 | 0000 0075 | 1748 8988 | 1006 0000 | 48c7 8018 | 0600 0001 
  0x000002049625ca30: 0000 00e9 | 2100 0000 | e91c 0000 

  0x000002049625ca3c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625ca3c: 0048 b8b8 | 557a ea04 | 0200 0048 | 83a8 f005 | 0000 01e9 | de29 0000 | e900 0000 | 0048 8bde 
  0x000002049625ca5c: 4d85 c975 

  0x000002049625ca60: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625ca60: 1648 b8b8 | 557a ea04 | 0200 0080 | 8821 0600 | 0001 e9e9 

  0x000002049625ca74: ;   {metadata('org/osgi/framework/ServiceListener')}
  0x000002049625ca74: 0000 0048 | b920 1309 | a804 0200 | 0041 8b41 | 0849 ba00 | 0000 a704 | 0200 0049 | 03c2 483b 
  0x000002049625ca94: 4820 0f84 | 1a00 0000 | 483b c10f | 8411 0000 

  0x000002049625caa4: ;   {runtime_call slow_subtype_check Runtime1 stub}
  0x000002049625caa4: 0050 51e8 | 544e 3007 | 5858 85c0 | 0f84 8e00 

  0x000002049625cab4: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625cab4: 0000 48b8 | b855 7aea | 0402 0000 | 418b 4908 | 49ba 0000 | 00a7 0402 | 0000 4903 | ca48 3b88 
  0x000002049625cad4: 3806 0000 | 750d 4883 | 8040 0600 | 0001 e979 | 0000 0048 | 3b88 4806 | 0000 750d | 4883 8050 
  0x000002049625caf4: 0600 0001 | e963 0000 | 0048 83b8 | 3806 0000 | 0075 1748 | 8988 3806 | 0000 48c7 | 8040 0600 
  0x000002049625cb14: 0001 0000 | 00e9 4200 | 0000 4883 | b848 0600 | 0000 7517 | 4889 8848 | 0600 0048 | c780 5006 
  0x000002049625cb34: 0000 0100 | 0000 e921 | 0000 00e9 | 1c00 0000 

  0x000002049625cb44: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625cb44: 48b8 b855 | 7aea 0402 | 0000 4883 | a828 0600 | 0001 e9e0 | 2800 00e9 | 0000 0000 | 4d8b c18b 
  0x000002049625cb64: 7a18 0fbe | 7f11 85ff 

  0x000002049625cb6c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625cb6c: 48bf b855 | 7aea 0402 | 0000 48c7 | c070 0600 | 0075 0748 | c7c0 6006 | 0000 488b | 0c07 488d 
  0x000002049625cb8c: 4901 4889 | 0c07 0f85 | 0800 0000 | 498b d0e9 | 040f 0000 

  0x000002049625cba0: ;   {no_reloc}
  0x000002049625cba0: e9b3 2800 | 0000 0000 | 0000 498b | 87b8 0100 | 0048 8d78 | 1849 3bbf | c801 0000 | 0f87 a028 
  0x000002049625cbc0: 0000 4989 | bfb8 0100 | 0048 c700 | 0100 0000 | 488b ca49 | ba00 0000 | a704 0200 | 0049 2bca 
  0x000002049625cbe0: 8948 0848 | 33c9 8948 | 0c48 33c9 | 4889 4810 | 4889 8424 | 0801 0000 

  0x000002049625cbf8: ; implicit exception: dispatches to 0x000002049625f46f
  0x000002049625cbf8: 493b 0049 

  0x000002049625cbfc: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625cbfc: 8bd0 48be | b855 7aea | 0402 0000 | 4883 8680 | 0600 0001 | 418b 5008 | 49ba 0000 | 00a7 0402 
  0x000002049625cc1c: 0000 4903 | d248 8b52 | 7048 8b12 

  0x000002049625cc28: ; implicit exception: dispatches to 0x000002049625f474
  0x000002049625cc28: 483b 0248 

  0x000002049625cc2c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625cc2c: 8bf2 48bf | b855 7aea | 0402 0000 | 4883 87b8 | 0600 0001 

  0x000002049625cc40: ;   {metadata(method data for {method} {0x00000204a7465ac0} 'getName' '()Ljava/lang/String;' in 'java/lang/Class')}
  0x000002049625cc40: 48be 0072 | 08ea 0402 | 0000 8bbe | cc00 0000 | 83c7 0289 | becc 0000 | 0081 e7fe | ff1f 0085 
  0x000002049625cc60: ff0f 8412 | 2800 008b | 722c 4885 

  0x000002049625cc6c: ;   {metadata(method data for {method} {0x00000204a7465ac0} 'getName' '()Ljava/lang/String;' in 'java/lang/Class')}
  0x000002049625cc6c: f648 bf00 | 7208 ea04 | 0200 0048 | c7c1 1001 | 0000 7407 | 48c7 c120 | 0100 004c | 8b2c 0f4d 
  0x000002049625cc8c: 8d6d 014c | 892c 0f0f | 8425 0000 

  0x000002049625cc98: ;   {metadata(method data for {method} {0x00000204a7465ac0} 'getName' '()Ljava/lang/String;' in 'java/lang/Class')}
  0x000002049625cc98: 0048 ba00 | 7208 ea04 | 0200 00ff | 8230 0100 | 004c 8984 | 2410 0100 | 0048 899c | 2418 0100 
  0x000002049625ccb8: 00e9 3900 | 0000 4c89 | 8424 1001 | 0000 4889 | 9c24 1801 | 0000 488b 

  0x000002049625ccd0: ;   {metadata(method data for {method} {0x00000204a7465ac0} 'getName' '()Ljava/lang/String;' in 'java/lang/Class')}
  0x000002049625ccd0: ca49 b900 | 7208 ea04 | 0200 0049 | 8381 4801 | 0000 010f 

  0x000002049625cce4: ;   {optimized virtual_call}
  0x000002049625cce4: 1f40 00e8 

  0x000002049625cce8: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [264]=Oop [272]=Oop [280]=Oop }
                      ;*invokevirtual initClassName {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.Class::getName@14
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@206 (line 984)
  0x000002049625cce8: 941e 2507 

  0x000002049625ccec: ;   {other}
  0x000002049625ccec: 0f1f 8400 | dc34 0027 

  0x000002049625ccf4: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625ccf4: 488b f048 | bab8 557a | ea04 0200 | 0048 8382 | f006 0000 

  0x000002049625cd08: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625cd08: 0148 ba08 | a00e ea04 | 0200 008b | bacc 0000 | 0083 c702 | 89ba cc00 | 0000 81e7 | feff 1f00 
  0x000002049625cd28: 85ff 0f84 | 6a27 0000 

  0x000002049625cd30: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625cd30: 4885 f648 | ba08 a00e | ea04 0200 | 0048 c7c7 | 1001 0000 | 7507 48c7 | c720 0100 | 0048 8b1c 
  0x000002049625cd50: 3a48 8d5b | 0148 891c | 3a0f 851f 

  0x000002049625cd5c: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625cd5c: 0000 0048 | ba08 a00e | ea04 0200 | 00ff 8230 

  0x000002049625cd6c: ;   {oop("null"{0x00000000c0004000})}
  0x000002049625cd6c: 0100 0048 | be00 4000 | c000 0000 | 00e9 b900 | 0000 483b | 0648 8bd6 

  0x000002049625cd84: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625cd84: 48bf 08a0 | 0eea 0402 | 0000 8b52 | 0849 ba00 | 0000 a704 | 0200 0049 | 03d2 483b | 9758 0100 
  0x000002049625cda4: 0075 0d48 | 8387 6001 | 0000 01e9 | 6000 0000 | 483b 9768 | 0100 0075 | 0d48 8387 | 7001 0000 
  0x000002049625cdc4: 01e9 4a00 | 0000 4883 | bf58 0100 | 0000 7517 | 4889 9758 | 0100 0048 | c787 6001 | 0000 0100 
  0x000002049625cde4: 0000 e929 | 0000 0048 | 83bf 6801 | 0000 0075 | 1748 8997 | 6801 0000 | 48c7 8770 | 0100 0001 
  0x000002049625ce04: 0000 00e9 | 0800 0000 | 4883 8748 | 0100 0001 | 488b d666 | 0f1f 4400 | 0048 b8ff | ffff ffff 
  0x000002049625ce24: ;   {virtual_call}
  0x000002049625ce24: ffff ffe8 

  0x000002049625ce28: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [264]=Oop [272]=Oop [280]=Oop }
                      ;*invokevirtual toString {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::valueOf@11
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@209 (line 984)
  0x000002049625ce28: 5422 2507 

  0x000002049625ce2c: ;   {other}
  0x000002049625ce2c: 0f1f 8400 | 1c36 0028 | 488b f048 | 8b84 2408 | 0100 004c 

  0x000002049625ce40: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625ce40: 8bc0 48bf | b855 7aea | 0402 0000 | 4883 8700 | 0700 0001 

  0x000002049625ce54: ;   {metadata(method data for {method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625ce54: 49b8 c8f6 | 2fea 0402 | 0000 418b | b8cc 0000 | 0083 c702 | 4189 b8cc | 0000 0081 | e7fe ff1f 
  0x000002049625ce74: 0085 ff0f | 8443 2600 | 004c 8bc0 

  0x000002049625ce80: ;   {metadata(method data for {method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625ce80: 48bf c8f6 | 2fea 0402 | 0000 4883 | 8710 0100 | 0001 4c8b | c648 8bd0 | 0f1f 8000 

  0x000002049625ce9c: ;   {optimized virtual_call}
  0x000002049625ce9c: 0000 00e8 

  0x000002049625cea0: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [264]=Oop [272]=Oop [280]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::<init>@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@212 (line 984)
  0x000002049625cea0: dc1c 2507 

  0x000002049625cea4: ;   {other}
  0x000002049625cea4: 0f1f 8400 | 9436 0029 | 488b 8424 | 0801 0000 

  0x000002049625ceb4: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625ceb4: 49b8 b855 | 7aea 0402 | 0000 4983 | 8010 0700 

  0x000002049625cec4: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625cec4: 0001 49b8 | 60b2 00ea | 0402 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x000002049625cee4: ff1f 0085 | d20f 84f2 | 2500 0048 | 8b84 2408 

  0x000002049625cef4: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625cef4: 0100 0049 | b860 b200 | ea04 0200 | 0049 8380 | 1001 0000 

  0x000002049625cf08: ;   {oop("@"{0x00000000c0006bf0})}
  0x000002049625cf08: 0149 b8f0 | 6b00 c000 | 0000 0048 | 8b94 2408 | 0100 000f 

  0x000002049625cf1c: ;   {optimized virtual_call}
  0x000002049625cf1c: 1f40 00e8 

  0x000002049625cf20: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [264]=Oop [272]=Oop [280]=Oop }
                      ;*invokespecial append {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@218 (line 984)
  0x000002049625cf20: 5c1c 2507 

  0x000002049625cf24: ;   {other}
  0x000002049625cf24: 0f1f 8400 | 1437 002a 

  0x000002049625cf2c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625cf2c: 48ba b855 | 7aea 0402 | 0000 4883 | 8248 0700 | 0001 488b | 9424 1001 

  0x000002049625cf44: ;   {static_call}
  0x000002049625cf44: 0000 90e8 

  0x000002049625cf48: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [264]=Oop [272]=Oop [280]=Oop }
                      ;*invokestatic identityHashCode {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@223 (line 985)
  0x000002049625cf48: 3426 2507 

  0x000002049625cf4c: ;   {other}
  0x000002049625cf4c: 0f1f 8400 | 3c37 002b 

  0x000002049625cf54: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625cf54: 48ba b855 | 7aea 0402 | 0000 4883 | 8258 0700 

  0x000002049625cf64: ;   {metadata(method data for {method} {0x00000204a74126c8} 'toHexString' '(I)Ljava/lang/String;' in 'java/lang/Integer')}
  0x000002049625cf64: 0001 48ba | a05c 2fea | 0402 0000 | 448b 82cc | 0000 0041 | 83c0 0244 | 8982 cc00 | 0000 4181 
  0x000002049625cf84: e0fe ff1f | 0045 85c0 | 0f84 7025 

  0x000002049625cf90: ;   {metadata(method data for {method} {0x00000204a74126c8} 'toHexString' '(I)Ljava/lang/String;' in 'java/lang/Integer')}
  0x000002049625cf90: 0000 48ba | a05c 2fea | 0402 0000 | 4883 8210 | 0100 0001 | 488b d041 | b804 0000 

  0x000002049625cfac: ;   {static_call}
  0x000002049625cfac: 0066 90e8 

  0x000002049625cfb0: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [264]=Oop [272]=Oop [280]=Oop }
                      ;*invokestatic toUnsignedString0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.Integer::toHexString@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@226 (line 985)
  0x000002049625cfb0: cc25 2507 

  0x000002049625cfb4: ;   {other}
  0x000002049625cfb4: 0f1f 8400 | a437 002c | 4c8b 8424 | 0801 0000 

  0x000002049625cfc4: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625cfc4: 48ba b855 | 7aea 0402 | 0000 4883 | 8268 0700 

  0x000002049625cfd4: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625cfd4: 0001 49b8 | 60b2 00ea | 0402 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x000002049625cff4: ff1f 0085 | d20f 8424 | 2500 004c | 8b84 2408 

  0x000002049625d004: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625d004: 0100 0048 | ba60 b200 | ea04 0200 | 0048 8382 | 1001 0000 | 014c 8bc0 | 488b 9424 | 0801 0000 
  0x000002049625d024: ;   {optimized virtual_call}
  0x000002049625d024: 6666 90e8 

  0x000002049625d028: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [264]=Oop [272]=Oop [280]=Oop }
                      ;*invokespecial append {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@229 (line 985)
  0x000002049625d028: 541b 2507 

  0x000002049625d02c: ;   {other}
  0x000002049625d02c: 0f1f 8400 | 1c38 002d | 488b 8424 | 0801 0000 

  0x000002049625d03c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625d03c: 48ba b855 | 7aea 0402 | 0000 4883 | 82a0 0700 

  0x000002049625d04c: ;   {metadata(method data for {method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625d04c: 0001 48ba | a8ba 00ea | 0402 0000 | 8bb2 cc00 | 0000 83c6 | 0289 b2cc | 0000 0081 | e6fe ff1f 
  0x000002049625d06c: 0085 f60f | 84cf 2400 

  0x000002049625d074: ;   {metadata('java/lang/String')}
  0x000002049625d074: 0048 ba40 | e700 a704 | 0200 0049 | 8b87 b801 | 0000 488d | 7818 493b | bfc8 0100 | 000f 87ce 
  0x000002049625d094: 2400 0049 | 89bf b801 | 0000 48c7 | 0001 0000 | 0048 8bca | 49ba 0000 | 00a7 0402 | 0000 492b 
  0x000002049625d0b4: ca89 4808 | 4833 c989 | 480c 4833 | c948 8948 | 1048 8984 | 2420 0100 | 004c 8bc0 

  0x000002049625d0d0: ;   {metadata(method data for {method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625d0d0: 49b9 a8ba | 00ea 0402 | 0000 4983 | 8110 0100 

  0x000002049625d0e0: ;   {metadata(method data for {method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625d0e0: 0001 49b8 | f0bb 00ea | 0402 0000 | 458b 88cc | 0000 0041 | 83c1 0245 | 8988 cc00 | 0000 4181 
  0x000002049625d100: e1fe ff1f | 0045 85c9 | 0f84 6424 | 0000 4c8b 

  0x000002049625d110: ;   {metadata(method data for {method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625d110: c049 b9f0 | bb00 ea04 | 0200 0049 | 8381 1001 | 0000 014c | 8b84 2408 

  0x000002049625d128: ;   {oop(nullptr)}
  0x000002049625d128: 0100 0049 | b900 0000 | 0000 0000 | 0048 8bd0 | 0f1f 8000 

  0x000002049625d13c: ;   {optimized virtual_call}
  0x000002049625d13c: 0000 00e8 

  0x000002049625d140: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [288]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::<init>@3
                      ; - java.lang.StringBuilder::toString@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@232 (line 984)
  0x000002049625d140: 3c1a 2507 

  0x000002049625d144: ;   {other}
  0x000002049625d144: 0f1f 8400 | 3439 002e | 488b 9424 | 8000 0000 | 8b5a 1848 | 899c 2430 | 0100 0090 

  0x000002049625d160: ;   {no_reloc}
  0x000002049625d160: e93d 2400 | 0000 0000 | 0000 498b | 87b8 0100 | 0048 8d78 | 1849 3bbf | c801 0000 | 0f87 2a24 
  0x000002049625d180: 0000 4989 | bfb8 0100 | 0048 c700 | 0100 0000 | 488b ca49 | ba00 0000 | a704 0200 | 0049 2bca 
  0x000002049625d1a0: 8948 0848 | 33c9 8948 | 0c48 33c9 | 4889 4810 | 4889 8424 | 2801 0000 

  0x000002049625d1b8: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625d1b8: 4c8b c048 | bab8 557a | ea04 0200 | 0048 8382 | d807 0000 

  0x000002049625d1cc: ;   {metadata(method data for {method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625d1cc: 0149 b8c8 | f62f ea04 | 0200 0041 | 8b90 cc00 | 0000 83c2 | 0241 8990 | cc00 0000 | 81e2 feff 
  0x000002049625d1ec: 1f00 85d2 | 0f84 c323 | 0000 4c8b 

  0x000002049625d1f8: ;   {metadata(method data for {method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625d1f8: c048 bac8 | f62f ea04 | 0200 0048 | 8382 1001 

  0x000002049625d208: ;   {oop("dispatchServiceEvent["{0x00000000eabca2b0})}
  0x000002049625d208: 0000 0149 | b8b0 a2bc | ea00 0000 | 0048 8bd0 | 0f1f 8000 

  0x000002049625d21c: ;   {optimized virtual_call}
  0x000002049625d21c: 0000 00e8 

  0x000002049625d220: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [288]=Oop [296]=Oop [304]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::<init>@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@251 (line 986)
  0x000002049625d220: 5c19 2507 

  0x000002049625d224: ;   {other}
  0x000002049625d224: 0f1f 8400 | 143a 002f | 488b 9424 | 8000 0000 | 8b72 1048 | 8b84 2428 

  0x000002049625d23c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625d23c: 0100 0048 | bfb8 557a | ea04 0200 | 0048 8387 | e807 0000 

  0x000002049625d250: ;   {metadata(method data for {method} {0x00000204a745dad0} 'append' '(Ljava/lang/Object;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625d250: 0148 bf68 | 9e0e ea04 | 0200 008b | 9fcc 0000 | 0083 c302 | 899f cc00 | 0000 81e3 | feff 1f00 
  0x000002049625d270: 85db 0f84 | 6223 0000 

  0x000002049625d278: ;   {metadata(method data for {method} {0x00000204a745dad0} 'append' '(Ljava/lang/Object;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625d278: 48bf 689e | 0eea 0402 | 0000 4883 | 8710 0100 

  0x000002049625d288: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625d288: 0001 48bf | 08a0 0eea | 0402 0000 | 8b9f cc00 | 0000 83c3 | 0289 9fcc | 0000 0081 | e3fe ff1f 
  0x000002049625d2a8: 0085 db0f | 844a 2300 | 0048 85f6 

  0x000002049625d2b4: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625d2b4: 48bf 08a0 | 0eea 0402 | 0000 48c7 | c310 0100 | 0075 0748 | c7c3 2001 | 0000 488b | 041f 488d 
  0x000002049625d2d4: 4001 4889 | 041f 0f85 | 1f00 0000 

  0x000002049625d2e0: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625d2e0: 48be 08a0 | 0eea 0402 | 0000 ff86 | 3001 0000 

  0x000002049625d2f0: ;   {oop("null"{0x00000000c0004000})}
  0x000002049625d2f0: 48be 0040 | 00c0 0000 | 0000 e9b0 | 0000 0048 | 3b06 488b 

  0x000002049625d304: ;   {metadata(method data for {method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625d304: fe48 bb08 | a00e ea04 | 0200 008b | 7f08 49ba | 0000 00a7 | 0402 0000 | 4903 fa48 | 3bbb 5801 
  0x000002049625d324: 0000 750d | 4883 8360 | 0100 0001 | e960 0000 | 0048 3bbb | 6801 0000 | 750d 4883 | 8370 0100 
  0x000002049625d344: 0001 e94a | 0000 0048 | 83bb 5801 | 0000 0075 | 1748 89bb | 5801 0000 | 48c7 8360 | 0100 0001 
  0x000002049625d364: 0000 00e9 | 2900 0000 | 4883 bb68 | 0100 0000 | 7517 4889 | bb68 0100 | 0048 c783 | 7001 0000 
  0x000002049625d384: 0100 0000 | e908 0000 | 0048 8383 | 4801 0000 | 0148 8bd6 | 0f1f 8000 

  0x000002049625d39c: ;   {optimized virtual_call}
  0x000002049625d39c: 0000 00e8 

  0x000002049625d3a0: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [288]=Oop [296]=Oop [304]=Oop }
                      ;*invokevirtual toString {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::valueOf@11
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@258 (line 986)
  0x000002049625d3a0: dc17 2507 

  0x000002049625d3a4: ;   {other}
  0x000002049625d3a4: 0f1f 8400 | 943b 0030 | 488b f048 | 8b84 2428 

  0x000002049625d3b4: ;   {metadata(method data for {method} {0x00000204a745dad0} 'append' '(Ljava/lang/Object;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625d3b4: 0100 0049 | b868 9e0e | ea04 0200 | 0049 8380 | 2001 0000 

  0x000002049625d3c8: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625d3c8: 0149 b860 | b200 ea04 | 0200 0041 | 8b90 cc00 | 0000 83c2 | 0241 8990 | cc00 0000 | 81e2 feff 
  0x000002049625d3e8: 1f00 85d2 | 0f84 2f22 | 0000 488b | 8424 2801 

  0x000002049625d3f8: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625d3f8: 0000 49b8 | 60b2 00ea | 0402 0000 | 4983 8010 | 0100 0001 | 4c8b c648 | 8b94 2428 

  0x000002049625d414: ;   {optimized virtual_call}
  0x000002049625d414: 0100 00e8 

  0x000002049625d418: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [288]=Oop [296]=Oop [304]=Oop }
                      ;*invokespecial append {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::append@2
                      ; - java.lang.StringBuilder::append@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@258 (line 986)
  0x000002049625d418: 6417 2507 

  0x000002049625d41c: ;   {other}
  0x000002049625d41c: 0f1f 8400 | 0c3c 0031 | 488b 8424 | 2801 0000 

  0x000002049625d42c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625d42c: 4c8b c048 | bab8 557a | ea04 0200 | 0048 8382 | 2008 0000 

  0x000002049625d440: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625d440: 0149 b860 | b200 ea04 | 0200 0041 | 8b90 cc00 | 0000 83c2 | 0241 8990 | cc00 0000 | 81e2 feff 
  0x000002049625d460: 1f00 85d2 | 0f84 d821 | 0000 4c8b 

  0x000002049625d46c: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625d46c: c048 ba60 | b200 ea04 | 0200 0048 | 8382 1001 

  0x000002049625d47c: ;   {oop("]("{0x00000000eabca280})}
  0x000002049625d47c: 0000 0149 | b880 a2bc | ea00 0000 | 0048 8bd0 

  0x000002049625d48c: ;   {optimized virtual_call}
  0x000002049625d48c: 6666 90e8 

  0x000002049625d490: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [288]=Oop [296]=Oop [304]=Oop }
                      ;*invokespecial append {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@264 (line 986)
  0x000002049625d490: ec16 2507 

  0x000002049625d494: ;   {other}
  0x000002049625d494: 0f1f 8400 | 843c 0032 | 488b 8424 | 2801 0000 

  0x000002049625d4a4: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625d4a4: 49b8 b855 | 7aea 0402 | 0000 4983 | 8058 0800 

  0x000002049625d4b4: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625d4b4: 0001 49b8 | 60b2 00ea | 0402 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x000002049625d4d4: ff1f 0085 | d20f 8484 | 2100 0048 | 8b84 2428 

  0x000002049625d4e4: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625d4e4: 0100 0049 | b860 b200 | ea04 0200 | 0049 8380 | 1001 0000 | 014c 8b84 | 2420 0100 | 0048 8b94 
  0x000002049625d504: 2428 0100 | 0066 0f1f 

  0x000002049625d50c: ;   {optimized virtual_call}
  0x000002049625d50c: 4400 00e8 

  0x000002049625d510: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [296]=Oop [304]=Oop }
                      ;*invokespecial append {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@269 (line 986)
  0x000002049625d510: 6c16 2507 

  0x000002049625d514: ;   {other}
  0x000002049625d514: 0f1f 8400 | 043d 0033 | 488b 8424 | 2801 0000 

  0x000002049625d524: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625d524: 49b8 b855 | 7aea 0402 | 0000 4983 | 8090 0800 

  0x000002049625d534: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625d534: 0001 49b8 | 60b2 00ea | 0402 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x000002049625d554: ff1f 0085 | d20f 8425 | 2100 0048 | 8b84 2428 

  0x000002049625d564: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625d564: 0100 0049 | b860 b200 | ea04 0200 | 0049 8380 | 1001 0000 

  0x000002049625d578: ;   {oop(")"{0x00000000c00004b0})}
  0x000002049625d578: 0149 b8b0 | 0400 c000 | 0000 0048 | 8b94 2428 | 0100 000f 

  0x000002049625d58c: ;   {optimized virtual_call}
  0x000002049625d58c: 1f40 00e8 

  0x000002049625d590: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [296]=Oop [304]=Oop }
                      ;*invokespecial append {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@275 (line 986)
  0x000002049625d590: ec15 2507 

  0x000002049625d594: ;   {other}
  0x000002049625d594: 0f1f 8400 | 843d 0034 | 488b 8424 | 2801 0000 

  0x000002049625d5a4: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625d5a4: 48ba b855 | 7aea 0402 | 0000 4883 | 82c8 0800 

  0x000002049625d5b4: ;   {metadata(method data for {method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625d5b4: 0001 48ba | a8ba 00ea | 0402 0000 | 8bb2 cc00 | 0000 83c6 | 0289 b2cc | 0000 0081 | e6fe ff1f 
  0x000002049625d5d4: 0085 f60f | 84c8 2000 

  0x000002049625d5dc: ;   {metadata('java/lang/String')}
  0x000002049625d5dc: 0048 ba40 | e700 a704 | 0200 0049 | 8b87 b801 | 0000 488d | 7818 493b | bfc8 0100 | 000f 87c7 
  0x000002049625d5fc: 2000 0049 | 89bf b801 | 0000 48c7 | 0001 0000 | 0048 8bca | 49ba 0000 | 00a7 0402 | 0000 492b 
  0x000002049625d61c: ca89 4808 | 4833 c989 | 480c 4833 | c948 8948 | 104c 8bc0 

  0x000002049625d630: ;   {metadata(method data for {method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625d630: 49b9 a8ba | 00ea 0402 | 0000 4983 | 8110 0100 

  0x000002049625d640: ;   {metadata(method data for {method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625d640: 0001 49b8 | f0bb 00ea | 0402 0000 | 458b 88cc | 0000 0041 | 83c1 0245 | 8988 cc00 | 0000 4181 
  0x000002049625d660: e1fe ff1f | 0045 85c9 | 0f84 6520 | 0000 4c8b 

  0x000002049625d670: ;   {metadata(method data for {method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625d670: c049 b9f0 | bb00 ea04 | 0200 0049 | 8381 1001 | 0000 014c | 8b84 2428 

  0x000002049625d688: ;   {oop(nullptr)}
  0x000002049625d688: 0100 0049 | b900 0000 | 0000 0000 | 0048 8bd0 | 4889 8424 | 3801 0000 | 0f1f 8000 

  0x000002049625d6a4: ;   {optimized virtual_call}
  0x000002049625d6a4: 0000 00e8 

  0x000002049625d6a8: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [304]=Oop [312]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::<init>@3
                      ; - java.lang.StringBuilder::toString@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@278 (line 986)
  0x000002049625d6a8: d414 2507 

  0x000002049625d6ac: ;   {other}
  0x000002049625d6ac: 0f1f 8400 | 9c3e 0035 | 488b 9c24 | 3001 0000 

  0x000002049625d6bc: ; implicit exception: dispatches to 0x000002049625f6f4
  0x000002049625d6bc: 483b 034c 

  0x000002049625d6c0: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625d6c0: 8bc3 49b9 | b855 7aea | 0402 0000 

  0x000002049625d6cc: ;   {metadata('org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625d6cc: 49ba 0000 | 09a8 0402 | 0000 4d89 | 9110 0900 | 0049 8381 | 1809 0000 

  0x000002049625d6e4: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625d6e4: 0149 b8c0 | 5078 ea04 | 0200 0045 | 8b88 cc00 | 0000 4183 | c102 4589 | 88cc 0000 | 0041 81e1 
  0x000002049625d704: feff 1f00 | 4585 c90f | 84e8 1f00 | 008b 5320 

  0x000002049625d714: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625d714: 4885 d249 | b8c0 5078 | ea04 0200 | 0049 c7c1 | 1001 0000 | 7407 49c7 | c120 0100 | 004b 8b34 
  0x000002049625d734: 0848 8d76 | 014b 8934 | 080f 84ff | 0200 0048 | 85d2 0f84 | 2900 0000 

  0x000002049625d74c: ;   {metadata('org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625d74c: 48bf 884a | 09a8 0402 | 0000 8b72 | 0849 ba00 | 0000 a704 | 0200 0049 | 03f2 483b | 7e38 0f85 
  0x000002049625d76c: aa1f 0000 | e900 0000 | 004c 8bc2 

  0x000002049625d778: ; implicit exception: dispatches to 0x000002049625f71f
  0x000002049625d778: 483b 024c 

  0x000002049625d77c: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625d77c: 8bc2 49b9 | c050 78ea | 0402 0000 

  0x000002049625d788: ;   {metadata('org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625d788: 49ba 884a | 09a8 0402 | 0000 4d89 | 9140 0100 | 0049 8381 | 4801 0000 

  0x000002049625d7a0: ;   {metadata(method data for {method} {0x00000204ea507238} 'getLogger' '(Ljava/lang/String;)Lorg/osgi/service/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625d7a0: 0149 b800 | 5378 ea04 | 0200 0045 | 8b88 cc00 | 0000 4183 | c102 4589 | 88cc 0000 | 0041 81e1 
  0x000002049625d7c0: feff 1f00 | 4585 c90f | 8457 1f00 | 004c 8bc2 

  0x000002049625d7d0: ;   {metadata(method data for {method} {0x00000204ea507238} 'getLogger' '(Ljava/lang/String;)Lorg/osgi/service/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625d7d0: 49b9 0053 | 78ea 0402 

  0x000002049625d7d8: ;   {metadata('org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625d7d8: 0000 49ba | 884a 09a8 | 0402 0000 | 4d89 9120 | 0100 0049 | 8381 2801 

  0x000002049625d7f0: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625d7f0: 0000 0149 | b890 5478 | ea04 0200 | 0045 8b88 | cc00 0000 | 4183 c102 | 4589 88cc | 0000 0041 
  0x000002049625d810: 81e1 feff | 1f00 4585 | c90f 8426 | 1f00 004c 

  0x000002049625d820: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625d820: 8bc2 49b9 | 9054 78ea | 0402 0000 

  0x000002049625d82c: ;   {metadata('org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625d82c: 49ba 884a | 09a8 0402 | 0000 4d89 | 9120 0100 | 0049 8381 | 2801 0000 

  0x000002049625d844: ;   {oop("org.eclipse.osgi/debug/events"{0x00000000c0064c78})}
  0x000002049625d844: 0149 b878 | 4c06 c000 

  0x000002049625d84c: ;   {oop(a 'java/lang/Class'{0x00000000ec830698} = 'org/osgi/service/log/Logger')}
  0x000002049625d84c: 0000 0049 | b998 0683 | ec00 0000 | 0066 0f1f 

  0x000002049625d85c: ;   {optimized virtual_call}
  0x000002049625d85c: 4400 00e8 

  0x000002049625d860: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [312]=Oop }
                      ;*invokevirtual getLogger {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@4 (line 73)
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@2 (line 1)
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@281 (line 986)
  0x000002049625d860: 1c13 2507 

  0x000002049625d864: ;   {other}
  0x000002049625d864: 0f1f 8400 | 5440 0036 | 4885 c075 

  0x000002049625d870: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625d870: 1648 be90 | 5478 ea04 | 0200 0080 | 8e41 0100 | 0001 e9e7 

  0x000002049625d884: ;   {metadata('org/eclipse/equinox/log/Logger')}
  0x000002049625d884: 0000 0048 | bf70 1f09 | a804 0200 | 008b 7008 | 49ba 0000 | 00a7 0402 | 0000 4903 | f248 3b7e 
  0x000002049625d8a4: 200f 841a | 0000 0048 | 3bf7 0f84 | 1100 0000 

  0x000002049625d8b4: ;   {runtime_call slow_subtype_check Runtime1 stub}
  0x000002049625d8b4: 5657 e845 | 4030 075e | 5e85 f60f | 848d 0000 

  0x000002049625d8c4: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625d8c4: 0048 be90 | 5478 ea04 | 0200 008b | 7808 49ba | 0000 00a7 | 0402 0000 | 4903 fa48 | 3bbe 5801 
  0x000002049625d8e4: 0000 750d | 4883 8660 | 0100 0001 | e979 0000 | 0048 3bbe | 6801 0000 | 750d 4883 | 8670 0100 
  0x000002049625d904: 0001 e963 | 0000 0048 | 83be 5801 | 0000 0075 | 1748 89be | 5801 0000 | 48c7 8660 | 0100 0001 
  0x000002049625d924: 0000 00e9 | 4200 0000 | 4883 be68 | 0100 0000 | 7517 4889 | be68 0100 | 0048 c786 | 7001 0000 
  0x000002049625d944: 0100 0000 | e921 0000 | 00e9 1c00 

  0x000002049625d950: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625d950: 0000 48be | 9054 78ea | 0402 0000 | 4883 ae48 | 0100 0001 | e9fd 1d00 | 00e9 0000 | 0000 488b 
  0x000002049625d970: d048 3b02 

  0x000002049625d974: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625d974: 4c8b c248 | bec0 5078 | ea04 0200 | 0045 8b40 | 0849 ba00 | 0000 a704 | 0200 004d | 03c2 4c3b 
  0x000002049625d994: 8678 0100 | 0075 0d48 | 8386 8001 | 0000 01e9 | 6000 0000 | 4c3b 8688 | 0100 0075 | 0d48 8386 
  0x000002049625d9b4: 9001 0000 | 01e9 4a00 | 0000 4883 | be78 0100 | 0000 7517 | 4c89 8678 | 0100 0048 | c786 8001 
  0x000002049625d9d4: 0000 0100 | 0000 e929 | 0000 0048 | 83be 8801 | 0000 0075 | 174c 8986 | 8801 0000 | 48c7 8690 
  0x000002049625d9f4: 0100 0001 | 0000 00e9 | 0800 0000 | 4883 8668 | 0100 0001 | 4c8b 8424 | 3801 0000 | 0f1f 4400 
  0x000002049625da14: 0048 b8ff | ffff ffff 

  0x000002049625da1c: ;   {virtual_call}
  0x000002049625da1c: ffff ffe8 

  0x000002049625da20: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop }
                      ;*invokeinterface trace {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@17 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@281 (line 986)
  0x000002049625da20: 5c16 2507 

  0x000002049625da24: ;   {other}
  0x000002049625da24: 0f1f 8400 | 1442 0037 

  0x000002049625da2c: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625da2c: 49b8 c050 | 78ea 0402 | 0000 41ff | 80a0 0100 | 00e9 5200 | 0000 488b | 8424 3801 

  0x000002049625da48: ;   {oop(a 'java/lang/Class'{0x00000000ec827d58} = 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625da48: 0000 49b8 | 587d 82ec | 0000 0000 | 418b 90c8 | 0000 0048 | 3b02 4c8b 

  0x000002049625da60: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625da60: c248 bec0 | 5078 ea04 

  0x000002049625da68: ;   {metadata('java/io/PrintStream')}
  0x000002049625da68: 0200 0049 | ba60 a300 | a704 0200 | 004c 8996 | c801 0000 | 4883 86d0 | 0100 0001 

  0x000002049625da84: ;   {optimized virtual_call}
  0x000002049625da84: 4c8b c0e8 

  0x000002049625da88: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop }
                      ;*invokevirtual println {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@29 (line 232)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@281 (line 986)
  0x000002049625da88: f410 2507 

  0x000002049625da8c: ;   {other}
  0x000002049625da8c: 0f1f 8400 | 7c42 0038 | 488b 9424 | 1001 0000 | 488b 9c24 | 1801 0000 

  0x000002049625daa4: ; implicit exception: dispatches to 0x000002049625f779
  0x000002049625daa4: 483b 024c 

  0x000002049625daa8: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625daa8: 8bc2 48be | b855 7aea | 0402 0000 | 458b 4008 | 49ba 0000 | 00a7 0402 | 0000 4d03 | c24c 3b86 
  0x000002049625dac8: 4809 0000 | 750d 4883 | 8650 0900 | 0001 e960 | 0000 004c | 3b86 5809 | 0000 750d | 4883 8660 
  0x000002049625dae8: 0900 0001 | e94a 0000 | 0048 83be | 4809 0000 | 0075 174c | 8986 4809 | 0000 48c7 | 8650 0900 
  0x000002049625db08: 0001 0000 | 00e9 2900 | 0000 4883 | be58 0900 | 0000 7517 | 4c89 8658 | 0900 0048 | c786 6009 
  0x000002049625db28: 0000 0100 | 0000 e908 | 0000 0048 | 8386 3809 | 0000 014c | 8bc3 0f1f | 8000 0000 | 0048 b8f8 
  0x000002049625db48: 0e10 a804 

  0x000002049625db4c: ;   {virtual_call}
  0x000002049625db4c: 0200 00e8 

  0x000002049625db50: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop }
                      ;*invokeinterface serviceChanged {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@288 (line 988)
  0x000002049625db50: 4c5d 0000 

  0x000002049625db54: ;   {other}
  0x000002049625db54: 0f1f 8400 | 4443 0039 

  0x000002049625db5c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625db5c: 49b8 b855 | 7aea 0402 | 0000 41ff | 8070 0900 | 0048 8b84 | 24b0 0000 

  0x000002049625db74: ;   {oop(a 'java/lang/Boolean'{0x00000000c0005570} = false)}
  0x000002049625db74: 0049 ba70 | 5500 c000 | 0000 0049 

  0x000002049625db80: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625db80: 3bc2 49b8 | b855 7aea | 0402 0000 | 48c7 c2f0 | 1000 0074 | 0748 c7c2 | 0011 0000 | 498b 3410 
  0x000002049625dba0: 488d 7601 | 4989 3410 | 0f84 e60e 

  0x000002049625dbac: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625dbac: 0000 49b8 | b855 7aea | 0402 0000 | 4983 8010 | 1100 0001 | 4885 c075 

  0x000002049625dbc4: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625dbc4: 1648 beb8 | 557a ea04 | 0200 0080 | 8e19 1100 | 0001 e9cd 

  0x000002049625dbd8: ;   {metadata('java/lang/ClassLoader')}
  0x000002049625dbd8: 0000 0048 | bf48 3d01 | a704 0200 | 008b 7008 | 49ba 0000 | 00a7 0402 | 0000 4903 | f248 3b7e 
  0x000002049625dbf8: 380f 858d 

  0x000002049625dbfc: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625dbfc: 0000 0048 | beb8 557a | ea04 0200 | 008b 7808 | 49ba 0000 | 00a7 0402 | 0000 4903 | fa48 3bbe 
  0x000002049625dc1c: 3011 0000 | 750d 4883 | 8638 1100 | 0001 e979 | 0000 0048 | 3bbe 4011 | 0000 750d | 4883 8648 
  0x000002049625dc3c: 1100 0001 | e963 0000 | 0048 83be | 3011 0000 | 0075 1748 | 89be 3011 | 0000 48c7 | 8638 1100 
  0x000002049625dc5c: 0001 0000 | 00e9 4200 | 0000 4883 | be40 1100 | 0000 7517 | 4889 be40 | 1100 0048 | c786 4811 
  0x000002049625dc7c: 0000 0100 | 0000 e921 | 0000 00e9 | 1c00 0000 

  0x000002049625dc8c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625dc8c: 48be b855 | 7aea 0402 | 0000 4883 | ae20 1100 | 0001 e9db | 1a00 00e9 | 0000 0000 | 4c8b c049 
  0x000002049625dcac: 8b97 8003 | 0000 488b | 1248 3b02 

  0x000002049625dcb8: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625dcb8: 488b f248 | bfb8 557a | ea04 0200 | 008b 7608 | 49ba 0000 | 00a7 0402 | 0000 4903 | f248 3bb7 
  0x000002049625dcd8: 6811 0000 | 750d 4883 | 8770 1100 | 0001 e960 | 0000 0048 | 3bb7 7811 | 0000 750d | 4883 8780 
  0x000002049625dcf8: 1100 0001 | e94a 0000 | 0048 83bf | 6811 0000 | 0075 1748 | 89b7 6811 | 0000 48c7 | 8770 1100 
  0x000002049625dd18: 0001 0000 | 00e9 2900 | 0000 4883 | bf78 1100 | 0000 7517 | 4889 b778 | 1100 0048 | c787 8011 
  0x000002049625dd38: 0000 0100 | 0000 e908 | 0000 0048 | 8387 5811 | 0000 0166 | 9048 b8ff | ffff ffff 

  0x000002049625dd54: ;   {virtual_call}
  0x000002049625dd54: ffff ffe8 

  0x000002049625dd58: ; ImmutableOopMap {}
                      ;*invokevirtual setContextClassLoader {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@573 (line 1027)
  0x000002049625dd58: 2413 2507 

  0x000002049625dd5c: ;   {other}
  0x000002049625dd5c: 0f1f 8400 | 4c45 003a | 4881 c490 | 0100 005d 

  0x000002049625dd6c: ;   {poll_return}
  0x000002049625dd6c: 493b a748 | 0400 000f | 8713 1a00 | 00c3 660f | 1f44 0000 

  0x000002049625dd80: ;   {no_reloc}
  0x000002049625dd80: e92c 1a00 | 0000 0000 | 0000 498b | 87b8 0100 | 0048 8d78 | 2849 3bbf | c801 0000 | 0f87 191a 
  0x000002049625dda0: 0000 4989 | bfb8 0100 | 0048 c700 | 0100 0000 | 488b ca49 | ba00 0000 | a704 0200 | 0049 2bca 
  0x000002049625ddc0: 8948 0848 | 33c9 8948 | 0c48 33c9 | 4889 4810 | 4889 4818 | 4889 4820 

  0x000002049625ddd8: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625ddd8: 488b d048 | beb8 557a | ea04 0200 | 0048 8386 | 200d 0000 | 0148 8bd0 | 4889 8424 | 4001 0000 
  0x000002049625ddf8: 0f1f 8000 

  0x000002049625ddfc: ;   {optimized virtual_call}
  0x000002049625ddfc: 0000 00e8 

  0x000002049625de00: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [320]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@418 (line 1006)
  0x000002049625de00: 7c0d 2507 

  0x000002049625de04: ;   {other}
  0x000002049625de04: 0f1f 8400 | f445 003b | 488b 8424 

  0x000002049625de10: ; ImmutableOopMap {rax=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*athrow {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@421 (line 1006)
  0x000002049625de10: 4001 0000 

  0x000002049625de14: ;   {section_word}
  0x000002049625de14: 48ba 14de | 2596 0402 

  0x000002049625de1c: ;   {runtime_call handle_exception_nofpu Runtime1 stub}
  0x000002049625de1c: 0000 e8dd | 2730 0790 | 488b 9424 | 8000 0000 | 498b 87f8 | 0400 004d | 33d2 4d89 | 97f8 0400 
  0x000002049625de3c: 004d 33d2 | 4d89 9700 | 0500 008b | 5a18 0fbe | 7311 85f6 

  0x000002049625de50: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625de50: 48be b855 | 7aea 0402 | 0000 48c7 | c740 0d00 | 0075 0748 | c7c7 300d | 0000 488b | 0c3e 488d 
  0x000002049625de70: 4901 4889 | 0c3e 0f85 | 0d00 0000 | 4889 8424 | 4801 0000 | e963 0700 | 0048 899c | 2458 0100 
  0x000002049625de90: 0048 8984 | 2448 0100 | 000f 1f80 | 0000 0000 

  0x000002049625dea0: ;   {no_reloc}
  0x000002049625dea0: e937 1900 | 0000 0000 | 0000 498b | 87b8 0100 | 0048 8d78 | 1849 3bbf | c801 0000 | 0f87 2419 
  0x000002049625dec0: 0000 4989 | bfb8 0100 | 0048 c700 | 0100 0000 | 488b ca49 | ba00 0000 | a704 0200 | 0049 2bca 
  0x000002049625dee0: 8948 0848 | 33c9 8948 | 0c48 33c9 | 4889 4810 

  0x000002049625def0: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625def0: 4c8b c048 | bab8 557a | ea04 0200 | 0048 8382 | 500d 0000 

  0x000002049625df04: ;   {metadata(method data for {method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625df04: 0149 b8c8 | f62f ea04 | 0200 0041 | 8b90 cc00 | 0000 83c2 | 0241 8990 | cc00 0000 | 81e2 feff 
  0x000002049625df24: 1f00 85d2 | 0f84 c518 | 0000 4c8b 

  0x000002049625df30: ;   {metadata(method data for {method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625df30: c048 bac8 | f62f ea04 | 0200 0048 | 8382 1001 

  0x000002049625df40: ;   {oop("Exception in bottom level event dispatcher: "{0x00000000eabca330})}
  0x000002049625df40: 0000 0149 | b830 a3bc | ea00 0000 | 0048 8bd0 | 4889 8424 | 5001 0000 | 0f1f 8000 

  0x000002049625df5c: ;   {optimized virtual_call}
  0x000002049625df5c: 0000 00e8 

  0x000002049625df60: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [328]=Oop [336]=Oop [344]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::<init>@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@448 (line 1012)
  0x000002049625df60: 1c0c 2507 

  0x000002049625df64: ;   {other}
  0x000002049625df64: 0f1f 8400 | 5447 003d | 488b 8424 | 4801 0000 

  0x000002049625df74: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625df74: 48ba b855 | 7aea 0402 | 0000 8b40 | 0849 ba00 | 0000 a704 | 0200 0049 | 03c2 483b | 8270 0d00 
  0x000002049625df94: 0075 0d48 | 8382 780d | 0000 01e9 | 6000 0000 | 483b 8280 | 0d00 0075 | 0d48 8382 | 880d 0000 
  0x000002049625dfb4: 01e9 4a00 | 0000 4883 | ba70 0d00 | 0000 7517 | 4889 8270 | 0d00 0048 | c782 780d | 0000 0100 
  0x000002049625dfd4: 0000 e929 | 0000 0048 | 83ba 800d | 0000 0075 | 1748 8982 | 800d 0000 | 48c7 8288 | 0d00 0001 
  0x000002049625dff4: 0000 00e9 | 0800 0000 | 4883 8260 | 0d00 0001 | 488b 9424 | 4801 0000 | 9048 b8ff | ffff ffff 
  0x000002049625e014: ;   {virtual_call}
  0x000002049625e014: ffff ffe8 

  0x000002049625e018: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [328]=Oop [336]=Oop [344]=Oop }
                      ;*invokevirtual getMessage {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@453 (line 1012)
  0x000002049625e018: 6410 2507 

  0x000002049625e01c: ;   {other}
  0x000002049625e01c: 0f1f 8400 | 0c48 003e | 4c8b 8424 | 5001 0000 

  0x000002049625e02c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625e02c: 48ba b855 | 7aea 0402 | 0000 4883 | 8298 0d00 

  0x000002049625e03c: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625e03c: 0001 49b8 | 60b2 00ea | 0402 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x000002049625e05c: ff1f 0085 | d20f 84ad | 1700 004c | 8b84 2450 

  0x000002049625e06c: ;   {metadata(method data for {method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625e06c: 0100 0048 | ba60 b200 | ea04 0200 | 0048 8382 | 1001 0000 | 014c 8bc0 | 488b 9424 | 5001 0000 
  0x000002049625e08c: ;   {optimized virtual_call}
  0x000002049625e08c: 6666 90e8 

  0x000002049625e090: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [328]=Oop [336]=Oop [344]=Oop }
                      ;*invokespecial append {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@456 (line 1012)
  0x000002049625e090: ec0a 2507 

  0x000002049625e094: ;   {other}
  0x000002049625e094: 0f1f 8400 | 8448 003f | 488b 8424 | 5001 0000 

  0x000002049625e0a4: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625e0a4: 48ba b855 | 7aea 0402 | 0000 4883 | 82d0 0d00 

  0x000002049625e0b4: ;   {metadata(method data for {method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625e0b4: 0001 48ba | a8ba 00ea | 0402 0000 | 8bb2 cc00 | 0000 83c6 | 0289 b2cc | 0000 0081 | e6fe ff1f 
  0x000002049625e0d4: 0085 f60f | 8458 1700 

  0x000002049625e0dc: ;   {metadata('java/lang/String')}
  0x000002049625e0dc: 0048 ba40 | e700 a704 | 0200 0049 | 8b87 b801 | 0000 488d | 7818 493b | bfc8 0100 | 000f 8757 
  0x000002049625e0fc: 1700 0049 | 89bf b801 | 0000 48c7 | 0001 0000 | 0048 8bca | 49ba 0000 | 00a7 0402 | 0000 492b 
  0x000002049625e11c: ca89 4808 | 4833 c989 | 480c 4833 | c948 8948 | 104c 8bc0 

  0x000002049625e130: ;   {metadata(method data for {method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625e130: 49b9 a8ba | 00ea 0402 | 0000 4983 | 8110 0100 

  0x000002049625e140: ;   {metadata(method data for {method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625e140: 0001 49b8 | f0bb 00ea | 0402 0000 | 458b 88cc | 0000 0041 | 83c1 0245 | 8988 cc00 | 0000 4181 
  0x000002049625e160: e1fe ff1f | 0045 85c9 | 0f84 f516 | 0000 4c8b 

  0x000002049625e170: ;   {metadata(method data for {method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625e170: c049 b9f0 | bb00 ea04 | 0200 0049 | 8381 1001 | 0000 014c | 8b84 2450 

  0x000002049625e188: ;   {oop(nullptr)}
  0x000002049625e188: 0100 0049 | b900 0000 | 0000 0000 | 0048 8bd0 | 4889 8424 | 6001 0000 | 0f1f 8000 

  0x000002049625e1a4: ;   {optimized virtual_call}
  0x000002049625e1a4: 0000 00e8 

  0x000002049625e1a8: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [328]=Oop [344]=Oop [352]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::<init>@3
                      ; - java.lang.StringBuilder::toString@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@459 (line 1012)
  0x000002049625e1a8: d409 2507 

  0x000002049625e1ac: ;   {other}
  0x000002049625e1ac: 0f1f 8400 | 9c49 0040 | 488b 9c24 | 5801 0000 

  0x000002049625e1bc: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625e1bc: 49b8 b855 | 7aea 0402 

  0x000002049625e1c4: ;   {metadata('org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625e1c4: 0000 49ba | 0000 09a8 | 0402 0000 | 4d89 9018 | 0e00 0049 | 8380 200e 

  0x000002049625e1dc: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625e1dc: 0000 0149 | b8c0 5078 | ea04 0200 | 0045 8b88 | cc00 0000 | 4183 c102 | 4589 88cc | 0000 0041 
  0x000002049625e1fc: 81e1 feff | 1f00 4585 | c90f 8479 | 1600 0048 | 8b9c 2458 | 0100 008b | 5320 4885 

  0x000002049625e218: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625e218: d249 b8c0 | 5078 ea04 | 0200 0049 | c7c1 1001 | 0000 7407 | 49c7 c120 | 0100 004b | 8b34 0848 
  0x000002049625e238: 8d76 014b | 8934 080f | 84fd 0200 | 0048 85d2 | 0f84 2900 

  0x000002049625e24c: ;   {metadata('org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625e24c: 0000 48bf | 884a 09a8 | 0402 0000 | 8b72 0849 | ba00 0000 | a704 0200 | 0049 03f2 | 483b 7e38 
  0x000002049625e26c: 0f85 3316 | 0000 e900 | 0000 004c | 8bc2 483b | 024c 8bc2 

  0x000002049625e280: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625e280: 49b9 c050 | 78ea 0402 

  0x000002049625e288: ;   {metadata('org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625e288: 0000 49ba | 884a 09a8 | 0402 0000 | 4d89 9140 | 0100 0049 | 8381 4801 

  0x000002049625e2a0: ;   {metadata(method data for {method} {0x00000204ea507238} 'getLogger' '(Ljava/lang/String;)Lorg/osgi/service/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625e2a0: 0000 0149 | b800 5378 | ea04 0200 | 0045 8b88 | cc00 0000 | 4183 c102 | 4589 88cc | 0000 0041 
  0x000002049625e2c0: 81e1 feff | 1f00 4585 | c90f 84e0 | 1500 004c 

  0x000002049625e2d0: ;   {metadata(method data for {method} {0x00000204ea507238} 'getLogger' '(Ljava/lang/String;)Lorg/osgi/service/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625e2d0: 8bc2 49b9 | 0053 78ea | 0402 0000 

  0x000002049625e2dc: ;   {metadata('org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625e2dc: 49ba 884a | 09a8 0402 | 0000 4d89 | 9120 0100 | 0049 8381 | 2801 0000 

  0x000002049625e2f4: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625e2f4: 0149 b890 | 5478 ea04 | 0200 0045 | 8b88 cc00 | 0000 4183 | c102 4589 | 88cc 0000 | 0041 81e1 
  0x000002049625e314: feff 1f00 | 4585 c90f | 84af 1500 | 004c 8bc2 

  0x000002049625e324: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625e324: 49b9 9054 | 78ea 0402 

  0x000002049625e32c: ;   {metadata('org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625e32c: 0000 49ba | 884a 09a8 | 0402 0000 | 4d89 9120 | 0100 0049 | 8381 2801 

  0x000002049625e344: ;   {oop("org.eclipse.osgi/debug/events"{0x00000000c0064c78})}
  0x000002049625e344: 0000 0149 | b878 4c06 | c000 0000 

  0x000002049625e350: ;   {oop(a 'java/lang/Class'{0x00000000ec830698} = 'org/osgi/service/log/Logger')}
  0x000002049625e350: 0049 b998 | 0683 ec00 | 0000 000f 

  0x000002049625e35c: ;   {optimized virtual_call}
  0x000002049625e35c: 1f40 00e8 

  0x000002049625e360: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [328]=Oop [352]=Oop }
                      ;*invokevirtual getLogger {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@4 (line 73)
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@2 (line 1)
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@462 (line 1012)
  0x000002049625e360: 1c08 2507 

  0x000002049625e364: ;   {other}
  0x000002049625e364: 0f1f 8400 | 544b 0041 | 4885 c075 

  0x000002049625e370: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625e370: 1648 be90 | 5478 ea04 | 0200 0080 | 8e41 0100 | 0001 e9e7 

  0x000002049625e384: ;   {metadata('org/eclipse/equinox/log/Logger')}
  0x000002049625e384: 0000 0048 | bf70 1f09 | a804 0200 | 008b 7008 | 49ba 0000 | 00a7 0402 | 0000 4903 | f248 3b7e 
  0x000002049625e3a4: 200f 841a | 0000 0048 | 3bf7 0f84 | 1100 0000 

  0x000002049625e3b4: ;   {runtime_call slow_subtype_check Runtime1 stub}
  0x000002049625e3b4: 5657 e845 | 3530 075e | 5e85 f60f | 848d 0000 

  0x000002049625e3c4: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625e3c4: 0048 be90 | 5478 ea04 | 0200 008b | 7808 49ba | 0000 00a7 | 0402 0000 | 4903 fa48 | 3bbe 5801 
  0x000002049625e3e4: 0000 750d | 4883 8660 | 0100 0001 | e979 0000 | 0048 3bbe | 6801 0000 | 750d 4883 | 8670 0100 
  0x000002049625e404: 0001 e963 | 0000 0048 | 83be 5801 | 0000 0075 | 1748 89be | 5801 0000 | 48c7 8660 | 0100 0001 
  0x000002049625e424: 0000 00e9 | 4200 0000 | 4883 be68 | 0100 0000 | 7517 4889 | be68 0100 | 0048 c786 | 7001 0000 
  0x000002049625e444: 0100 0000 | e921 0000 | 00e9 1c00 

  0x000002049625e450: ;   {metadata(method data for {method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625e450: 0000 48be | 9054 78ea | 0402 0000 | 4883 ae48 | 0100 0001 | e988 1400 | 00e9 0000 | 0000 488b 
  0x000002049625e470: d048 3b02 

  0x000002049625e474: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625e474: 4c8b c248 | bec0 5078 | ea04 0200 | 0045 8b40 | 0849 ba00 | 0000 a704 | 0200 004d | 03c2 4c3b 
  0x000002049625e494: 8678 0100 | 0075 0d48 | 8386 8001 | 0000 01e9 | 6000 0000 | 4c3b 8688 | 0100 0075 | 0d48 8386 
  0x000002049625e4b4: 9001 0000 | 01e9 4a00 | 0000 4883 | be78 0100 | 0000 7517 | 4c89 8678 | 0100 0048 | c786 8001 
  0x000002049625e4d4: 0000 0100 | 0000 e929 | 0000 0048 | 83be 8801 | 0000 0075 | 174c 8986 | 8801 0000 | 48c7 8690 
  0x000002049625e4f4: 0100 0001 | 0000 00e9 | 0800 0000 | 4883 8668 | 0100 0001 | 4c8b 8424 | 6001 0000 | 0f1f 4400 
  0x000002049625e514: 0048 b8ff | ffff ffff 

  0x000002049625e51c: ;   {virtual_call}
  0x000002049625e51c: ffff ffe8 

  0x000002049625e520: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [328]=Oop }
                      ;*invokeinterface trace {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@17 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@462 (line 1012)
  0x000002049625e520: 5c0b 2507 

  0x000002049625e524: ;   {other}
  0x000002049625e524: 0f1f 8400 | 144d 0042 

  0x000002049625e52c: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625e52c: 49b8 c050 | 78ea 0402 | 0000 41ff | 80a0 0100 | 00e9 5200 | 0000 488b | 8424 6001 

  0x000002049625e548: ;   {oop(a 'java/lang/Class'{0x00000000ec827d58} = 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625e548: 0000 49b8 | 587d 82ec | 0000 0000 | 418b 90c8 | 0000 0048 | 3b02 4c8b 

  0x000002049625e560: ;   {metadata(method data for {method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625e560: c248 bec0 | 5078 ea04 

  0x000002049625e568: ;   {metadata('java/io/PrintStream')}
  0x000002049625e568: 0200 0049 | ba60 a300 | a704 0200 | 004c 8996 | c801 0000 | 4883 86d0 | 0100 0001 

  0x000002049625e584: ;   {optimized virtual_call}
  0x000002049625e584: 4c8b c0e8 

  0x000002049625e588: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [328]=Oop }
                      ;*invokevirtual println {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@29 (line 232)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@462 (line 1012)
  0x000002049625e588: f405 2507 

  0x000002049625e58c: ;   {other}
  0x000002049625e58c: 0f1f 8400 | 7c4d 0043 | 488b 9424 | 8000 0000 | 8b72 1848 | 3b06 4c8b 

  0x000002049625e5a4: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625e5a4: c649 b9b8 | 557a ea04 

  0x000002049625e5ac: ;   {metadata('org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625e5ac: 0200 0049 | ba00 0009 | a804 0200 | 004d 8991 | 500e 0000 | 4983 8158 | 0e00 0001 

  0x000002049625e5c8: ;   {oop("org.eclipse.osgi/debug/events"{0x00000000c0064c78})}
  0x000002049625e5c8: 49b8 784c | 06c0 0000 | 0000 4c8b | 8c24 4801 | 0000 488b 

  0x000002049625e5dc: ;   {optimized virtual_call}
  0x000002049625e5dc: d666 90e8 

  0x000002049625e5e0: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [328]=Oop }
                      ;*invokevirtual traceThrowable {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@474 (line 1016)
  0x000002049625e5e0: 9c05 2507 

  0x000002049625e5e4: ;   {other}
  0x000002049625e5e4: 0f1f 8400 | d44d 0044 | 8bbc 2488 | 0000 0083 

  0x000002049625e5f4: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625e5f4: ff04 48b8 | b855 7aea | 0402 0000 | 49c7 c078 | 0e00 0075 | 0749 c7c0 | 880e 0000 | 4e8b 0c00 
  0x000002049625e614: 4d8d 4901 | 4e89 0c00 | 0f85 4f01 | 0000 488b | b424 9800 | 0000 4885 

  0x000002049625e62c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625e62c: f675 1749 | b9b8 557a | ea04 0200 | 0041 8089 | 910e 0000 | 01e9 cd00 

  0x000002049625e644: ;   {metadata('org/osgi/framework/FrameworkEvent')}
  0x000002049625e644: 0000 48bf | 8056 11a8 | 0402 0000 | 448b 4608 | 49ba 0000 | 00a7 0402 | 0000 4d03 | c249 3bf8 
  0x000002049625e664: 0f85 8d00 

  0x000002049625e668: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625e668: 0000 49b9 | b855 7aea | 0402 0000 | 8b7e 0849 | ba00 0000 | a704 0200 | 0049 03fa | 493b b9a8 
  0x000002049625e688: 0e00 0075 | 0d49 8381 | b00e 0000 | 01e9 7900 | 0000 493b | b9b8 0e00 | 0075 0d49 | 8381 c00e 
  0x000002049625e6a8: 0000 01e9 | 6300 0000 | 4983 b9a8 | 0e00 0000 | 7517 4989 | b9a8 0e00 | 0049 c781 | b00e 0000 
  0x000002049625e6c8: 0100 0000 | e942 0000 | 0049 83b9 | b80e 0000 | 0075 1749 | 89b9 b80e | 0000 49c7 | 81c0 0e00 
  0x000002049625e6e8: 0001 0000 | 00e9 2100 | 0000 e91c 

  0x000002049625e6f4: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625e6f4: 0000 0049 | b9b8 557a | ea04 0200 | 0049 83a9 | 980e 0000 | 01e9 fb11 | 0000 e900 | 0000 0048 
  0x000002049625e714: 8bc6 483b | 004c 8bc0 

  0x000002049625e71c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625e71c: 49b9 b855 | 7aea 0402 

  0x000002049625e724: ;   {metadata('org/osgi/framework/FrameworkEvent')}
  0x000002049625e724: 0000 49ba | 8056 11a8 | 0402 0000 | 4d89 91e0 | 0e00 0049 | 8381 e80e | 0000 018b | 4010 83f8 
  0x000002049625e744: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625e744: 0248 b8b8 | 557a ea04 | 0200 0049 | c7c0 080f | 0000 7407 | 49c7 c018 | 0f00 004e | 8b0c 004d 
  0x000002049625e764: 8d49 014e | 890c 000f | 841b 0100 | 0048 8bb4 | 2480 0000 | 0044 8b46 | 1449 3b00 

  0x000002049625e780: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625e780: 498b c049 | b9b8 557a | ea04 0200 

  0x000002049625e78c: ;   {metadata('org/eclipse/osgi/internal/framework/EquinoxContainer')}
  0x000002049625e78c: 0049 bab8 | 9608 a804 | 0200 004d | 8991 380f | 0000 4983 | 8140 0f00 

  0x000002049625e7a4: ;   {metadata(method data for {method} {0x00000204ea4a2110} 'getEventPublisher' '()Lorg/eclipse/osgi/internal/framework/EquinoxEventPublisher;' in 'org/eclipse/osgi/internal/framework/EquinoxContainer')}
  0x000002049625e7a4: 0001 48b8 | 98be 78ea | 0402 0000 | 448b 88cc | 0000 0041 | 83c1 0244 | 8988 cc00 | 0000 4181 
  0x000002049625e7c4: e1fe ff1f | 0045 85c9 | 0f84 4a11 | 0000 458b | 482c 488d | bc24 7001 | 0000 4c89 | 4f08 498b 
  0x000002049625e7e4: 0148 83c8 | 0148 8907 | f049 0fb1 | 390f 8412 | 0000 0048 | 2bc4 4825 | 07f0 ffff | 4889 070f 
  0x000002049625e804: 8539 1100 | 0049 ff87 | 4805 0000 | 418b 5028 | 488d 8424 | 7001 0000 | 4c8b 004d | 85c0 0f84 
  0x000002049625e824: 0f00 0000 | 4c8b 4808 | f04d 0fb1 | 010f 851e | 1100 0049 | ff8f 4805 | 0000 448b | 4e10 483b 
  0x000002049625e844: 024c 8bc2 

  0x000002049625e848: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625e848: 48bf b855 | 7aea 0402 

  0x000002049625e850: ;   {metadata('org/eclipse/osgi/internal/framework/EquinoxEventPublisher')}
  0x000002049625e850: 0000 49ba | f869 0ba8 | 0402 0000 | 4c89 9770 | 0f00 0048 | 8387 780f | 0000 0141 | b802 0000 
  0x000002049625e870: 0048 8bbc | 2448 0100 | 0066 0f1f 

  0x000002049625e87c: ;   {optimized virtual_call}
  0x000002049625e87c: 4400 00e8 

  0x000002049625e880: ; ImmutableOopMap {[176]=Oop }
                      ;*invokevirtual publishFrameworkEvent {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@508 (line 1023)
  0x000002049625e880: fc02 2507 

  0x000002049625e884: ;   {other}
  0x000002049625e884: 0f1f 8400 | 7450 0045 | 488b bc24 | b000 0000 

  0x000002049625e894: ;   {oop(a 'java/lang/Boolean'{0x00000000c0005570} = false)}
  0x000002049625e894: 49ba 7055 | 00c0 0000 | 0000 493b 

  0x000002049625e8a0: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625e8a0: fa49 b8b8 | 557a ea04 | 0200 0048 | c7c2 980f | 0000 7407 | 48c7 c2a8 | 0f00 0049 | 8b34 1048 
  0x000002049625e8c0: 8d76 0149 | 8934 100f | 84c7 0100 

  0x000002049625e8cc: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625e8cc: 0049 b8b8 | 557a ea04 | 0200 0049 | 8380 b80f | 0000 0148 | 85ff 7516 

  0x000002049625e8e4: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625e8e4: 48be b855 | 7aea 0402 | 0000 808e | c10f 0000 | 01e9 ce00 

  0x000002049625e8f8: ;   {metadata('java/lang/ClassLoader')}
  0x000002049625e8f8: 0000 49b8 | 483d 01a7 | 0402 0000 | 8b77 0849 | ba00 0000 | a704 0200 | 0049 03f2 | 4c3b 4638 
  0x000002049625e918: 0f85 8e00 

  0x000002049625e91c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625e91c: 0000 48be | b855 7aea | 0402 0000 | 448b 4708 | 49ba 0000 | 00a7 0402 | 0000 4d03 | c24c 3b86 
  0x000002049625e93c: d80f 0000 | 750d 4883 | 86e0 0f00 | 0001 e979 | 0000 004c | 3b86 e80f | 0000 750d | 4883 86f0 
  0x000002049625e95c: 0f00 0001 | e963 0000 | 0048 83be | d80f 0000 | 0075 174c | 8986 d80f | 0000 48c7 | 86e0 0f00 
  0x000002049625e97c: 0001 0000 | 00e9 4200 | 0000 4883 | bee8 0f00 | 0000 7517 | 4c89 86e8 | 0f00 0048 | c786 f00f 
  0x000002049625e99c: 0000 0100 | 0000 e921 | 0000 00e9 | 1c00 0000 

  0x000002049625e9ac: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625e9ac: 48be b855 | 7aea 0402 | 0000 4883 | aec8 0f00 | 0001 e9ad | 0f00 00e9 | 0000 0000 | 4c8b c749 
  0x000002049625e9cc: 8b97 8003 | 0000 488b | 1248 3b02 

  0x000002049625e9d8: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625e9d8: 488b f248 | bfb8 557a | ea04 0200 | 008b 7608 | 49ba 0000 | 00a7 0402 | 0000 4903 | f248 3bb7 
  0x000002049625e9f8: 1010 0000 | 750d 4883 | 8718 1000 | 0001 e960 | 0000 0048 | 3bb7 2010 | 0000 750d | 4883 8728 
  0x000002049625ea18: 1000 0001 | e94a 0000 | 0048 83bf | 1010 0000 | 0075 1748 | 89b7 1010 | 0000 48c7 | 8718 1000 
  0x000002049625ea38: 0001 0000 | 00e9 2900 | 0000 4883 | bf20 1000 | 0000 7517 | 4889 b720 | 1000 0048 | c787 2810 
  0x000002049625ea58: 0000 0100 | 0000 e908 | 0000 0048 | 8387 0010 | 0000 0166 | 9048 b8ff | ffff ffff 

  0x000002049625ea74: ;   {virtual_call}
  0x000002049625ea74: ffff ffe8 

  0x000002049625ea78: ; ImmutableOopMap {}
                      ;*invokevirtual setContextClassLoader {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@527 (line 1027)
  0x000002049625ea78: 0406 2507 

  0x000002049625ea7c: ;   {other}
  0x000002049625ea7c: 0f1f 8400 | 6c52 0046 

  0x000002049625ea84: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625ea84: 48b8 b855 | 7aea 0402 | 0000 ff80 | 3810 0000 | 4881 c490 | 0100 005d 

  0x000002049625ea9c: ;   {poll_return}
  0x000002049625ea9c: 493b a748 | 0400 000f | 87d5 0e00 | 00c3 488b | bc24 b000 | 0000 488b | b424 8000 | 0000 498b 
  0x000002049625eabc: 87f8 0400 | 004d 33d2 | 4d89 97f8 | 0400 004d | 33d2 4d89 | 9700 0500 | 0048 8bd0 | 488d 8424 
  0x000002049625eadc: 7001 0000 | 4c8b 004d | 85c0 0f84 | 0f00 0000 | 488b 5808 | f04c 0fb1 | 030f 8599 | 0e00 0049 
  0x000002049625eafc: ff8f 4805 | 0000 4c8b | 8424 4801 | 0000 488b 

  0x000002049625eb0c: ; ImmutableOopMap {rdi=Oop rsi=Oop r8=Oop rax=Oop [128]=Oop [176]=Oop }
                      ;*athrow {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.osgi.internal.framework.EquinoxContainer::getEventPublisher@16 (line 275)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@498 (line 1023)
                      ;   {section_word}
  0x000002049625eb0c: c248 ba0d | eb25 9604 

  0x000002049625eb14: ;   {runtime_call handle_exception_nofpu Runtime1 stub}
  0x000002049625eb14: 0200 00e8 | e41a 3007 | 9048 8bbc | 24b0 0000 | 0049 8b87 | f804 0000 | 4d33 d24d | 8997 f804 
  0x000002049625eb34: 0000 4d33 | d24d 8997 | 0005 0000 

  0x000002049625eb40: ;   {oop(a 'java/lang/Boolean'{0x00000000c0005570} = false)}
  0x000002049625eb40: 49ba 7055 | 00c0 0000 | 0000 493b 

  0x000002049625eb4c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625eb4c: fa49 b8b8 | 557a ea04 | 0200 0048 | c7c2 5010 | 0000 7407 | 48c7 c260 | 1000 0049 | 8b34 1048 
  0x000002049625eb6c: 8d76 0149 | 8934 100f | 84cb 0100 | 0048 8984 | 2468 0100 

  0x000002049625eb80: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625eb80: 0049 b8b8 | 557a ea04 | 0200 0049 | 8380 7010 | 0000 0148 | 85ff 7516 

  0x000002049625eb98: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625eb98: 48be b855 | 7aea 0402 | 0000 808e | 7910 0000 | 01e9 ce00 

  0x000002049625ebac: ;   {metadata('java/lang/ClassLoader')}
  0x000002049625ebac: 0000 49b8 | 483d 01a7 | 0402 0000 | 8b77 0849 | ba00 0000 | a704 0200 | 0049 03f2 | 4c3b 4638 
  0x000002049625ebcc: 0f85 8e00 

  0x000002049625ebd0: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625ebd0: 0000 48be | b855 7aea | 0402 0000 | 448b 4708 | 49ba 0000 | 00a7 0402 | 0000 4d03 | c24c 3b86 
  0x000002049625ebf0: 9010 0000 | 750d 4883 | 8698 1000 | 0001 e979 | 0000 004c | 3b86 a010 | 0000 750d | 4883 86a8 
  0x000002049625ec10: 1000 0001 | e963 0000 | 0048 83be | 9010 0000 | 0075 174c | 8986 9010 | 0000 48c7 | 8698 1000 
  0x000002049625ec30: 0001 0000 | 00e9 4200 | 0000 4883 | bea0 1000 | 0000 7517 | 4c89 86a0 | 1000 0048 | c786 a810 
  0x000002049625ec50: 0000 0100 | 0000 e921 | 0000 00e9 | 1c00 0000 

  0x000002049625ec60: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625ec60: 48be b855 | 7aea 0402 | 0000 4883 | ae80 1000 | 0001 e933 | 0d00 00e9 | 0000 0000 | 4c8b c749 
  0x000002049625ec80: 8b97 8003 | 0000 488b | 1248 3b02 

  0x000002049625ec8c: ;   {metadata(method data for {method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625ec8c: 488b f248 | bfb8 557a | ea04 0200 | 008b 7608 | 49ba 0000 | 00a7 0402 | 0000 4903 | f248 3bb7 
  0x000002049625ecac: c810 0000 | 750d 4883 | 87d0 1000 | 0001 e960 | 0000 0048 | 3bb7 d810 | 0000 750d | 4883 87e0 
  0x000002049625eccc: 1000 0001 | e94a 0000 | 0048 83bf | c810 0000 | 0075 1748 | 89b7 c810 | 0000 48c7 | 87d0 1000 
  0x000002049625ecec: 0001 0000 | 00e9 2900 | 0000 4883 | bfd8 1000 | 0000 7517 | 4889 b7d8 | 1000 0048 | c787 e010 
  0x000002049625ed0c: 0000 0100 | 0000 e908 | 0000 0048 | 8387 b810 | 0000 0166 | 0f1f 4400 | 0048 b8ff | ffff ffff 
  0x000002049625ed2c: ;   {virtual_call}
  0x000002049625ed2c: ffff ffe8 

  0x000002049625ed30: ; ImmutableOopMap {[360]=Oop }
                      ;*invokevirtual setContextClassLoader {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@551 (line 1027)
  0x000002049625ed30: 4c03 2507 

  0x000002049625ed34: ;   {other}
  0x000002049625ed34: 0f1f 8400 | 2455 0048 | 488b 8424 | 6801 0000 | e98c 0c00 

  0x000002049625ed48: ;   {metadata({method} {0x00000204ea678510} 'dispatchEvent' '(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V' in 'org/eclipse/osgi/internal/framework/BundleContextImpl')}
  0x000002049625ed48: 0049 ba08 | 8567 ea04 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002049625ed60: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625ed60: e81b 5c30 

  0x000002049625ed64: ; ImmutableOopMap {rdx=Oop r8=Oop r9=Oop rsi=Oop [128]=Oop [152]=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@-1 (line 960)
  0x000002049625ed64: 07e9 92b5 

  0x000002049625ed68: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625ed68: ffff e8b1 

  0x000002049625ed6c: ; ImmutableOopMap {rax=Oop rdx=Oop rsi=Oop [128]=Oop [144]=Oop [152]=Oop [176]=Oop }
                      ;*invokevirtual getBundleId {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@17 (line 963)
                      ;   {metadata({method} {0x00000204ea5e07b0} 'getBundleId' '()J' in 'org/eclipse/osgi/internal/framework/EquinoxBundle')}
  0x000002049625ed6c: f92f 0749 | baa8 075e | ea04 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002049625ed84: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625ed84: ffff e8f5 

  0x000002049625ed88: ; ImmutableOopMap {rax=Oop rdx=Oop rsi=Oop [128]=Oop [144]=Oop [152]=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.framework.EquinoxBundle::getBundleId@-1 (line 553)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@17 (line 963)
  0x000002049625ed88: 5b30 07e9 | dfb6 ffff 

  0x000002049625ed90: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625ed90: e88b f92f 

  0x000002049625ed94: ; ImmutableOopMap {rax=Oop rdx=Oop rsi=Oop [128]=Oop [144]=Oop [152]=Oop [176]=Oop }
                      ;*invokevirtual getId {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.EquinoxBundle::getBundleId@4 (line 553)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@17 (line 963)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625ed94: 07e8 86f9 

  0x000002049625ed98: ; ImmutableOopMap {rax=Oop rdx=Oop rsi=Oop [128]=Oop [144]=Oop [152]=Oop [176]=Oop }
                      ;*invokevirtual longValue {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.EquinoxBundle::getBundleId@7 (line 553)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@17 (line 963)
  0x000002049625ed98: 2f07 4c89 

  0x000002049625ed9c: ;   {runtime_call throw_class_cast_exception Runtime1 stub}
  0x000002049625ed9c: 0c24 e87d 

  0x000002049625eda0: ; ImmutableOopMap {rax=Oop rdx=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@57 (line 967)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625eda0: 2530 07e8 

  0x000002049625eda4: ; ImmutableOopMap {rax=Oop rdx=Oop rbx=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*getfield DEBUG_EVENTS {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@66 (line 969)
  0x000002049625eda4: 78f9 2f07 

  0x000002049625eda8: ;   {metadata(nullptr)}
  0x000002049625eda8: 48ba 0000 | 0000 0000 | 0000 b800 

  0x000002049625edb4: ;   {runtime_call load_klass_patching Runtime1 stub}
  0x000002049625edb4: 0f05 0ae8 

  0x000002049625edb8: ; ImmutableOopMap {rbx=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*new {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@72 (line 970)
  0x000002049625edb8: 4445 3007 | e9f7 b8ff | ff48 8bd2 

  0x000002049625edc4: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x000002049625edc4: e837 0430 

  0x000002049625edc8: ; ImmutableOopMap {rbx=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@72 (line 970)
  0x000002049625edc8: 07e9 3ab9 

  0x000002049625edcc: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625edcc: ffff e84d 

  0x000002049625edd0: ; ImmutableOopMap {rbx=Oop rax=Oop [128]=Oop [152]=Oop [160]=Oop [176]=Oop }
                      ;*invokevirtual getClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@78 (line 970)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625edd0: f92f 07e8 

  0x000002049625edd4: ; ImmutableOopMap {rbx=Oop rax=Oop rdx=Oop [128]=Oop [152]=Oop [160]=Oop [176]=Oop }
                      ;*invokevirtual getName {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@81 (line 970)
  0x000002049625edd4: 48f9 2f07 

  0x000002049625edd8: ;   {metadata({method} {0x00000204a7465ac0} 'getName' '()Ljava/lang/String;' in 'java/lang/Class')}
  0x000002049625edd8: 49ba b85a | 46a7 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625edec: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625edec: ffff ffe8 

  0x000002049625edf0: ; ImmutableOopMap {rbx=Oop rax=Oop rdx=Oop [128]=Oop [152]=Oop [160]=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - java.lang.Class::getName@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@81 (line 970)
  0x000002049625edf0: 8c5b 3007 | e985 b9ff 

  0x000002049625edf8: ;   {metadata({method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625edf8: ff49 ba50 | 1245 a704 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002049625ee10: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625ee10: e86b 5b30 

  0x000002049625ee14: ; ImmutableOopMap {rsi=Oop [128]=Oop [152]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - java.lang.String::valueOf@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@84 (line 970)
  0x000002049625ee14: 07e9 1eba 

  0x000002049625ee18: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625ee18: ffff e801 

  0x000002049625ee1c: ; ImmutableOopMap {rsi=Oop [128]=Oop [152]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual toString {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::valueOf@11
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@84 (line 970)
                      ;   {metadata({method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625ee1c: f92f 0749 | ba30 dd45 | a704 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002049625ee34: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625ee34: ffff e845 

  0x000002049625ee38: ; ImmutableOopMap {rsi=Oop rax=Oop [128]=Oop [152]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::<init>@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@87 (line 970)
  0x000002049625ee38: 5b30 07e9 | 45bb ffff 

  0x000002049625ee40: ;   {metadata({method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625ee40: 49ba 70da | 45a7 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625ee54: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625ee54: ffff ffe8 

  0x000002049625ee58: ; ImmutableOopMap {[128]=Oop [152]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@93 (line 970)
  0x000002049625ee58: 245b 3007 | e996 bbff 

  0x000002049625ee60: ;   {metadata({method} {0x00000204a74126c8} 'toHexString' '(I)Ljava/lang/String;' in 'java/lang/Integer')}
  0x000002049625ee60: ff49 bac0 | 2641 a704 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002049625ee78: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625ee78: e803 5b30 

  0x000002049625ee7c: ; ImmutableOopMap {[128]=Oop [152]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - java.lang.Integer::toHexString@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@101 (line 971)
  0x000002049625ee7c: 07e9 18bc 

  0x000002049625ee80: ;   {metadata({method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625ee80: ffff 49ba | 70da 45a7 | 0402 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002049625ee98: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625ee98: ffe8 e25a 

  0x000002049625ee9c: ; ImmutableOopMap {rax=Oop [128]=Oop [152]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@104 (line 971)
  0x000002049625ee9c: 3007 e964 

  0x000002049625eea0: ;   {metadata({method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625eea0: bcff ff49 | ba08 8900 | a704 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002049625eeb8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625eeb8: ffff e8c1 

  0x000002049625eebc: ; ImmutableOopMap {[128]=Oop [152]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::toString@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@107 (line 970)
  0x000002049625eebc: 5a30 07e9 | b9bc ffff 

  0x000002049625eec4: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x000002049625eec4: 488b d2e8 

  0x000002049625eec8: ; ImmutableOopMap {[128]=Oop [152]=Oop [160]=Oop [168]=Oop [176]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::toString@0
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@107 (line 970)
  0x000002049625eec8: 3403 3007 | e9fc bcff 

  0x000002049625eed0: ;   {metadata({method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625eed0: ff49 baa8 | 1d45 a704 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002049625eee8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625eee8: e893 5a30 

  0x000002049625eeec: ; ImmutableOopMap {rax=Oop [128]=Oop [152]=Oop [160]=Oop [168]=Oop [176]=Oop [184]=Oop }
                      ;*synchronization entry
                      ; - java.lang.String::<init>@-1
                      ; - java.lang.StringBuilder::toString@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@107 (line 970)
  0x000002049625eeec: 07e9 24bd 

  0x000002049625eef0: ;   {metadata(nullptr)}
  0x000002049625eef0: ffff 48ba | 0000 0000 | 0000 0000 | b800 0f05 

  0x000002049625ef00: ;   {runtime_call load_klass_patching Runtime1 stub}
  0x000002049625ef00: 0ae8 fa43 

  0x000002049625ef04: ; ImmutableOopMap {rbx=Oop [128]=Oop [152]=Oop [168]=Oop [176]=Oop [184]=Oop [200]=Oop }
                      ;*new {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@119 (line 972)
  0x000002049625ef04: 3007 e95d | bdff ff48 

  0x000002049625ef0c: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x000002049625ef0c: 8bd2 e8ed 

  0x000002049625ef10: ; ImmutableOopMap {rbx=Oop [128]=Oop [152]=Oop [168]=Oop [176]=Oop [184]=Oop [200]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@119 (line 972)
  0x000002049625ef10: 0230 07e9 | a0bd ffff 

  0x000002049625ef18: ;   {metadata({method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625ef18: 49ba 30dd | 45a7 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625ef2c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625ef2c: ffff ffe8 

  0x000002049625ef30: ; ImmutableOopMap {rbx=Oop rax=Oop [128]=Oop [152]=Oop [168]=Oop [176]=Oop [184]=Oop [192]=Oop [200]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::<init>@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@126 (line 972)
  0x000002049625ef30: 4c5a 3007 | e9c5 bdff 

  0x000002049625ef38: ;   {metadata({method} {0x00000204a745dad0} 'append' '(Ljava/lang/Object;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625ef38: ff49 bac8 | da45 a704 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002049625ef50: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625ef50: e82b 5a30 

  0x000002049625ef54: ; ImmutableOopMap {rdx=Oop rsi=Oop [128]=Oop [152]=Oop [168]=Oop [176]=Oop [184]=Oop [192]=Oop [200]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@133 (line 972)
  0x000002049625ef54: 07e9 26be 

  0x000002049625ef58: ;   {metadata({method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625ef58: ffff 49ba | 5012 45a7 | 0402 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002049625ef70: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625ef70: ffe8 0a5a 

  0x000002049625ef74: ; ImmutableOopMap {rdx=Oop rsi=Oop [128]=Oop [152]=Oop [168]=Oop [176]=Oop [184]=Oop [192]=Oop [200]=Oop }
                      ;*synchronization entry
                      ; - java.lang.String::valueOf@-1
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@133 (line 972)
  0x000002049625ef74: 3007 e93e 

  0x000002049625ef78: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625ef78: beff ffe8 

  0x000002049625ef7c: ; ImmutableOopMap {rsi=Oop [128]=Oop [152]=Oop [168]=Oop [176]=Oop [184]=Oop [192]=Oop [200]=Oop }
                      ;*invokevirtual toString {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::valueOf@11
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@133 (line 972)
  0x000002049625ef7c: a0f7 2f07 

  0x000002049625ef80: ;   {metadata({method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625ef80: 49ba 70da | 45a7 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625ef94: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625ef94: ffff ffe8 

  0x000002049625ef98: ; ImmutableOopMap {rsi=Oop [128]=Oop [152]=Oop [168]=Oop [176]=Oop [184]=Oop [192]=Oop [200]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - java.lang.StringBuilder::append@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@133 (line 972)
  0x000002049625ef98: e459 3007 | e959 bfff 

  0x000002049625efa0: ;   {metadata({method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625efa0: ff49 ba70 | da45 a704 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002049625efb8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625efb8: e8c3 5930 

  0x000002049625efbc: ; ImmutableOopMap {rax=Oop [128]=Oop [152]=Oop [168]=Oop [176]=Oop [184]=Oop [192]=Oop [200]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@139 (line 972)
  0x000002049625efbc: 07e9 b0bf 

  0x000002049625efc0: ;   {metadata({method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625efc0: ffff 49ba | 70da 45a7 | 0402 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002049625efd8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625efd8: ffe8 a259 

  0x000002049625efdc: ; ImmutableOopMap {[128]=Oop [152]=Oop [168]=Oop [176]=Oop [184]=Oop [192]=Oop [200]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@144 (line 972)
  0x000002049625efdc: 3007 e904 

  0x000002049625efe0: ;   {metadata({method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625efe0: c0ff ff49 | ba70 da45 | a704 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002049625eff8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625eff8: ffff e881 

  0x000002049625effc: ; ImmutableOopMap {[128]=Oop [152]=Oop [168]=Oop [176]=Oop [192]=Oop [200]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@150 (line 972)
  0x000002049625effc: 5930 07e9 | 63c0 ffff 

  0x000002049625f004: ;   {metadata({method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625f004: 49ba 0889 | 00a7 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625f018: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f018: ffff ffe8 

  0x000002049625f01c: ; ImmutableOopMap {[128]=Oop [152]=Oop [168]=Oop [176]=Oop [192]=Oop [200]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::toString@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@153 (line 972)
  0x000002049625f01c: 6059 3007 | e9c0 c0ff | ff48 8bd2 

  0x000002049625f028: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x000002049625f028: e8d3 0130 

  0x000002049625f02c: ; ImmutableOopMap {[128]=Oop [152]=Oop [168]=Oop [176]=Oop [192]=Oop [200]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::toString@0
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@153 (line 972)
  0x000002049625f02c: 07e9 03c1 

  0x000002049625f030: ;   {metadata({method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625f030: ffff 49ba | a81d 45a7 | 0402 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002049625f048: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f048: ffe8 3259 

  0x000002049625f04c: ; ImmutableOopMap {rax=Oop [128]=Oop [152]=Oop [168]=Oop [176]=Oop [192]=Oop [200]=Oop }
                      ;*synchronization entry
                      ; - java.lang.String::<init>@-1
                      ; - java.lang.StringBuilder::toString@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@153 (line 972)
  0x000002049625f04c: 3007 e923 

  0x000002049625f050: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f050: c1ff ffe8 

  0x000002049625f054: ; ImmutableOopMap {rbx=Oop [128]=Oop [152]=Oop [168]=Oop [176]=Oop [200]=Oop [208]=Oop }
                      ;*invokevirtual trace {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@156 (line 972)
  0x000002049625f054: c8f6 2f07 

  0x000002049625f058: ;   {metadata({method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625f058: 49ba 0002 | 4eea 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625f06c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f06c: ffff ffe8 

  0x000002049625f070: ; ImmutableOopMap {rbx=Oop [128]=Oop [152]=Oop [168]=Oop [176]=Oop [200]=Oop [208]=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@-1 (line 221)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@156 (line 972)
  0x000002049625f070: 0c59 3007 | e9a0 c1ff 

  0x000002049625f078: ;   {runtime_call throw_incompatible_class_change_error Runtime1 stub}
  0x000002049625f078: ffe8 a225 

  0x000002049625f07c: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [168]=Oop [176]=Oop [208]=Oop }
                      ;*invokeinterface getLogger {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@156 (line 972)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f07c: 3007 e89d 

  0x000002049625f080: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [168]=Oop [176]=Oop [208]=Oop }
                      ;*invokeinterface getLogger {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@156 (line 972)
                      ;   {metadata({method} {0x00000204ea507238} 'getLogger' '(Ljava/lang/String;)Lorg/osgi/service/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625f080: f62f 0749 | ba30 7250 | ea04 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002049625f098: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f098: ffff e8e1 

  0x000002049625f09c: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [168]=Oop [176]=Oop [208]=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@-1 (line 1)
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@156 (line 972)
  0x000002049625f09c: 5830 07e9 | 31c2 ffff 

  0x000002049625f0a4: ;   {metadata({method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625f0a4: 49ba b84a | 50ea 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625f0b8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f0b8: ffff ffe8 

  0x000002049625f0bc: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [168]=Oop [176]=Oop [208]=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@-1 (line 73)
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@2 (line 1)
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@156 (line 972)
  0x000002049625f0bc: c058 3007 | e962 c2ff | ff48 8904 

  0x000002049625f0c8: ;   {runtime_call throw_class_cast_exception Runtime1 stub}
  0x000002049625f0c8: 24e8 5222 

  0x000002049625f0cc: ; ImmutableOopMap {[128]=Oop [152]=Oop [168]=Oop [176]=Oop [208]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@7 (line 73)
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@2 (line 1)
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@156 (line 972)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f0cc: 3007 e84d 

  0x000002049625f0d0: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [168]=Oop [176]=Oop [208]=Oop }
                      ;*invokeinterface trace {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@17 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@156 (line 972)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f0d0: f62f 07e8 

  0x000002049625f0d4: ; ImmutableOopMap {rax=Oop rdx=Oop [128]=Oop [152]=Oop [168]=Oop [176]=Oop }
                      ;*invokevirtual println {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@29 (line 232)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@156 (line 972)
  0x000002049625f0d4: 48f6 2f07 | 4889 3424 

  0x000002049625f0dc: ;   {runtime_call throw_class_cast_exception Runtime1 stub}
  0x000002049625f0dc: e83f 2230 

  0x000002049625f0e0: ; ImmutableOopMap {rsi=Oop rbx=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@163 (line 975)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f0e0: 07e8 3af6 

  0x000002049625f0e4: ; ImmutableOopMap {rsi=Oop rbx=Oop r8=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*invokeinterface bundleChanged {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@166 (line 975)
  0x000002049625f0e4: 2f07 4c89 

  0x000002049625f0e8: ;   {runtime_call throw_class_cast_exception Runtime1 stub}
  0x000002049625f0e8: 0c24 e831 

  0x000002049625f0ec: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@297 (line 993)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f0ec: 2230 07e8 

  0x000002049625f0f0: ; ImmutableOopMap {rdx=Oop rbx=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*getfield DEBUG_EVENTS {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@306 (line 995)
  0x000002049625f0f0: 2cf6 2f07 

  0x000002049625f0f4: ;   {metadata(nullptr)}
  0x000002049625f0f4: 48ba 0000 | 0000 0000 | 0000 b800 

  0x000002049625f100: ;   {runtime_call load_klass_patching Runtime1 stub}
  0x000002049625f100: 0f05 0ae8 

  0x000002049625f104: ; ImmutableOopMap {rbx=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*new {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@312 (line 996)
  0x000002049625f104: f841 3007 | e9a3 c7ff | ff48 8bd2 

  0x000002049625f110: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x000002049625f110: e8eb 0030 

  0x000002049625f114: ; ImmutableOopMap {rbx=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@312 (line 996)
  0x000002049625f114: 07e9 e6c7 

  0x000002049625f118: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f118: ffff e801 

  0x000002049625f11c: ; ImmutableOopMap {rbx=Oop rax=Oop [128]=Oop [152]=Oop [176]=Oop [216]=Oop }
                      ;*invokevirtual getClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@318 (line 996)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f11c: f62f 07e8 

  0x000002049625f120: ; ImmutableOopMap {rbx=Oop rax=Oop rdx=Oop [128]=Oop [152]=Oop [176]=Oop [216]=Oop }
                      ;*invokevirtual getName {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@321 (line 996)
  0x000002049625f120: fcf5 2f07 

  0x000002049625f124: ;   {metadata({method} {0x00000204a7465ac0} 'getName' '()Ljava/lang/String;' in 'java/lang/Class')}
  0x000002049625f124: 49ba b85a | 46a7 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625f138: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f138: ffff ffe8 

  0x000002049625f13c: ; ImmutableOopMap {rbx=Oop rax=Oop rdx=Oop [128]=Oop [152]=Oop [176]=Oop [216]=Oop }
                      ;*synchronization entry
                      ; - java.lang.Class::getName@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@321 (line 996)
  0x000002049625f13c: 4058 3007 | e931 c8ff 

  0x000002049625f144: ;   {metadata({method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625f144: ff49 ba50 | 1245 a704 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002049625f15c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f15c: e81f 5830 

  0x000002049625f160: ; ImmutableOopMap {rsi=Oop [128]=Oop [152]=Oop [176]=Oop [216]=Oop [224]=Oop }
                      ;*synchronization entry
                      ; - java.lang.String::valueOf@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@324 (line 996)
  0x000002049625f160: 07e9 cac8 

  0x000002049625f164: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f164: ffff e8b5 

  0x000002049625f168: ; ImmutableOopMap {rsi=Oop [128]=Oop [152]=Oop [176]=Oop [216]=Oop [224]=Oop }
                      ;*invokevirtual toString {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::valueOf@11
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@324 (line 996)
                      ;   {metadata({method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625f168: f52f 0749 | ba30 dd45 | a704 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002049625f180: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f180: ffff e8f9 

  0x000002049625f184: ; ImmutableOopMap {rsi=Oop rax=Oop [128]=Oop [152]=Oop [176]=Oop [216]=Oop [224]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::<init>@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@327 (line 996)
  0x000002049625f184: 5730 07e9 | f1c9 ffff 

  0x000002049625f18c: ;   {metadata({method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625f18c: 49ba 70da | 45a7 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625f1a0: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f1a0: ffff ffe8 

  0x000002049625f1a4: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [216]=Oop [224]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@333 (line 996)
  0x000002049625f1a4: d857 3007 | e942 caff 

  0x000002049625f1ac: ;   {metadata({method} {0x00000204a74126c8} 'toHexString' '(I)Ljava/lang/String;' in 'java/lang/Integer')}
  0x000002049625f1ac: ff49 bac0 | 2641 a704 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002049625f1c4: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f1c4: e8b7 5730 

  0x000002049625f1c8: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [216]=Oop [224]=Oop }
                      ;*synchronization entry
                      ; - java.lang.Integer::toHexString@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@341 (line 997)
  0x000002049625f1c8: 07e9 c4ca 

  0x000002049625f1cc: ;   {metadata({method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625f1cc: ffff 49ba | 70da 45a7 | 0402 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002049625f1e4: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f1e4: ffe8 9657 

  0x000002049625f1e8: ; ImmutableOopMap {rax=Oop [128]=Oop [152]=Oop [176]=Oop [216]=Oop [224]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@344 (line 997)
  0x000002049625f1e8: 3007 e910 

  0x000002049625f1ec: ;   {metadata({method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625f1ec: cbff ff49 | ba08 8900 | a704 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002049625f204: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f204: ffff e875 

  0x000002049625f208: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [216]=Oop [224]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::toString@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@347 (line 996)
  0x000002049625f208: 5730 07e9 | 65cb ffff 

  0x000002049625f210: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x000002049625f210: 488b d2e8 

  0x000002049625f214: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [216]=Oop [224]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::toString@0
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@347 (line 996)
  0x000002049625f214: e8ff 2f07 | e9a8 cbff 

  0x000002049625f21c: ;   {metadata({method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625f21c: ff49 baa8 | 1d45 a704 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002049625f234: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f234: e847 5730 

  0x000002049625f238: ; ImmutableOopMap {rax=Oop [128]=Oop [152]=Oop [176]=Oop [216]=Oop [224]=Oop [232]=Oop }
                      ;*synchronization entry
                      ; - java.lang.String::<init>@-1
                      ; - java.lang.StringBuilder::toString@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@347 (line 996)
  0x000002049625f238: 07e9 d0cb 

  0x000002049625f23c: ;   {metadata(nullptr)}
  0x000002049625f23c: ffff 48ba | 0000 0000 | 0000 0000 | b800 0f05 

  0x000002049625f24c: ;   {runtime_call load_klass_patching Runtime1 stub}
  0x000002049625f24c: 0ae8 ae40 

  0x000002049625f250: ; ImmutableOopMap {rbx=Oop [128]=Oop [152]=Oop [176]=Oop [224]=Oop [232]=Oop [248]=Oop }
                      ;*new {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@359 (line 999)
  0x000002049625f250: 3007 e909 | ccff ff48 

  0x000002049625f258: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x000002049625f258: 8bd2 e8a1 

  0x000002049625f25c: ; ImmutableOopMap {rbx=Oop [128]=Oop [152]=Oop [176]=Oop [224]=Oop [232]=Oop [248]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@359 (line 999)
  0x000002049625f25c: ff2f 07e9 | 4ccc ffff 

  0x000002049625f264: ;   {metadata({method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625f264: 49ba 30dd | 45a7 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625f278: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f278: ffff ffe8 

  0x000002049625f27c: ; ImmutableOopMap {rbx=Oop rax=Oop [128]=Oop [152]=Oop [176]=Oop [224]=Oop [232]=Oop [240]=Oop [248]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::<init>@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@366 (line 999)
  0x000002049625f27c: 0057 3007 | e971 ccff 

  0x000002049625f284: ;   {metadata({method} {0x00000204a745dad0} 'append' '(Ljava/lang/Object;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625f284: ff49 bac8 | da45 a704 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002049625f29c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f29c: e8df 5630 

  0x000002049625f2a0: ; ImmutableOopMap {rdx=Oop rsi=Oop [128]=Oop [152]=Oop [176]=Oop [224]=Oop [232]=Oop [240]=Oop [248]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@373 (line 999)
  0x000002049625f2a0: 07e9 d2cc 

  0x000002049625f2a4: ;   {metadata({method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625f2a4: ffff 49ba | 5012 45a7 | 0402 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002049625f2bc: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f2bc: ffe8 be56 

  0x000002049625f2c0: ; ImmutableOopMap {rdx=Oop rsi=Oop [128]=Oop [152]=Oop [176]=Oop [224]=Oop [232]=Oop [240]=Oop [248]=Oop }
                      ;*synchronization entry
                      ; - java.lang.String::valueOf@-1
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@373 (line 999)
  0x000002049625f2c0: 3007 e9ea 

  0x000002049625f2c4: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f2c4: ccff ffe8 

  0x000002049625f2c8: ; ImmutableOopMap {rsi=Oop [128]=Oop [152]=Oop [176]=Oop [224]=Oop [232]=Oop [240]=Oop [248]=Oop }
                      ;*invokevirtual toString {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::valueOf@11
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@373 (line 999)
  0x000002049625f2c8: 54f4 2f07 

  0x000002049625f2cc: ;   {metadata({method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625f2cc: 49ba 70da | 45a7 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625f2e0: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f2e0: ffff ffe8 

  0x000002049625f2e4: ; ImmutableOopMap {rsi=Oop [128]=Oop [152]=Oop [176]=Oop [224]=Oop [232]=Oop [240]=Oop [248]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - java.lang.StringBuilder::append@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@373 (line 999)
  0x000002049625f2e4: 9856 3007 | e905 ceff 

  0x000002049625f2ec: ;   {metadata({method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625f2ec: ff49 ba70 | da45 a704 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002049625f304: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f304: e877 5630 

  0x000002049625f308: ; ImmutableOopMap {rax=Oop [128]=Oop [152]=Oop [176]=Oop [224]=Oop [232]=Oop [240]=Oop [248]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@379 (line 999)
  0x000002049625f308: 07e9 5cce 

  0x000002049625f30c: ;   {metadata({method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625f30c: ffff 49ba | 70da 45a7 | 0402 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002049625f324: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f324: ffe8 5656 

  0x000002049625f328: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [224]=Oop [232]=Oop [240]=Oop [248]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@384 (line 999)
  0x000002049625f328: 3007 e9b0 

  0x000002049625f32c: ;   {metadata({method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625f32c: ceff ff49 | ba70 da45 | a704 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002049625f344: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f344: ffff e835 

  0x000002049625f348: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [224]=Oop [240]=Oop [248]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@390 (line 999)
  0x000002049625f348: 5630 07e9 | 0fcf ffff 

  0x000002049625f350: ;   {metadata({method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625f350: 49ba 0889 | 00a7 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625f364: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f364: ffff ffe8 

  0x000002049625f368: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [224]=Oop [240]=Oop [248]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::toString@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@393 (line 999)
  0x000002049625f368: 1456 3007 | e96c cfff | ff48 8bd2 

  0x000002049625f374: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x000002049625f374: e887 fe2f 

  0x000002049625f378: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [224]=Oop [240]=Oop [248]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::toString@0
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@393 (line 999)
  0x000002049625f378: 07e9 afcf 

  0x000002049625f37c: ;   {metadata({method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625f37c: ffff 49ba | a81d 45a7 | 0402 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002049625f394: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f394: ffe8 e655 

  0x000002049625f398: ; ImmutableOopMap {rax=Oop [128]=Oop [152]=Oop [176]=Oop [224]=Oop [240]=Oop [248]=Oop }
                      ;*synchronization entry
                      ; - java.lang.String::<init>@-1
                      ; - java.lang.StringBuilder::toString@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@393 (line 999)
  0x000002049625f398: 3007 e9cf 

  0x000002049625f39c: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f39c: cfff ffe8 

  0x000002049625f3a0: ; ImmutableOopMap {rbx=Oop [128]=Oop [152]=Oop [176]=Oop [224]=Oop [248]=Oop [256]=Oop }
                      ;*invokevirtual trace {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@396 (line 998)
  0x000002049625f3a0: 7cf3 2f07 

  0x000002049625f3a4: ;   {metadata({method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625f3a4: 49ba 0002 | 4eea 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625f3b8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f3b8: ffff ffe8 

  0x000002049625f3bc: ; ImmutableOopMap {rbx=Oop [128]=Oop [152]=Oop [176]=Oop [224]=Oop [248]=Oop [256]=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@-1 (line 221)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@396 (line 998)
  0x000002049625f3bc: c055 3007 | e94c d0ff 

  0x000002049625f3c4: ;   {runtime_call throw_incompatible_class_change_error Runtime1 stub}
  0x000002049625f3c4: ffe8 5622 

  0x000002049625f3c8: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [176]=Oop [224]=Oop [256]=Oop }
                      ;*invokeinterface getLogger {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@396 (line 998)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f3c8: 3007 e851 

  0x000002049625f3cc: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [176]=Oop [224]=Oop [256]=Oop }
                      ;*invokeinterface getLogger {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@396 (line 998)
                      ;   {metadata({method} {0x00000204ea507238} 'getLogger' '(Ljava/lang/String;)Lorg/osgi/service/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625f3cc: f32f 0749 | ba30 7250 | ea04 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002049625f3e4: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f3e4: ffff e895 

  0x000002049625f3e8: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [176]=Oop [224]=Oop [256]=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@-1 (line 1)
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@396 (line 998)
  0x000002049625f3e8: 5530 07e9 | ddd0 ffff 

  0x000002049625f3f0: ;   {metadata({method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625f3f0: 49ba b84a | 50ea 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625f404: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f404: ffff ffe8 

  0x000002049625f408: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [176]=Oop [224]=Oop [256]=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@-1 (line 73)
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@2 (line 1)
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@396 (line 998)
  0x000002049625f408: 7455 3007 | e90e d1ff | ff48 8904 

  0x000002049625f414: ;   {runtime_call throw_class_cast_exception Runtime1 stub}
  0x000002049625f414: 24e8 061f 

  0x000002049625f418: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [224]=Oop [256]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@7 (line 73)
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@2 (line 1)
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@396 (line 998)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f418: 3007 e801 

  0x000002049625f41c: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [176]=Oop [224]=Oop [256]=Oop }
                      ;*invokeinterface trace {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@17 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@396 (line 998)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f41c: f32f 07e8 

  0x000002049625f420: ; ImmutableOopMap {rax=Oop rdx=Oop [128]=Oop [152]=Oop [176]=Oop [224]=Oop }
                      ;*invokevirtual println {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@29 (line 232)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@396 (line 998)
  0x000002049625f420: fcf2 2f07 | 4889 3424 

  0x000002049625f428: ;   {runtime_call throw_class_cast_exception Runtime1 stub}
  0x000002049625f428: e8f3 1e30 

  0x000002049625f42c: ; ImmutableOopMap {rbx=Oop rsi=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@403 (line 1002)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f42c: 07e8 eef2 

  0x000002049625f430: ; ImmutableOopMap {rbx=Oop rsi=Oop r8=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*invokeinterface frameworkEvent {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@406 (line 1002)
  0x000002049625f430: 2f07 4889 

  0x000002049625f434: ;   {runtime_call throw_class_cast_exception Runtime1 stub}
  0x000002049625f434: 3424 e8e5 

  0x000002049625f438: ; ImmutableOopMap {rsi=Oop rdx=Oop r9=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@176 (line 980)
  0x000002049625f438: 1e30 074c 

  0x000002049625f43c: ;   {runtime_call throw_class_cast_exception Runtime1 stub}
  0x000002049625f43c: 890c 24e8 

  0x000002049625f440: ; ImmutableOopMap {rsi=Oop rdx=Oop rbx=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@182 (line 982)
  0x000002049625f440: dc1e 3007 

  0x000002049625f444: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f444: e8d7 f22f 

  0x000002049625f448: ; ImmutableOopMap {rsi=Oop rdx=Oop rbx=Oop r8=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*getfield DEBUG_EVENTS {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@191 (line 983)
                      ;   {metadata(nullptr)}
  0x000002049625f448: 0748 ba00 | 0000 0000 | 0000 00b8 | 000f 050a 

  0x000002049625f458: ;   {runtime_call load_klass_patching Runtime1 stub}
  0x000002049625f458: e8a3 3e30 

  0x000002049625f45c: ; ImmutableOopMap {rbx=Oop r8=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*new {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@197 (line 984)
  0x000002049625f45c: 07e9 3ed7 | ffff 488b 

  0x000002049625f464: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x000002049625f464: d2e8 96fd 

  0x000002049625f468: ; ImmutableOopMap {rbx=Oop r8=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@197 (line 984)
  0x000002049625f468: 2f07 e981 

  0x000002049625f46c: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f46c: d7ff ffe8 

  0x000002049625f470: ; ImmutableOopMap {rbx=Oop r8=Oop rax=Oop [128]=Oop [152]=Oop [176]=Oop [264]=Oop }
                      ;*invokevirtual getClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@203 (line 984)
  0x000002049625f470: acf2 2f07 

  0x000002049625f474: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f474: e8a7 f22f 

  0x000002049625f478: ; ImmutableOopMap {rbx=Oop r8=Oop rax=Oop rdx=Oop [128]=Oop [152]=Oop [176]=Oop [264]=Oop }
                      ;*invokevirtual getName {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@206 (line 984)
                      ;   {metadata({method} {0x00000204a7465ac0} 'getName' '()Ljava/lang/String;' in 'java/lang/Class')}
  0x000002049625f478: 0749 bab8 | 5a46 a704 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002049625f490: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f490: e8eb 5430 

  0x000002049625f494: ; ImmutableOopMap {rbx=Oop r8=Oop rax=Oop rdx=Oop [128]=Oop [152]=Oop [176]=Oop [264]=Oop }
                      ;*synchronization entry
                      ; - java.lang.Class::getName@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@206 (line 984)
  0x000002049625f494: 07e9 cdd7 

  0x000002049625f498: ;   {metadata({method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625f498: ffff 49ba | 5012 45a7 | 0402 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002049625f4b0: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f4b0: ffe8 ca54 

  0x000002049625f4b4: ; ImmutableOopMap {rsi=Oop [128]=Oop [152]=Oop [176]=Oop [264]=Oop [272]=Oop [280]=Oop }
                      ;*synchronization entry
                      ; - java.lang.String::valueOf@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@209 (line 984)
  0x000002049625f4b4: 3007 e975 

  0x000002049625f4b8: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f4b8: d8ff ffe8 

  0x000002049625f4bc: ; ImmutableOopMap {rsi=Oop [128]=Oop [152]=Oop [176]=Oop [264]=Oop [272]=Oop [280]=Oop }
                      ;*invokevirtual toString {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::valueOf@11
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@209 (line 984)
  0x000002049625f4bc: 60f2 2f07 

  0x000002049625f4c0: ;   {metadata({method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625f4c0: 49ba 30dd | 45a7 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625f4d4: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f4d4: ffff ffe8 

  0x000002049625f4d8: ; ImmutableOopMap {rsi=Oop rax=Oop [128]=Oop [152]=Oop [176]=Oop [264]=Oop [272]=Oop [280]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::<init>@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@212 (line 984)
  0x000002049625f4d8: a454 3007 | e99c d9ff 

  0x000002049625f4e0: ;   {metadata({method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625f4e0: ff49 ba70 | da45 a704 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002049625f4f8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f4f8: e883 5430 

  0x000002049625f4fc: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [264]=Oop [272]=Oop [280]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@218 (line 984)
  0x000002049625f4fc: 07e9 edd9 

  0x000002049625f500: ;   {metadata({method} {0x00000204a74126c8} 'toHexString' '(I)Ljava/lang/String;' in 'java/lang/Integer')}
  0x000002049625f500: ffff 49ba | c026 41a7 | 0402 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002049625f518: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f518: ffe8 6254 

  0x000002049625f51c: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [264]=Oop [272]=Oop [280]=Oop }
                      ;*synchronization entry
                      ; - java.lang.Integer::toHexString@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@226 (line 985)
  0x000002049625f51c: 3007 e96f 

  0x000002049625f520: ;   {metadata({method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625f520: daff ff49 | ba70 da45 | a704 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002049625f538: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f538: ffff e841 

  0x000002049625f53c: ; ImmutableOopMap {rax=Oop [128]=Oop [152]=Oop [176]=Oop [264]=Oop [272]=Oop [280]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@229 (line 985)
  0x000002049625f53c: 5430 07e9 | bbda ffff 

  0x000002049625f544: ;   {metadata({method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625f544: 49ba 0889 | 00a7 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625f558: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f558: ffff ffe8 

  0x000002049625f55c: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [264]=Oop [272]=Oop [280]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::toString@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@232 (line 984)
  0x000002049625f55c: 2054 3007 | e910 dbff | ff48 8bd2 

  0x000002049625f568: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x000002049625f568: e893 fc2f 

  0x000002049625f56c: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [264]=Oop [272]=Oop [280]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::toString@0
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@232 (line 984)
  0x000002049625f56c: 07e9 53db 

  0x000002049625f570: ;   {metadata({method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625f570: ffff 49ba | a81d 45a7 | 0402 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002049625f588: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f588: ffe8 f253 

  0x000002049625f58c: ; ImmutableOopMap {rax=Oop [128]=Oop [152]=Oop [176]=Oop [264]=Oop [272]=Oop [280]=Oop [288]=Oop }
                      ;*synchronization entry
                      ; - java.lang.String::<init>@-1
                      ; - java.lang.StringBuilder::toString@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@232 (line 984)
  0x000002049625f58c: 3007 e97b 

  0x000002049625f590: ;   {metadata(nullptr)}
  0x000002049625f590: dbff ff48 | ba00 0000 | 0000 0000 | 00b8 000f 

  0x000002049625f5a0: ;   {runtime_call load_klass_patching Runtime1 stub}
  0x000002049625f5a0: 050a e859 

  0x000002049625f5a4: ; ImmutableOopMap {rbx=Oop [128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [288]=Oop [304]=Oop }
                      ;*new {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@244 (line 986)
  0x000002049625f5a4: 3d30 07e9 | b4db ffff 

  0x000002049625f5ac: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x000002049625f5ac: 488b d2e8 

  0x000002049625f5b0: ; ImmutableOopMap {rbx=Oop [128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [288]=Oop [304]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@244 (line 986)
  0x000002049625f5b0: 4cfc 2f07 | e9f7 dbff 

  0x000002049625f5b8: ;   {metadata({method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625f5b8: ff49 ba30 | dd45 a704 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002049625f5d0: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f5d0: e8ab 5330 

  0x000002049625f5d4: ; ImmutableOopMap {rbx=Oop rax=Oop [128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [288]=Oop [296]=Oop [304]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::<init>@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@251 (line 986)
  0x000002049625f5d4: 07e9 1cdc 

  0x000002049625f5d8: ;   {metadata({method} {0x00000204a745dad0} 'append' '(Ljava/lang/Object;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625f5d8: ffff 49ba | c8da 45a7 | 0402 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002049625f5f0: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f5f0: ffe8 8a53 

  0x000002049625f5f4: ; ImmutableOopMap {rdx=Oop rsi=Oop [128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [288]=Oop [296]=Oop [304]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@258 (line 986)
  0x000002049625f5f4: 3007 e97d 

  0x000002049625f5f8: ;   {metadata({method} {0x00000204a7451258} 'valueOf' '(Ljava/lang/Object;)Ljava/lang/String;' in 'java/lang/String')}
  0x000002049625f5f8: dcff ff49 | ba50 1245 | a704 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002049625f610: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f610: ffff e869 

  0x000002049625f614: ; ImmutableOopMap {rdx=Oop rsi=Oop [128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [288]=Oop [296]=Oop [304]=Oop }
                      ;*synchronization entry
                      ; - java.lang.String::valueOf@-1
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@258 (line 986)
  0x000002049625f614: 5330 07e9 | 95dc ffff 

  0x000002049625f61c: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f61c: e8ff f02f 

  0x000002049625f620: ; ImmutableOopMap {rsi=Oop [128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [288]=Oop [296]=Oop [304]=Oop }
                      ;*invokevirtual toString {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.String::valueOf@11
                      ; - java.lang.StringBuilder::append@2
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@258 (line 986)
                      ;   {metadata({method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625f620: 0749 ba70 | da45 a704 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002049625f638: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f638: e843 5330 

  0x000002049625f63c: ; ImmutableOopMap {rsi=Oop [128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [288]=Oop [296]=Oop [304]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - java.lang.StringBuilder::append@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@258 (line 986)
  0x000002049625f63c: 07e9 b0dd 

  0x000002049625f640: ;   {metadata({method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625f640: ffff 49ba | 70da 45a7 | 0402 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002049625f658: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f658: ffe8 2253 

  0x000002049625f65c: ; ImmutableOopMap {rax=Oop [128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [288]=Oop [296]=Oop [304]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@264 (line 986)
  0x000002049625f65c: 3007 e907 

  0x000002049625f660: ;   {metadata({method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625f660: deff ff49 | ba70 da45 | a704 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002049625f678: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f678: ffff e801 

  0x000002049625f67c: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [288]=Oop [296]=Oop [304]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@269 (line 986)
  0x000002049625f67c: 5330 07e9 | 5bde ffff 

  0x000002049625f684: ;   {metadata({method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625f684: 49ba 70da | 45a7 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625f698: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f698: ffff ffe8 

  0x000002049625f69c: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [296]=Oop [304]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@275 (line 986)
  0x000002049625f69c: e052 3007 | e9ba deff 

  0x000002049625f6a4: ;   {metadata({method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625f6a4: ff49 ba08 | 8900 a704 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002049625f6bc: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f6bc: e8bf 5230 

  0x000002049625f6c0: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [296]=Oop [304]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::toString@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@278 (line 986)
  0x000002049625f6c0: 07e9 17df | ffff 488b 

  0x000002049625f6c8: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x000002049625f6c8: d2e8 32fb 

  0x000002049625f6cc: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [296]=Oop [304]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::toString@0
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@278 (line 986)
  0x000002049625f6cc: 2f07 e95a 

  0x000002049625f6d0: ;   {metadata({method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625f6d0: dfff ff49 | baa8 1d45 | a704 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002049625f6e8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f6e8: ffff e891 

  0x000002049625f6ec: ; ImmutableOopMap {rax=Oop [128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [296]=Oop [304]=Oop }
                      ;*synchronization entry
                      ; - java.lang.String::<init>@-1
                      ; - java.lang.StringBuilder::toString@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@278 (line 986)
  0x000002049625f6ec: 5230 07e9 | 7adf ffff 

  0x000002049625f6f4: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f6f4: e827 f02f 

  0x000002049625f6f8: ; ImmutableOopMap {rbx=Oop [128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [304]=Oop [312]=Oop }
                      ;*invokevirtual trace {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@281 (line 986)
                      ;   {metadata({method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625f6f8: 0749 ba00 | 024e ea04 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002049625f710: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f710: e86b 5230 

  0x000002049625f714: ; ImmutableOopMap {rbx=Oop [128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [304]=Oop [312]=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@-1 (line 221)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@281 (line 986)
  0x000002049625f714: 07e9 f7df 

  0x000002049625f718: ;   {runtime_call throw_incompatible_class_change_error Runtime1 stub}
  0x000002049625f718: ffff e801 

  0x000002049625f71c: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [312]=Oop }
                      ;*invokeinterface getLogger {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@281 (line 986)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f71c: 1f30 07e8 

  0x000002049625f720: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [312]=Oop }
                      ;*invokeinterface getLogger {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@281 (line 986)
  0x000002049625f720: fcef 2f07 

  0x000002049625f724: ;   {metadata({method} {0x00000204ea507238} 'getLogger' '(Ljava/lang/String;)Lorg/osgi/service/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625f724: 49ba 3072 | 50ea 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625f738: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f738: ffff ffe8 

  0x000002049625f73c: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [312]=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@-1 (line 1)
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@281 (line 986)
  0x000002049625f73c: 4052 3007 | e988 e0ff 

  0x000002049625f744: ;   {metadata({method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625f744: ff49 bab8 | 4a50 ea04 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002049625f75c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f75c: e81f 5230 

  0x000002049625f760: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [312]=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@-1 (line 73)
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@2 (line 1)
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@281 (line 986)
  0x000002049625f760: 07e9 b9e0 | ffff 4889 

  0x000002049625f768: ;   {runtime_call throw_class_cast_exception Runtime1 stub}
  0x000002049625f768: 0424 e8b1 

  0x000002049625f76c: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [312]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@7 (line 73)
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@2 (line 1)
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@281 (line 986)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f76c: 1b30 07e8 

  0x000002049625f770: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop [312]=Oop }
                      ;*invokeinterface trace {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@17 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@281 (line 986)
  0x000002049625f770: acef 2f07 

  0x000002049625f774: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f774: e8a7 ef2f 

  0x000002049625f778: ; ImmutableOopMap {rax=Oop rdx=Oop [128]=Oop [152]=Oop [176]=Oop [272]=Oop [280]=Oop }
                      ;*invokevirtual println {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@29 (line 232)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@281 (line 986)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f778: 07e8 a2ef 

  0x000002049625f77c: ; ImmutableOopMap {rbx=Oop rdx=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*invokeinterface serviceChanged {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@288 (line 988)
  0x000002049625f77c: 2f07 4889 

  0x000002049625f780: ;   {runtime_call throw_class_cast_exception Runtime1 stub}
  0x000002049625f780: 0424 e899 

  0x000002049625f784: ; ImmutableOopMap {}
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@570 (line 1027)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f784: 1b30 07e8 

  0x000002049625f788: ; ImmutableOopMap {r8=Oop rdx=Oop }
                      ;*invokevirtual setContextClassLoader {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@573 (line 1027)
  0x000002049625f788: 94ef 2f07 

  0x000002049625f78c: ;   {internal_word}
  0x000002049625f78c: 49ba 6cdd | 2596 0402 | 0000 4d89 | 9760 0400 

  0x000002049625f79c: ;   {runtime_call SafepointBlob}
  0x000002049625f79c: 00e9 de63 

  0x000002049625f7a0: ;   {metadata(nullptr)}
  0x000002049625f7a0: 2507 48ba | 0000 0000 | 0000 0000 | b800 0f05 

  0x000002049625f7b0: ;   {runtime_call load_klass_patching Runtime1 stub}
  0x000002049625f7b0: 0ae8 4a3b 

  0x000002049625f7b4: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop }
                      ;*new {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@414 (line 1006)
  0x000002049625f7b4: 3007 e9c5 | e5ff ff48 

  0x000002049625f7bc: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x000002049625f7bc: 8bd2 e83d 

  0x000002049625f7c0: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@414 (line 1006)
  0x000002049625f7c0: fa2f 07e9 | 10e6 ffff 

  0x000002049625f7c8: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f7c8: e853 ef2f 

  0x000002049625f7cc: ; ImmutableOopMap {rdx=Oop rax=Oop rbx=Oop [128]=Oop [152]=Oop [176]=Oop }
                      ;*getfield DEBUG_EVENTS {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@428 (line 1011)
                      ;   {metadata(nullptr)}
  0x000002049625f7cc: 0748 ba00 | 0000 0000 | 0000 00b8 | 000f 050a 

  0x000002049625f7dc: ;   {runtime_call load_klass_patching Runtime1 stub}
  0x000002049625f7dc: e81f 3b30 

  0x000002049625f7e0: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [328]=Oop [344]=Oop }
                      ;*new {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@441 (line 1012)
  0x000002049625f7e0: 07e9 bae6 | ffff 488b 

  0x000002049625f7e8: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x000002049625f7e8: d2e8 12fa 

  0x000002049625f7ec: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [328]=Oop [344]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@441 (line 1012)
  0x000002049625f7ec: 2f07 e9fd 

  0x000002049625f7f0: ;   {metadata({method} {0x00000204a745dd38} '<init>' '(Ljava/lang/String;)V' in 'java/lang/StringBuilder')}
  0x000002049625f7f0: e6ff ff49 | ba30 dd45 | a704 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002049625f808: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f808: ffff e871 

  0x000002049625f80c: ; ImmutableOopMap {rax=Oop [128]=Oop [152]=Oop [176]=Oop [328]=Oop [344]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::<init>@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@448 (line 1012)
  0x000002049625f80c: 5130 07e9 | 1ae7 ffff 

  0x000002049625f814: ;   {metadata({method} {0x00000204a745da78} 'append' '(Ljava/lang/String;)Ljava/lang/StringBuilder;' in 'java/lang/StringBuilder')}
  0x000002049625f814: 49ba 70da | 45a7 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625f828: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f828: ffff ffe8 

  0x000002049625f82c: ; ImmutableOopMap {rax=Oop [128]=Oop [152]=Oop [176]=Oop [328]=Oop [336]=Oop [344]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::append@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@456 (line 1012)
  0x000002049625f82c: 5051 3007 | e932 e8ff 

  0x000002049625f834: ;   {metadata({method} {0x00000204a7008910} 'toString' '()Ljava/lang/String;' in 'java/lang/StringBuilder')}
  0x000002049625f834: ff49 ba08 | 8900 a704 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002049625f84c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f84c: e82f 5130 

  0x000002049625f850: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [328]=Oop [336]=Oop [344]=Oop }
                      ;*synchronization entry
                      ; - java.lang.StringBuilder::toString@-1
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@459 (line 1012)
  0x000002049625f850: 07e9 87e8 | ffff 488b 

  0x000002049625f858: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x000002049625f858: d2e8 a2f9 

  0x000002049625f85c: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [328]=Oop [336]=Oop [344]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.StringBuilder::toString@0
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@459 (line 1012)
  0x000002049625f85c: 2f07 e9ca 

  0x000002049625f860: ;   {metadata({method} {0x00000204a7451db0} '<init>' '(Ljava/lang/StringBuilder;)V' in 'java/lang/String')}
  0x000002049625f860: e8ff ff49 | baa8 1d45 | a704 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002049625f878: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f878: ffff e801 

  0x000002049625f87c: ; ImmutableOopMap {rax=Oop [128]=Oop [152]=Oop [176]=Oop [328]=Oop [336]=Oop [344]=Oop }
                      ;*synchronization entry
                      ; - java.lang.String::<init>@-1
                      ; - java.lang.StringBuilder::toString@5
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@459 (line 1012)
  0x000002049625f87c: 5130 07e9 | eae8 ffff 

  0x000002049625f884: ;   {metadata({method} {0x00000204ea4e0208} 'trace' '(Ljava/lang/String;Ljava/lang/String;)V' in 'org/eclipse/osgi/internal/debug/Debug')}
  0x000002049625f884: 49ba 0002 | 4eea 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625f898: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f898: ffff ffe8 

  0x000002049625f89c: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [328]=Oop [344]=Oop [352]=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@-1 (line 221)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@462 (line 1012)
  0x000002049625f89c: e050 3007 | e966 e9ff 

  0x000002049625f8a4: ;   {runtime_call throw_incompatible_class_change_error Runtime1 stub}
  0x000002049625f8a4: ffe8 761d 

  0x000002049625f8a8: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [176]=Oop [328]=Oop [352]=Oop }
                      ;*invokeinterface getLogger {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@462 (line 1012)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f8a8: 3007 e871 

  0x000002049625f8ac: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [176]=Oop [328]=Oop [352]=Oop }
                      ;*invokeinterface getLogger {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@462 (line 1012)
                      ;   {metadata({method} {0x00000204ea507238} 'getLogger' '(Ljava/lang/String;)Lorg/osgi/service/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625f8ac: ee2f 0749 | ba30 7250 | ea04 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002049625f8c4: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f8c4: ffff e8b5 

  0x000002049625f8c8: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [176]=Oop [328]=Oop [352]=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@-1 (line 1)
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@462 (line 1012)
  0x000002049625f8c8: 5030 07e9 | ffe9 ffff 

  0x000002049625f8d0: ;   {metadata({method} {0x00000204ea504ac0} 'getLogger' '(Ljava/lang/String;)Lorg/eclipse/equinox/log/Logger;' in 'org/eclipse/osgi/internal/log/ExtendedLogServiceImpl')}
  0x000002049625f8d0: 49ba b84a | 50ea 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625f8e4: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f8e4: ffff ffe8 

  0x000002049625f8e8: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [176]=Oop [328]=Oop [352]=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@-1 (line 73)
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@2 (line 1)
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@462 (line 1012)
  0x000002049625f8e8: 9450 3007 | e930 eaff | ff48 8904 

  0x000002049625f8f4: ;   {runtime_call throw_class_cast_exception Runtime1 stub}
  0x000002049625f8f4: 24e8 261a 

  0x000002049625f8f8: ; ImmutableOopMap {[128]=Oop [152]=Oop [176]=Oop [328]=Oop [352]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@7 (line 73)
                      ; - org.eclipse.osgi.internal.log.ExtendedLogServiceImpl::getLogger@2 (line 1)
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@11 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@462 (line 1012)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f8f8: 3007 e821 

  0x000002049625f8fc: ; ImmutableOopMap {rdx=Oop [128]=Oop [152]=Oop [176]=Oop [328]=Oop [352]=Oop }
                      ;*invokeinterface trace {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@17 (line 230)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@462 (line 1012)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f8fc: ee2f 07e8 

  0x000002049625f900: ; ImmutableOopMap {rax=Oop rdx=Oop [128]=Oop [152]=Oop [176]=Oop [328]=Oop }
                      ;*invokevirtual println {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.debug.Debug::trace@29 (line 232)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@462 (line 1012)
  0x000002049625f900: 1cee 2f07 

  0x000002049625f904: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f904: e817 ee2f 

  0x000002049625f908: ; ImmutableOopMap {rdx=Oop rsi=Oop [128]=Oop [152]=Oop [176]=Oop [328]=Oop }
                      ;*invokevirtual traceThrowable {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@474 (line 1016)
  0x000002049625f908: 0748 8934 

  0x000002049625f90c: ;   {runtime_call throw_class_cast_exception Runtime1 stub}
  0x000002049625f90c: 24e8 0e1a 

  0x000002049625f910: ; ImmutableOopMap {[128]=Oop [176]=Oop [328]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@484 (line 1020)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f910: 3007 e809 

  0x000002049625f914: ; ImmutableOopMap {rax=Oop [128]=Oop [176]=Oop [328]=Oop }
                      ;*invokevirtual getType {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@487 (line 1020)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f914: ee2f 07e8 

  0x000002049625f918: ; ImmutableOopMap {rsi=Oop r8=Oop [128]=Oop [176]=Oop [328]=Oop }
                      ;*invokevirtual getEventPublisher {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@498 (line 1023)
  0x000002049625f918: 04ee 2f07 

  0x000002049625f91c: ;   {metadata({method} {0x00000204ea4a2110} 'getEventPublisher' '()Lorg/eclipse/osgi/internal/framework/EquinoxEventPublisher;' in 'org/eclipse/osgi/internal/framework/EquinoxContainer')}
  0x000002049625f91c: 49ba 0821 | 4aea 0402 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002049625f930: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002049625f930: ffff ffe8 

  0x000002049625f934: ; ImmutableOopMap {rsi=Oop r8=Oop [128]=Oop [176]=Oop [328]=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.framework.EquinoxContainer::getEventPublisher@-1 (line 275)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@498 (line 1023)
  0x000002049625f934: 4850 3007 | e995 eeff 

  0x000002049625f93c: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f93c: ffe8 deed 

  0x000002049625f940: ; ImmutableOopMap {rsi=Oop r8=Oop r9=Oop [128]=Oop [176]=Oop [328]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.EquinoxContainer::getEventPublisher@6 (line 275)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@498 (line 1023)
  0x000002049625f940: 2f07 4c89 | 4c24 0848 

  0x000002049625f948: ;   {runtime_call monitorenter_nofpu Runtime1 stub}
  0x000002049625f948: 893c 24e8 

  0x000002049625f94c: ; ImmutableOopMap {rsi=Oop r8=Oop r9=Oop [128]=Oop [176]=Oop [328]=Oop [376]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.EquinoxContainer::getEventPublisher@6 (line 275)
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@498 (line 1023)
  0x000002049625f94c: 3026 3007 | e9bb eeff | ff48 8d84 | 2470 0100 | 0048 8904 

  0x000002049625f960: ;   {runtime_call monitorexit_nofpu Runtime1 stub}
  0x000002049625f960: 24e8 9a2c | 3007 e9d3 

  0x000002049625f968: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f968: eeff ffe8 

  0x000002049625f96c: ; ImmutableOopMap {rdx=Oop r9=Oop [176]=Oop [328]=Oop }
                      ;*invokevirtual publishFrameworkEvent {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@508 (line 1023)
  0x000002049625f96c: b0ed 2f07 | 4889 3c24 

  0x000002049625f974: ;   {runtime_call throw_class_cast_exception Runtime1 stub}
  0x000002049625f974: e8a7 1930 

  0x000002049625f978: ; ImmutableOopMap {}
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@524 (line 1027)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f978: 07e8 a2ed 

  0x000002049625f97c: ; ImmutableOopMap {r8=Oop rdx=Oop }
                      ;*invokevirtual setContextClassLoader {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@527 (line 1027)
                      ;   {internal_word}
  0x000002049625f97c: 2f07 49ba | 9cea 2596 | 0402 0000 | 4d89 9760 

  0x000002049625f98c: ;   {runtime_call SafepointBlob}
  0x000002049625f98c: 0400 00e9 | ec61 2507 | 488d 8424 | 7001 0000 | 4889 0424 

  0x000002049625f9a0: ;   {runtime_call monitorexit_nofpu Runtime1 stub}
  0x000002049625f9a0: e85b 2c30 | 07e9 58f1 | ffff 4889 

  0x000002049625f9ac: ;   {runtime_call throw_class_cast_exception Runtime1 stub}
  0x000002049625f9ac: 3c24 e86d 

  0x000002049625f9b0: ; ImmutableOopMap {[360]=Oop }
                      ;*checkcast {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@548 (line 1027)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002049625f9b0: 1930 07e8 

  0x000002049625f9b4: ; ImmutableOopMap {r8=Oop rdx=Oop [360]=Oop }
                      ;*invokevirtual setContextClassLoader {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent@551 (line 1027)
  0x000002049625f9b4: 68ed 2f07 | 498b 87f8 | 0400 0049 | c787 f804 | 0000 0000 | 0000 49c7 | 8700 0500 | 0000 0000 
  0x000002049625f9d4: 0048 81c4 | 9001 0000 

  0x000002049625f9dc: ;   {runtime_call unwind_exception Runtime1 stub}
  0x000002049625f9dc: 5de9 1ede | 2f07 f4f4 | f4f4 f4f4 
[Stub Code]
  0x000002049625f9e8: ;   {no_reloc}
  0x000002049625f9e8: 0f1f 4400 

  0x000002049625f9ec: ;   {static_stub}
  0x000002049625f9ec: 0048 bb00 | 0000 0000 

  0x000002049625f9f4: ;   {runtime_call nmethod}
  0x000002049625f9f4: 0000 00e9 | fbff ffff 

  0x000002049625f9fc: ;   {static_stub}
  0x000002049625f9fc: 9048 bb00 | 0000 0000 

  0x000002049625fa04: ;   {runtime_call nmethod}
  0x000002049625fa04: 0000 00e9 | fbff ffff 

  0x000002049625fa0c: ;   {static_stub}
  0x000002049625fa0c: 9048 bb00 | 0000 0000 

  0x000002049625fa14: ;   {runtime_call nmethod}
  0x000002049625fa14: 0000 00e9 | fbff ffff 

  0x000002049625fa1c: ;   {static_stub}
  0x000002049625fa1c: 9048 bb00 | 0000 0000 

  0x000002049625fa24: ;   {runtime_call nmethod}
  0x000002049625fa24: 0000 00e9 | fbff ffff 

  0x000002049625fa2c: ;   {static_stub}
  0x000002049625fa2c: 9048 bb00 | 0000 0000 

  0x000002049625fa34: ;   {runtime_call nmethod}
  0x000002049625fa34: 0000 00e9 | fbff ffff 

  0x000002049625fa3c: ;   {static_stub}
  0x000002049625fa3c: 9048 bb00 | 0000 0000 

  0x000002049625fa44: ;   {runtime_call nmethod}
  0x000002049625fa44: 0000 00e9 | fbff ffff 

  0x000002049625fa4c: ;   {static_stub}
  0x000002049625fa4c: 9048 bb00 | 0000 0000 

  0x000002049625fa54: ;   {runtime_call nmethod}
  0x000002049625fa54: 0000 00e9 | fbff ffff 

  0x000002049625fa5c: ;   {static_stub}
  0x000002049625fa5c: 9048 bb00 | 0000 0000 

  0x000002049625fa64: ;   {runtime_call nmethod}
  0x000002049625fa64: 0000 00e9 | fbff ffff 

  0x000002049625fa6c: ;   {static_stub}
  0x000002049625fa6c: 9048 bb00 | 0000 0000 

  0x000002049625fa74: ;   {runtime_call nmethod}
  0x000002049625fa74: 0000 00e9 | fbff ffff 

  0x000002049625fa7c: ;   {static_stub}
  0x000002049625fa7c: 9048 bb00 | 0000 0000 

  0x000002049625fa84: ;   {runtime_call nmethod}
  0x000002049625fa84: 0000 00e9 | fbff ffff 

  0x000002049625fa8c: ;   {static_stub}
  0x000002049625fa8c: 9048 bb00 | 0000 0000 

  0x000002049625fa94: ;   {runtime_call nmethod}
  0x000002049625fa94: 0000 00e9 | fbff ffff 

  0x000002049625fa9c: ;   {static_stub}
  0x000002049625fa9c: 9048 bb00 | 0000 0000 

  0x000002049625faa4: ;   {runtime_call nmethod}
  0x000002049625faa4: 0000 00e9 | fbff ffff 

  0x000002049625faac: ;   {static_stub}
  0x000002049625faac: 9048 bb00 | 0000 0000 

  0x000002049625fab4: ;   {runtime_call nmethod}
  0x000002049625fab4: 0000 00e9 | fbff ffff 

  0x000002049625fabc: ;   {static_stub}
  0x000002049625fabc: 9048 bb00 | 0000 0000 

  0x000002049625fac4: ;   {runtime_call nmethod}
  0x000002049625fac4: 0000 00e9 | fbff ffff 

  0x000002049625facc: ;   {static_stub}
  0x000002049625facc: 9048 bb00 | 0000 0000 

  0x000002049625fad4: ;   {runtime_call nmethod}
  0x000002049625fad4: 0000 00e9 | fbff ffff 

  0x000002049625fadc: ;   {static_stub}
  0x000002049625fadc: 9048 bb00 | 0000 0000 

  0x000002049625fae4: ;   {runtime_call nmethod}
  0x000002049625fae4: 0000 00e9 | fbff ffff 

  0x000002049625faec: ;   {static_stub}
  0x000002049625faec: 9048 bb00 | 0000 0000 

  0x000002049625faf4: ;   {runtime_call nmethod}
  0x000002049625faf4: 0000 00e9 | fbff ffff 

  0x000002049625fafc: ;   {static_stub}
  0x000002049625fafc: 9048 bb00 | 0000 0000 

  0x000002049625fb04: ;   {runtime_call nmethod}
  0x000002049625fb04: 0000 00e9 | fbff ffff 

  0x000002049625fb0c: ;   {static_stub}
  0x000002049625fb0c: 9048 bb00 | 0000 0000 

  0x000002049625fb14: ;   {runtime_call nmethod}
  0x000002049625fb14: 0000 00e9 | fbff ffff 

  0x000002049625fb1c: ;   {static_stub}
  0x000002049625fb1c: 9048 bb00 | 0000 0000 

  0x000002049625fb24: ;   {runtime_call nmethod}
  0x000002049625fb24: 0000 00e9 | fbff ffff 

  0x000002049625fb2c: ;   {static_stub}
  0x000002049625fb2c: 9048 bb00 | 0000 0000 

  0x000002049625fb34: ;   {runtime_call nmethod}
  0x000002049625fb34: 0000 00e9 | fbff ffff 

  0x000002049625fb3c: ;   {static_stub}
  0x000002049625fb3c: 9048 bb00 | 0000 0000 

  0x000002049625fb44: ;   {runtime_call nmethod}
  0x000002049625fb44: 0000 00e9 | fbff ffff 

  0x000002049625fb4c: ;   {static_stub}
  0x000002049625fb4c: 9048 bb00 | 0000 0000 

  0x000002049625fb54: ;   {runtime_call nmethod}
  0x000002049625fb54: 0000 00e9 | fbff ffff 

  0x000002049625fb5c: ;   {static_stub}
  0x000002049625fb5c: 9048 bb00 | 0000 0000 

  0x000002049625fb64: ;   {runtime_call nmethod}
  0x000002049625fb64: 0000 00e9 | fbff ffff 

  0x000002049625fb6c: ;   {static_stub}
  0x000002049625fb6c: 9048 bb00 | 0000 0000 

  0x000002049625fb74: ;   {runtime_call nmethod}
  0x000002049625fb74: 0000 00e9 | fbff ffff 

  0x000002049625fb7c: ;   {static_stub}
  0x000002049625fb7c: 9048 bb00 | 0000 0000 

  0x000002049625fb84: ;   {runtime_call nmethod}
  0x000002049625fb84: 0000 00e9 | fbff ffff 

  0x000002049625fb8c: ;   {static_stub}
  0x000002049625fb8c: 9048 bb00 | 0000 0000 

  0x000002049625fb94: ;   {runtime_call nmethod}
  0x000002049625fb94: 0000 00e9 | fbff ffff 

  0x000002049625fb9c: ;   {static_stub}
  0x000002049625fb9c: 9048 bb00 | 0000 0000 

  0x000002049625fba4: ;   {runtime_call nmethod}
  0x000002049625fba4: 0000 00e9 | fbff ffff 

  0x000002049625fbac: ;   {static_stub}
  0x000002049625fbac: 9048 bb00 | 0000 0000 

  0x000002049625fbb4: ;   {runtime_call nmethod}
  0x000002049625fbb4: 0000 00e9 | fbff ffff 

  0x000002049625fbbc: ;   {static_stub}
  0x000002049625fbbc: 9048 bb00 | 0000 0000 

  0x000002049625fbc4: ;   {runtime_call nmethod}
  0x000002049625fbc4: 0000 00e9 | fbff ffff 

  0x000002049625fbcc: ;   {static_stub}
  0x000002049625fbcc: 9048 bb00 | 0000 0000 

  0x000002049625fbd4: ;   {runtime_call nmethod}
  0x000002049625fbd4: 0000 00e9 | fbff ffff 

  0x000002049625fbdc: ;   {static_stub}
  0x000002049625fbdc: 9048 bb00 | 0000 0000 

  0x000002049625fbe4: ;   {runtime_call nmethod}
  0x000002049625fbe4: 0000 00e9 | fbff ffff 

  0x000002049625fbec: ;   {static_stub}
  0x000002049625fbec: 9048 bb00 | 0000 0000 

  0x000002049625fbf4: ;   {runtime_call nmethod}
  0x000002049625fbf4: 0000 00e9 | fbff ffff 

  0x000002049625fbfc: ;   {static_stub}
  0x000002049625fbfc: 9048 bb00 | 0000 0000 

  0x000002049625fc04: ;   {runtime_call nmethod}
  0x000002049625fc04: 0000 00e9 | fbff ffff 

  0x000002049625fc0c: ;   {static_stub}
  0x000002049625fc0c: 9048 bb00 | 0000 0000 

  0x000002049625fc14: ;   {runtime_call nmethod}
  0x000002049625fc14: 0000 00e9 | fbff ffff 

  0x000002049625fc1c: ;   {static_stub}
  0x000002049625fc1c: 9048 bb00 | 0000 0000 

  0x000002049625fc24: ;   {runtime_call nmethod}
  0x000002049625fc24: 0000 00e9 | fbff ffff 

  0x000002049625fc2c: ;   {static_stub}
  0x000002049625fc2c: 9048 bb00 | 0000 0000 

  0x000002049625fc34: ;   {runtime_call nmethod}
  0x000002049625fc34: 0000 00e9 | fbff ffff 

  0x000002049625fc3c: ;   {static_stub}
  0x000002049625fc3c: 9048 bb00 | 0000 0000 

  0x000002049625fc44: ;   {runtime_call nmethod}
  0x000002049625fc44: 0000 00e9 | fbff ffff 

  0x000002049625fc4c: ;   {static_stub}
  0x000002049625fc4c: 9048 bb00 | 0000 0000 

  0x000002049625fc54: ;   {runtime_call nmethod}
  0x000002049625fc54: 0000 00e9 | fbff ffff 

  0x000002049625fc5c: ;   {static_stub}
  0x000002049625fc5c: 9048 bb00 | 0000 0000 

  0x000002049625fc64: ;   {runtime_call nmethod}
  0x000002049625fc64: 0000 00e9 | fbff ffff 

  0x000002049625fc6c: ;   {static_stub}
  0x000002049625fc6c: 9048 bb00 | 0000 0000 

  0x000002049625fc74: ;   {runtime_call nmethod}
  0x000002049625fc74: 0000 00e9 | fbff ffff 

  0x000002049625fc7c: ;   {static_stub}
  0x000002049625fc7c: 9048 bb00 | 0000 0000 

  0x000002049625fc84: ;   {runtime_call nmethod}
  0x000002049625fc84: 0000 00e9 | fbff ffff 

  0x000002049625fc8c: ;   {static_stub}
  0x000002049625fc8c: 9048 bb00 | 0000 0000 

  0x000002049625fc94: ;   {runtime_call nmethod}
  0x000002049625fc94: 0000 00e9 | fbff ffff 

  0x000002049625fc9c: ;   {static_stub}
  0x000002049625fc9c: 9048 bb00 | 0000 0000 

  0x000002049625fca4: ;   {runtime_call nmethod}
  0x000002049625fca4: 0000 00e9 | fbff ffff 

  0x000002049625fcac: ;   {static_stub}
  0x000002049625fcac: 9048 bb00 | 0000 0000 

  0x000002049625fcb4: ;   {runtime_call nmethod}
  0x000002049625fcb4: 0000 00e9 | fbff ffff 

  0x000002049625fcbc: ;   {static_stub}
  0x000002049625fcbc: 9048 bb00 | 0000 0000 

  0x000002049625fcc4: ;   {runtime_call nmethod}
  0x000002049625fcc4: 0000 00e9 | fbff ffff 

  0x000002049625fccc: ;   {static_stub}
  0x000002049625fccc: 9048 bb00 | 0000 0000 

  0x000002049625fcd4: ;   {runtime_call nmethod}
  0x000002049625fcd4: 0000 00e9 | fbff ffff 

  0x000002049625fcdc: ;   {static_stub}
  0x000002049625fcdc: 48bb 0000 | 0000 0000 

  0x000002049625fce4: ;   {runtime_call nmethod}
  0x000002049625fce4: 0000 e9fb 

  0x000002049625fce8: ;   {static_stub}
  0x000002049625fce8: ffff ff48 | bb00 0000 | 0000 0000 

  0x000002049625fcf4: ;   {runtime_call nmethod}
  0x000002049625fcf4: 00e9 fbff 

  0x000002049625fcf8: ;   {static_stub}
  0x000002049625fcf8: ffff 48bb | 0000 0000 | 0000 0000 

  0x000002049625fd04: ;   {runtime_call nmethod}
  0x000002049625fd04: e9fb ffff 

  0x000002049625fd08: ;   {static_stub}
  0x000002049625fd08: ff48 bb00 | 0000 0000 

  0x000002049625fd10: ;   {runtime_call nmethod}
  0x000002049625fd10: 0000 00e9 | fbff ffff 

  0x000002049625fd18: ;   {static_stub}
  0x000002049625fd18: 48bb 0000 | 0000 0000 

  0x000002049625fd20: ;   {runtime_call nmethod}
  0x000002049625fd20: 0000 e9fb 

  0x000002049625fd24: ;   {static_stub}
  0x000002049625fd24: ffff ff48 | bb00 0000 | 0000 0000 

  0x000002049625fd30: ;   {runtime_call nmethod}
  0x000002049625fd30: 00e9 fbff 

  0x000002049625fd34: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x000002049625fd34: ffff e8c5 

  0x000002049625fd38: ;   {external_word}
  0x000002049625fd38: 0f30 0748 | b990 7f0f | d5fd 7f00 | 0048 83e4 

  0x000002049625fd48: ;   {runtime_call}
  0x000002049625fd48: f048 b8e0 | 44d2 d4fd | 7f00 00ff 

  0x000002049625fd54: ;   {section_word}
  0x000002049625fd54: d0f4 49ba | 56fd 2596 | 0402 0000 

  0x000002049625fd60: ;   {runtime_call DeoptimizationBlob}
  0x000002049625fd60: 4152 e9b9 | 7225 07f4 
[/MachCode]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000204e9e92630, length=17, elements={
0x0000020492d351a0, 0x00000204e81b4400, 0x00000204e81b5d00, 0x00000204e81b8890,
0x00000204e81b97a0, 0x00000204e81bd370, 0x00000204e81bddc0, 0x00000204e81c1630,
0x00000204e81d4c40, 0x00000204e9c41110, 0x00000204e9e9e110, 0x00000204ee5ef7a0,
0x00000204ee773280, 0x00000204eea895e0, 0x00000204ee661800, 0x00000204ee682f80,
0x00000204ee924820
}

Java Threads: ( => current thread )
  0x0000020492d351a0 JavaThread "main"                              [_thread_blocked, id=7388, stack(0x00000098da900000,0x00000098daa00000) (1024K)]
  0x00000204e81b4400 JavaThread "Reference Handler"          daemon [_thread_blocked, id=15576, stack(0x00000098dae00000,0x00000098daf00000) (1024K)]
  0x00000204e81b5d00 JavaThread "Finalizer"                  daemon [_thread_blocked, id=2708, stack(0x00000098daf00000,0x00000098db000000) (1024K)]
  0x00000204e81b8890 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=876, stack(0x00000098db000000,0x00000098db100000) (1024K)]
  0x00000204e81b97a0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=7564, stack(0x00000098db100000,0x00000098db200000) (1024K)]
  0x00000204e81bd370 JavaThread "Service Thread"             daemon [_thread_blocked, id=10396, stack(0x00000098db200000,0x00000098db300000) (1024K)]
  0x00000204e81bddc0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=9196, stack(0x00000098db300000,0x00000098db400000) (1024K)]
  0x00000204e81c1630 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=3304, stack(0x00000098db400000,0x00000098db500000) (1024K)]
  0x00000204e81d4c40 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=6572, stack(0x00000098db500000,0x00000098db600000) (1024K)]
  0x00000204e9c41110 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=13820, stack(0x00000098db600000,0x00000098db700000) (1024K)]
  0x00000204e9e9e110 JavaThread "Notification Thread"        daemon [_thread_blocked, id=460, stack(0x00000098db800000,0x00000098db900000) (1024K)]
  0x00000204ee5ef7a0 JavaThread "Active Thread: Equinox Container: 0dcfd6cf-2c61-4e48-a984-4a53b6d5beec"        [_thread_blocked, id=12648, stack(0x00000098dc000000,0x00000098dc100000) (1024K)]
  0x00000204ee773280 JavaThread "Framework Event Dispatcher: Equinox Container: 0dcfd6cf-2c61-4e48-a984-4a53b6d5beec" daemon [_thread_blocked, id=5708, stack(0x00000098db900000,0x00000098dba00000) (1024K)]
=>0x00000204eea895e0 JavaThread "Start Level: Equinox Container: 0dcfd6cf-2c61-4e48-a984-4a53b6d5beec" daemon [_thread_in_vm, id=580, stack(0x00000098dc100000,0x00000098dc200000) (1024K)]
  0x00000204ee661800 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=2160, stack(0x00000098dc200000,0x00000098dc300000) (1024K)]
  0x00000204ee682f80 JavaThread "SCR Component Registry"     daemon [_thread_blocked, id=5700, stack(0x00000098dc400000,0x00000098dc500000) (1024K)]
  0x00000204ee924820 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=16376, stack(0x00000098db700000,0x00000098db800000) (1024K)]
Total: 17

Other Threads:
  0x00000204e81a1480 VMThread "VM Thread"                           [id=13288, stack(0x00000098dac00000,0x00000098dad00000) (1024K)]
  0x00000204e80dc950 WatcherThread "VM Periodic Task Thread"        [id=12756, stack(0x00000098dab00000,0x00000098dac00000) (1024K)]
  0x0000020492d534a0 WorkerThread "GC Thread#0"                     [id=1156, stack(0x00000098daa00000,0x00000098dab00000) (1024K)]
  0x00000204ee131e20 WorkerThread "GC Thread#1"                     [id=2308, stack(0x00000098dba00000,0x00000098dbb00000) (1024K)]
  0x00000204ee1321c0 WorkerThread "GC Thread#2"                     [id=13384, stack(0x00000098dbb00000,0x00000098dbc00000) (1024K)]
  0x00000204ee132560 WorkerThread "GC Thread#3"                     [id=2928, stack(0x00000098dbc00000,0x00000098dbd00000) (1024K)]
  0x00000204ee3514e0 WorkerThread "GC Thread#4"                     [id=14140, stack(0x00000098dbd00000,0x00000098dbe00000) (1024K)]
  0x00000204ee351880 WorkerThread "GC Thread#5"                     [id=3192, stack(0x00000098dbe00000,0x00000098dbf00000) (1024K)]
  0x00000204ee351c20 WorkerThread "GC Thread#6"                     [id=11112, stack(0x00000098dbf00000,0x00000098dc000000) (1024K)]
  0x00000204ee657200 WorkerThread "GC Thread#7"                     [id=13376, stack(0x00000098dc300000,0x00000098dc400000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread1  2218 1942   !   4       java.io.BufferedInputStream::read (69 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffdd53ac308] Metaspace_lock - owner thread: 0x00000204eea895e0

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x00000204a7000000-0x00000204a7ba0000-0x00000204a7ba0000), size 12189696, SharedBaseAddress: 0x00000204a7000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x00000204a8000000-0x00000204e8000000, reserved size: 1073741824
Narrow klass base: 0x00000204a7000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 7975M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 29696K, used 15707K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 45% used [0x00000000eab00000,0x00000000eb659df8,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfd1e8,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 911K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 1% used [0x00000000c0000000,0x00000000c00e3ea8,0x00000000c4300000)
 Metaspace       used 10651K, committed 11008K, reserved 1114112K
  class space    used 1109K, committed 1280K, reserved 1048576K

Card table byte_map: [0x00000204926e0000,0x00000204928f0000] _byte_map_base: 0x00000204920e0000

Marking Bits: (ParMarkBitMap*) 0x00007ffdd53b31f0
 Begin Bits: [0x00000204a4f30000, 0x00000204a5f30000)
 End Bits:   [0x00000204a5f30000, 0x00000204a6f30000)

Polling page: 0x0000020490bb0000

Metaspace:

Usage:
  Non-class:      9.32 MB used.
      Class:      1.08 MB used.
       Both:     10.40 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       9.50 MB ( 15%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.25 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      10.75 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  6.11 MB
       Class:  14.70 MB
        Both:  20.81 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 276.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 172.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 554.
num_chunk_merges: 0.
num_chunk_splits: 377.
num_chunks_enlarged: 254.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=857Kb max_used=857Kb free=119142Kb
 bounds [0x000002049da00000, 0x000002049dc70000, 0x00000204a4f30000]
CodeHeap 'profiled nmethods': size=120000Kb used=3522Kb max_used=3522Kb free=116477Kb
 bounds [0x0000020495f30000, 0x00000204962b0000, 0x000002049d460000]
CodeHeap 'non-nmethods': size=5760Kb used=1234Kb max_used=1266Kb free=4526Kb
 bounds [0x000002049d460000, 0x000002049d6d0000, 0x000002049da00000]
 total_blobs=2441 nmethods=1948 adapters=400
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 1.953 Thread 0x00000204e81d4c40 nmethod 1933 0x0000020496299690 code [0x00000204962998a0, 0x0000020496299eb0]
Event: 1.953 Thread 0x00000204e81d4c40 1938       3       jdk.internal.misc.Unsafe::getLongUnaligned (12 bytes)
Event: 1.953 Thread 0x00000204e81d4c40 nmethod 1938 0x000002049629a190 code [0x000002049629a340, 0x000002049629a520]
Event: 1.953 Thread 0x00000204e81d4c40 1934       3       jdk.internal.util.ByteArray::getLong (9 bytes)
Event: 1.953 Thread 0x00000204e81d4c40 nmethod 1934 0x000002049629a610 code [0x000002049629a800, 0x000002049629ad30]
Event: 1.953 Thread 0x00000204e81d4c40 1935       3       java.lang.invoke.VarHandleGuards::guard_LI_J (78 bytes)
Event: 1.955 Thread 0x00000204e81d4c40 nmethod 1935 0x000002049629af90 code [0x000002049629b2c0, 0x000002049629ca90]
Event: 1.955 Thread 0x00000204e81d4c40 1936       3       java.lang.invoke.VarHandleByteArrayAsLongs$ArrayHandle::get (36 bytes)
Event: 1.955 Thread 0x00000204e81d4c40 nmethod 1936 0x000002049629d190 code [0x000002049629d360, 0x000002049629d738]
Event: 1.955 Thread 0x00000204e81d4c40 1937       3       java.lang.invoke.VarHandleByteArrayAsLongs$ArrayHandle::index (13 bytes)
Event: 1.955 Thread 0x00000204e81d4c40 nmethod 1937 0x000002049629d910 code [0x000002049629dac0, 0x000002049629dc08]
Event: 1.955 Thread 0x00000204e81c1630 1940       4       jdk.internal.misc.InternalLock::unlock (8 bytes)
Event: 1.956 Thread 0x00000204e81d4c40 1946   !   3       java.io.BufferedInputStream::read (54 bytes)
Event: 1.956 Thread 0x00000204e81d4c40 nmethod 1946 0x000002049629dd10 code [0x000002049629dfa0, 0x000002049629eba8]
Event: 1.957 Thread 0x00000204e81c1630 nmethod 1940 0x000002049dad0c90 code [0x000002049dad0e40, 0x000002049dad0fd0]
Event: 1.957 Thread 0x00000204e81c1630 1943       4       java.io.DataInputStream::readFully (59 bytes)
Event: 1.957 Thread 0x00000204ee924820 1944 %     4       java.io.DataInputStream::readUTF @ 92 (501 bytes)
Event: 1.959 Thread 0x00000204e81d4c40 1947       1       org.eclipse.osgi.storage.BundleInfo::getBundleId (5 bytes)
Event: 1.960 Thread 0x00000204e81d4c40 nmethod 1947 0x000002049dad1190 code [0x000002049dad1320, 0x000002049dad13e8]
Event: 1.961 Thread 0x00000204e81d4c40 1948       3       org.eclipse.osgi.internal.framework.EquinoxBundle::getState (101 bytes)

GC Heap History (4 events):
Event: 0.838 GC heap before
{Heap before GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 25600K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 0K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0000000,0x00000000c4300000)
 Metaspace       used 4353K, committed 4544K, reserved 1114112K
  class space    used 460K, committed 576K, reserved 1048576K
}
Event: 0.846 GC heap after
{Heap after GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 3270K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 79% used [0x00000000ec400000,0x00000000ec731870,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 16K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0004000,0x00000000c4300000)
 Metaspace       used 4353K, committed 4544K, reserved 1114112K
  class space    used 460K, committed 576K, reserved 1048576K
}
Event: 1.623 GC heap before
{Heap before GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 28870K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000eab00000,0x00000000ec400000,0x00000000ec400000)
  from space 4096K, 79% used [0x00000000ec400000,0x00000000ec731870,0x00000000ec800000)
  to   space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
 ParOldGen       total 68608K, used 16K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0004000,0x00000000c4300000)
 Metaspace       used 8285K, committed 8576K, reserved 1114112K
  class space    used 850K, committed 1024K, reserved 1048576K
}
Event: 1.628 GC heap after
{Heap after GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 4084K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000eab00000,0x00000000eab00000,0x00000000ec400000)
  from space 4096K, 99% used [0x00000000ec800000,0x00000000ecbfd1e8,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 911K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 1% used [0x00000000c0000000,0x00000000c00e3ea8,0x00000000c4300000)
 Metaspace       used 8285K, committed 8576K, reserved 1114112K
  class space    used 850K, committed 1024K, reserved 1048576K
}

Dll operation events (9 events):
Event: 0.008 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.105 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.126 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.137 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.139 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.143 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.169 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.254 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 1.583 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll

Deoptimization events (20 events):
Event: 1.489 Thread 0x0000020492d351a0 DEOPT PACKING pc=0x00000204960b1688 sp=0x00000098da9fc5f0
Event: 1.489 Thread 0x0000020492d351a0 DEOPT UNPACKING pc=0x000002049d4b78e2 sp=0x00000098da9fba70 mode 0
Event: 1.491 Thread 0x0000020492d351a0 DEOPT PACKING pc=0x00000204960b1688 sp=0x00000098da9fc900
Event: 1.491 Thread 0x0000020492d351a0 DEOPT UNPACKING pc=0x000002049d4b78e2 sp=0x00000098da9fbd80 mode 0
Event: 1.492 Thread 0x0000020492d351a0 DEOPT PACKING pc=0x00000204960b1688 sp=0x00000098da9fc850
Event: 1.492 Thread 0x0000020492d351a0 DEOPT UNPACKING pc=0x000002049d4b78e2 sp=0x00000098da9fbcd0 mode 0
Event: 1.492 Thread 0x0000020492d351a0 DEOPT PACKING pc=0x00000204960b1688 sp=0x00000098da9fcb50
Event: 1.492 Thread 0x0000020492d351a0 DEOPT UNPACKING pc=0x000002049d4b78e2 sp=0x00000098da9fbfd0 mode 0
Event: 1.601 Thread 0x00000204eea895e0 Uncommon trap: trap_request=0xffffff6e fr.pc=0x000002049da77a28 relative=0x0000000000000368
Event: 1.601 Thread 0x00000204eea895e0 Uncommon trap: reason=loop_limit_check action=maybe_recompile pc=0x000002049da77a28 method=java.lang.StringLatin1.indexOf([BI[BII)I @ 37 c2
Event: 1.601 Thread 0x00000204eea895e0 DEOPT PACKING pc=0x000002049da77a28 sp=0x00000098dc1f9020
Event: 1.601 Thread 0x00000204eea895e0 DEOPT UNPACKING pc=0x000002049d4b6da2 sp=0x00000098dc1f8f80 mode 2
Event: 1.832 Thread 0x00000204eea895e0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002049dac4c2c relative=0x000000000000152c
Event: 1.832 Thread 0x00000204eea895e0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002049dac4c2c method=java.io.WinNTFileSystem.normalize(Ljava/lang/String;II)Ljava/lang/String; @ 100 c2
Event: 1.832 Thread 0x00000204eea895e0 DEOPT PACKING pc=0x000002049dac4c2c sp=0x00000098dc1f92d0
Event: 1.832 Thread 0x00000204eea895e0 DEOPT UNPACKING pc=0x000002049d4b6da2 sp=0x00000098dc1f9210 mode 2
Event: 1.871 Thread 0x00000204eea895e0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002049da83eb4 relative=0x0000000000000094
Event: 1.871 Thread 0x00000204eea895e0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002049da83eb4 method=java.io.BufferedInputStream.getBufIfOpen(Z)[B @ 13 c2
Event: 1.871 Thread 0x00000204eea895e0 DEOPT PACKING pc=0x000002049da83eb4 sp=0x00000098dc1f8bb0
Event: 1.871 Thread 0x00000204eea895e0 DEOPT UNPACKING pc=0x000002049d4b6da2 sp=0x00000098dc1f8b30 mode 2

Classes loaded (20 events):
Event: 1.760 Loading class java/io/CharConversionException done
Event: 1.760 Loading class com/sun/org/apache/xerces/internal/impl/io/MalformedByteSequenceException done
Event: 1.774 Loading class java/util/concurrent/RejectedExecutionException
Event: 1.774 Loading class java/util/concurrent/RejectedExecutionException done
Event: 1.774 Loading class java/util/concurrent/CompletionStage
Event: 1.774 Loading class java/util/concurrent/CompletionStage done
Event: 1.776 Loading class java/util/Timer
Event: 1.776 Loading class java/util/Timer done
Event: 1.776 Loading class java/util/TaskQueue
Event: 1.776 Loading class java/util/TaskQueue done
Event: 1.776 Loading class java/util/TimerThread
Event: 1.776 Loading class java/util/TimerThread done
Event: 1.776 Loading class java/util/Timer$ThreadReaper
Event: 1.777 Loading class java/util/Timer$ThreadReaper done
Event: 1.794 Loading class java/util/concurrent/locks/ReentrantLock$FairSync
Event: 1.794 Loading class java/util/concurrent/locks/ReentrantLock$FairSync done
Event: 1.797 Loading class java/util/Collections$ReverseComparator
Event: 1.797 Loading class java/util/Collections$ReverseComparator done
Event: 1.854 Loading class java/lang/CloneNotSupportedException
Event: 1.854 Loading class java/lang/CloneNotSupportedException done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.835 Thread 0x0000020492d351a0 Exception <a 'java/io/FileNotFoundException'{0x00000000ec3e7aa0}> (0x00000000ec3e7aa0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 0.879 Thread 0x0000020492d351a0 Implicit null exception at 0x000002049da618c9 to 0x000002049da6224c
Event: 0.879 Thread 0x0000020492d351a0 Implicit null exception at 0x000002049da6649a to 0x000002049da666a4
Event: 0.880 Thread 0x0000020492d351a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eacd40d0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eacd40d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.881 Thread 0x0000020492d351a0 Implicit null exception at 0x000002049da66b9a to 0x000002049da66da4
Event: 0.881 Thread 0x0000020492d351a0 Implicit null exception at 0x000002049da63b3d to 0x000002049da63bc1
Event: 0.930 Thread 0x0000020492d351a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eae91470}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x00000000eae91470) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.931 Thread 0x0000020492d351a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eae98e60}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x00000000eae98e60) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.933 Thread 0x0000020492d351a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaea6858}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000eaea6858) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.938 Thread 0x0000020492d351a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaed00e0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eaed00e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.939 Thread 0x0000020492d351a0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000eaed85e0}: Found class java.lang.Object, but interface was expected> (0x00000000eaed85e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 0.939 Thread 0x0000020492d351a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaedc780}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000eaedc780) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.941 Thread 0x0000020492d351a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eaee3658}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000eaee3658) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.043 Thread 0x0000020492d351a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb0207c8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000eb0207c8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.313 Thread 0x0000020492d351a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000eb8c2120}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000000eb8c2120) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.405 Thread 0x0000020492d351a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ebc2b288}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int, java.lang.Object)'> (0x00000000ebc2b288) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.441 Thread 0x0000020492d351a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ebdac5f0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000ebdac5f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.580 Thread 0x0000020492d351a0 Exception <a 'java/lang/UnsatisfiedLinkError'{0x00000000ec1f8398}: 'void org.eclipse.equinox.launcher.JNIBridge._update_splash()'> (0x00000000ec1f8398) 
thrown [s\src\hotspot\share\prims\nativeLookup.cpp, line 415]
Event: 1.674 Thread 0x00000204eea895e0 Exception <a 'java/lang/NullPointerException'{0x00000000ead36968}> (0x00000000ead36968) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1372]
Event: 1.674 Thread 0x00000204eea895e0 Exception <a 'java/lang/NullPointerException'{0x00000000ead36c48}> (0x00000000ead36c48) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1372]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 0.549 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.550 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.578 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.578 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.799 Executing VM operation: ICBufferFull
Event: 0.799 Executing VM operation: ICBufferFull done
Event: 0.838 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 0.846 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 1.384 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.384 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.393 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.393 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.438 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.438 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.623 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 1.628 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 1.660 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.660 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.795 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.795 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.053 Thread 0x0000020492d351a0 Thread added: 0x00000204e81b4400
Event: 0.053 Thread 0x0000020492d351a0 Thread added: 0x00000204e81b5d00
Event: 0.053 Thread 0x0000020492d351a0 Thread added: 0x00000204e81b8890
Event: 0.054 Thread 0x0000020492d351a0 Thread added: 0x00000204e81b97a0
Event: 0.054 Thread 0x0000020492d351a0 Thread added: 0x00000204e81bd370
Event: 0.054 Thread 0x0000020492d351a0 Thread added: 0x00000204e81bddc0
Event: 0.054 Thread 0x0000020492d351a0 Thread added: 0x00000204e81c1630
Event: 0.054 Thread 0x0000020492d351a0 Thread added: 0x00000204e81d4c40
Event: 0.087 Thread 0x0000020492d351a0 Thread added: 0x00000204e9c41110
Event: 0.246 Thread 0x00000204e81c1630 Thread added: 0x00000204e9e73360
Event: 0.356 Thread 0x0000020492d351a0 Thread added: 0x00000204e9e9e110
Event: 0.561 Thread 0x00000204e81d4c40 Thread added: 0x00000204ee064500
Event: 1.366 Thread 0x0000020492d351a0 Thread added: 0x00000204ee5ef7a0
Event: 1.423 Thread 0x00000204ee064500 Thread exited: 0x00000204ee064500
Event: 1.574 Thread 0x0000020492d351a0 Thread added: 0x00000204ee773280
Event: 1.579 Thread 0x0000020492d351a0 Thread added: 0x00000204eea895e0
Event: 1.618 Thread 0x00000204eea895e0 Thread added: 0x00000204ee661800
Event: 1.777 Thread 0x00000204eea895e0 Thread added: 0x00000204ee682f80
Event: 1.952 Thread 0x00000204e9e73360 Thread exited: 0x00000204e9e73360
Event: 1.957 Thread 0x00000204e81c1630 Thread added: 0x00000204ee924820


Dynamic libraries:
0x00007ff6d47b0000 - 0x00007ff6d47be000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ffe80050000 - 0x00007ffe80248000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffe7fb30000 - 0x00007ffe7fbf2000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffe7d7c0000 - 0x00007ffe7dab6000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffe7dce0000 - 0x00007ffe7dde0000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffe5e7d0000 - 0x00007ffe5e7e8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ffe7e080000 - 0x00007ffe7e21d000 	C:\WINDOWS\System32\USER32.dll
0x00007ffe7dde0000 - 0x00007ffe7de02000 	C:\WINDOWS\System32\win32u.dll
0x00007ffe7f810000 - 0x00007ffe7f83b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffe7db70000 - 0x00007ffe7dc8a000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffe7d720000 - 0x00007ffe7d7bd000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe5cbf0000 - 0x00007ffe5cc0e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffe65d80000 - 0x00007ffe6601a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5794_none_60bcd33171f2783c\COMCTL32.dll
0x00007ffe7f170000 - 0x00007ffe7f20e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffe7fe10000 - 0x00007ffe7fe3f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffe777e0000 - 0x00007ffe777ec000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffe4f5f0000 - 0x00007ffe4f67d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007ffdd4700000 - 0x00007ffdd5490000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ffe7f760000 - 0x00007ffe7f80f000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffe7f0d0000 - 0x00007ffe7f16f000 	C:\WINDOWS\System32\sechost.dll
0x00007ffe7f8a0000 - 0x00007ffe7f9c3000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffe7d6f0000 - 0x00007ffe7d717000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffe7f2c0000 - 0x00007ffe7f32b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffe7cc80000 - 0x00007ffe7cccb000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffe6d7c0000 - 0x00007ffe6d7e7000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffe74cf0000 - 0x00007ffe74cfa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffe7caf0000 - 0x00007ffe7cb02000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffe7b4f0000 - 0x00007ffe7b502000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffe6d7a0000 - 0x00007ffe6d7aa000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ffe74aa0000 - 0x00007ffe74ca1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffe56040000 - 0x00007ffe56074000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffe7dff0000 - 0x00007ffe7e072000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffe5cab0000 - 0x00007ffe5cabf000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ffe5cbd0000 - 0x00007ffe5cbef000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ffe7e350000 - 0x00007ffe7eabe000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffe7b710000 - 0x00007ffe7beb3000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffe7f330000 - 0x00007ffe7f683000 	C:\WINDOWS\System32\combase.dll
0x00007ffe7d120000 - 0x00007ffe7d14b000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ffe7f690000 - 0x00007ffe7f75d000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffe7f210000 - 0x00007ffe7f2bd000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffe7fac0000 - 0x00007ffe7fb15000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffe7d620000 - 0x00007ffe7d645000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffe5ca90000 - 0x00007ffe5caa8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ffe5bbc0000 - 0x00007ffe5bbd0000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ffe76610000 - 0x00007ffe7671a000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffe7ce80000 - 0x00007ffe7ceec000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffe5bb10000 - 0x00007ffe5bb26000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ffe50100000 - 0x00007ffe50110000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ffe43780000 - 0x00007ffe437c5000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ffe7e220000 - 0x00007ffe7e34b000 	C:\WINDOWS\System32\ole32.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5794_none_60bcd33171f2783c;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\1eb1b3ffae57f2c6129ade188dcbd886\redhat.java\ss_ws --pipe=\\.\pipe\lsp-6965f6f9e3a5178cadbf3b90b826bd06-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-11
PATH=C:\app\tshep\product\21c\dbhomeXE\bin;C:\app\tshep\product\18.0.0\dbhomeXE\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files (x86)\dotnet\;C:\Program Files\Java\jdk-11\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\xampp\php;C:\Program Files\Java\glassfish4\bin;C:\Users\<USER>\Downloads\apache-maven-3.9.9\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.12;C:\Program Files (x86)\cloudflared\;C:\Program Files\Redis\;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\anaconda3;C:\Users\<USER>\anaconda3\Library\mingw-w64\bin;C:\Users\<USER>\anaconda3\Library\usr\bin;C:\Users\<USER>\anaconda3\Library\bin;C:\Users\<USER>\anaconda3\Scripts;C:\Users\<USER>\anaconda3\bin;C:\Users\<USER>\anaconda3\condabin;C:\app\tshep\product\21c\dbhomeXE\bin;C:\app\tshep\product\18.0.0\dbhomeXE\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\dotnet;C:\Program Files (x86)\dotnet;C:\Program Files\Java\jdk-11\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs;C:\ProgramData\chocolatey\bin;C:\xampp\php;C:\Program Files\Java\glassfish4\bin;C:\Users\<USER>\Downloads\apache-maven-3.9.9\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.12;C:\Program Files (x86)\cloudflared;C:\Program Files\Redis;C:\Users\<USER>\scoop\shims;C:\app\tshep\product\21c\dbhomeXE\bin;C:\app\tshep\product\18.0.0\dbhomeXE\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\dotnet;C:\Program Files (x86)\dotnet;C:\Program Files\Java\jdk-11\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs;C:\ProgramData\chocolatey\bin;C:\xampp\php;C:\Program Files\Java\glassfish4\bin;C:\Users\<USER>\Downloads\apache-maven-3.9.9\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.12;C:\Program Files (x86)\cloudflared;C:\Program Files\Redis;C:\Users\<USER>\Downloads\ffmpeg-2025-03-24-git-cbbc927a67-full_build\ffmpeg-2025-03-24-git-cbbc927a67-full_build\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\Downloads\ffmpeg-2025-03-24-git-cbbc927a67-full_build\ffmpeg-2025-03-24-git-cbbc927a67-full_build\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools
USERNAME=tshep
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 140 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5794)
OS uptime: 0 days 0:39 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 140 stepping 1 microcode 0x9a, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ibt, cet_ss, avx512_ifma
Processor Information for processor 0
  Max Mhz: 2419, Current Mhz: 907, Mhz Limit: 2419
Processor Information for processor 1
  Max Mhz: 2419, Current Mhz: 2419, Mhz Limit: 2419
Processor Information for processor 2
  Max Mhz: 2419, Current Mhz: 2419, Mhz Limit: 2419
Processor Information for processor 3
  Max Mhz: 2419, Current Mhz: 2419, Mhz Limit: 2419
Processor Information for processor 4
  Max Mhz: 2419, Current Mhz: 907, Mhz Limit: 2419
Processor Information for processor 5
  Max Mhz: 2419, Current Mhz: 2419, Mhz Limit: 2419
Processor Information for processor 6
  Max Mhz: 2419, Current Mhz: 2419, Mhz Limit: 2419
Processor Information for processor 7
  Max Mhz: 2419, Current Mhz: 907, Mhz Limit: 2419

Memory: 4k page, system-wide physical 7975M (1502M free)
TotalPageFile size 11429M (AvailPageFile size 256M)
current process WorkingSet (physical memory assigned to process): 99M, peak: 99M
current process commit charge ("private bytes"): 213M, peak: 213M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
