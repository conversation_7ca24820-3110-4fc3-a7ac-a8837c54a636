#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1048576 bytes. Error detail: AllocateHeap
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:44), pid=11932, tid=10092
#
# JRE version:  (21.0.7+6) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.7+6-LTS, mixed mode, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\1eb1b3ffae57f2c6129ade188dcbd886\redhat.java\ss_ws --pipe=\\.\pipe\lsp-e6a02c6c6037bbb2e7f06e599f91b30b-sock

Host: 11th Gen Intel(R) Core(TM) i5-1135G7 @ 2.40GHz, 8 cores, 7G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5794)
Time: Sun Jun  1 15:08:18 2025 South Africa Standard Time elapsed time: 0.165065 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000021610894bd0):  JavaThread "Unknown thread" [_thread_in_vm, id=10092, stack(0x0000009999500000,0x0000009999600000) (1024K)]

Stack: [0x0000009999500000,0x0000009999600000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xbfba7]
V  [jvm.dll+0x702092]
V  [jvm.dll+0x702dcc]
V  [jvm.dll+0x6dcc63]
V  [jvm.dll+0x871dbc]
V  [jvm.dll+0x3bc47c]
V  [jvm.dll+0x85a848]
V  [jvm.dll+0x45080e]
V  [jvm.dll+0x452451]
C  [jli.dll+0x5278]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000216108ff300, length=1, elements={
0x0000021610894bd0
}

Java Threads: ( => current thread )
=>0x0000021610894bd0 JavaThread "Unknown thread"             [_thread_in_vm, id=10092, stack(0x0000009999500000,0x0000009999600000) (1024K)]
Total: 1

Other Threads:
  0x0000021624b6c950 WatcherThread "VM Periodic Task Thread"        [id=13284, stack(0x0000009999700000,0x0000009999800000) (1024K)]
  0x00000216108b3c70 WorkerThread "GC Thread#0"                     [id=12764, stack(0x0000009999600000,0x0000009999700000) (1024K)]
Total: 2

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000c0000000, size: 1024 MB, Compressed Oops mode: 32-bit

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000100000000-0x0000000140000000, reserved size: 1073741824
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3, Narrow klass range: 0x140000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 7975M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 1G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 29696K, used 512K [0x00000000eab00000, 0x00000000ecc00000, 0x0000000100000000)
  eden space 25600K, 2% used [0x00000000eab00000,0x00000000eab80070,0x00000000ec400000)
  from space 4096K, 0% used [0x00000000ec800000,0x00000000ec800000,0x00000000ecc00000)
  to   space 4096K, 0% used [0x00000000ec400000,0x00000000ec400000,0x00000000ec800000)
 ParOldGen       total 68608K, used 0K [0x00000000c0000000, 0x00000000c4300000, 0x00000000eab00000)
  object space 68608K, 0% used [0x00000000c0000000,0x00000000c0000000,0x00000000c4300000)
 Metaspace       used 1392K, committed 1472K, reserved 1114112K
  class space    used 109K, committed 128K, reserved 1048576K

Card table byte_map: [0x0000021610250000,0x0000021610460000] _byte_map_base: 0x000002160fc50000

Marking Bits: (ParMarkBitMap*) 0x00007ffdc80831f0
 Begin Bits: [0x0000021622a90000, 0x0000021623a90000)
 End Bits:   [0x0000021623a90000, 0x0000021624a90000)

Polling page: 0x000002160e890000

Metaspace:

Usage:
  Non-class:      1.25 MB used.
      Class:    109.62 KB used.
       Both:      1.36 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       1.31 MB (  2%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     128.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       1.44 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  12.00 MB
       Class:  15.75 MB
        Both:  27.75 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 17179869184.00 GB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 2.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 23.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 3.
num_chunk_merges: 0.
num_chunk_splits: 2.
num_chunks_enlarged: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=0Kb max_used=0Kb free=120000Kb
 bounds [0x000002161b560000, 0x000002161b7d0000, 0x0000021622a90000]
CodeHeap 'profiled nmethods': size=120000Kb used=0Kb max_used=0Kb free=120000Kb
 bounds [0x0000021613a90000, 0x0000021613d00000, 0x000002161afc0000]
CodeHeap 'non-nmethods': size=5760Kb used=199Kb max_used=348Kb free=5560Kb
 bounds [0x000002161afc0000, 0x000002161b230000, 0x000002161b560000]
 total_blobs=70 nmethods=0 adapters=48
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (0 events):
No events

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.052 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.087 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll

Deoptimization events (0 events):
No events

Classes loaded (20 events):
Event: 0.156 Loading class java/util/Iterator
Event: 0.157 Loading class java/util/Iterator done
Event: 0.157 Loading class java/lang/reflect/RecordComponent
Event: 0.157 Loading class java/lang/reflect/RecordComponent done
Event: 0.157 Loading class jdk/internal/vm/vector/VectorSupport
Event: 0.158 Loading class jdk/internal/vm/vector/VectorSupport done
Event: 0.158 Loading class jdk/internal/vm/vector/VectorSupport$VectorPayload
Event: 0.158 Loading class jdk/internal/vm/vector/VectorSupport$VectorPayload done
Event: 0.158 Loading class jdk/internal/vm/vector/VectorSupport$Vector
Event: 0.158 Loading class jdk/internal/vm/vector/VectorSupport$Vector done
Event: 0.158 Loading class jdk/internal/vm/vector/VectorSupport$VectorMask
Event: 0.158 Loading class jdk/internal/vm/vector/VectorSupport$VectorMask done
Event: 0.158 Loading class jdk/internal/vm/vector/VectorSupport$VectorShuffle
Event: 0.158 Loading class jdk/internal/vm/vector/VectorSupport$VectorShuffle done
Event: 0.158 Loading class jdk/internal/vm/FillerObject
Event: 0.158 Loading class jdk/internal/vm/FillerObject done
Event: 0.162 Loading class java/lang/NullPointerException
Event: 0.162 Loading class java/lang/NullPointerException done
Event: 0.162 Loading class java/lang/ArithmeticException
Event: 0.162 Loading class java/lang/ArithmeticException done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (1 events):
Event: 0.072 Thread 0x0000021610894bd0 Thread added: 0x0000021610894bd0


Dynamic libraries:
0x00007ff6d47b0000 - 0x00007ff6d47be000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ffe80050000 - 0x00007ffe80248000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffe7fb30000 - 0x00007ffe7fbf2000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffe7d7c0000 - 0x00007ffe7dab6000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffe7dce0000 - 0x00007ffe7dde0000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffe49740000 - 0x00007ffe49758000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ffe78cd0000 - 0x00007ffe78cee000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ffe7e080000 - 0x00007ffe7e21d000 	C:\WINDOWS\System32\USER32.dll
0x00007ffe7dde0000 - 0x00007ffe7de02000 	C:\WINDOWS\System32\win32u.dll
0x00007ffe65d80000 - 0x00007ffe6601a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5794_none_60bcd33171f2783c\COMCTL32.dll
0x00007ffe7f810000 - 0x00007ffe7f83b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffe7db70000 - 0x00007ffe7dc8a000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffe7f170000 - 0x00007ffe7f20e000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffe7d720000 - 0x00007ffe7d7bd000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffe7fe10000 - 0x00007ffe7fe3f000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffe49730000 - 0x00007ffe4973c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ffdeecf0000 - 0x00007ffdeed7d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007ffdc73d0000 - 0x00007ffdc8160000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ffe7f760000 - 0x00007ffe7f80f000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffe7f0d0000 - 0x00007ffe7f16f000 	C:\WINDOWS\System32\sechost.dll
0x00007ffe7f8a0000 - 0x00007ffe7f9c3000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffe7d6f0000 - 0x00007ffe7d717000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffe7f2c0000 - 0x00007ffe7f32b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffe7cc80000 - 0x00007ffe7cccb000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffe74cf0000 - 0x00007ffe74cfa000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffe6d7c0000 - 0x00007ffe6d7e7000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffe7caf0000 - 0x00007ffe7cb02000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffe7b4f0000 - 0x00007ffe7b502000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffe49710000 - 0x00007ffe4971a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ffe74aa0000 - 0x00007ffe74ca1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffe56040000 - 0x00007ffe56074000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffe7dff0000 - 0x00007ffe7e072000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffe49700000 - 0x00007ffe4970f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ffe496e0000 - 0x00007ffe496ff000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ffe437b0000 - 0x00007ffe437c8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5794_none_60bcd33171f2783c;c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\jre\21.0.7-win32-x86_64\bin\server

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\lombok\lombok-1.18.36.jar 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.42.0\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\1eb1b3ffae57f2c6129ade188dcbd886\redhat.java\ss_ws --pipe=\\.\pipe\lsp-e6a02c6c6037bbb2e7f06e599f91b30b-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.42.0-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250424-1814.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 357564416                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 1073741824                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk-11
PATH=C:\app\tshep\product\21c\dbhomeXE\bin;C:\app\tshep\product\18.0.0\dbhomeXE\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files (x86)\dotnet\;C:\Program Files\Java\jdk-11\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\xampp\php;C:\Program Files\Java\glassfish4\bin;C:\Users\<USER>\Downloads\apache-maven-3.9.9\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.12;C:\Program Files (x86)\cloudflared\;C:\Program Files\Redis\;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\anaconda3;C:\Users\<USER>\anaconda3\Library\mingw-w64\bin;C:\Users\<USER>\anaconda3\Library\usr\bin;C:\Users\<USER>\anaconda3\Library\bin;C:\Users\<USER>\anaconda3\Scripts;C:\Users\<USER>\anaconda3\bin;C:\Users\<USER>\anaconda3\condabin;C:\app\tshep\product\21c\dbhomeXE\bin;C:\app\tshep\product\18.0.0\dbhomeXE\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\dotnet;C:\Program Files (x86)\dotnet;C:\Program Files\Java\jdk-11\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs;C:\ProgramData\chocolatey\bin;C:\xampp\php;C:\Program Files\Java\glassfish4\bin;C:\Users\<USER>\Downloads\apache-maven-3.9.9\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.12;C:\Program Files (x86)\cloudflared;C:\Program Files\Redis;C:\Users\<USER>\scoop\shims;C:\app\tshep\product\21c\dbhomeXE\bin;C:\app\tshep\product\18.0.0\dbhomeXE\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Program Files\dotnet;C:\Program Files (x86)\dotnet;C:\Program Files\Java\jdk-11\bin;C:\Program Files\Git\cmd;C:\Program Files\nodejs;C:\ProgramData\chocolatey\bin;C:\xampp\php;C:\Program Files\Java\glassfish4\bin;C:\Users\<USER>\Downloads\apache-maven-3.9.9\bin;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.12;C:\Program Files (x86)\cloudflared;C:\Program Files\Redis;C:\Users\<USER>\Downloads\ffmpeg-2025-03-24-git-cbbc927a67-full_build\ffmpeg-2025-03-24-git-cbbc927a67-full_build\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\Downloads\ffmpeg-2025-03-24-git-cbbc927a67-full_build\ffmpeg-2025-03-24-git-cbbc927a67-full_build\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;C:\Users\<USER>\.dotnet\tools
USERNAME=tshep
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 140 Stepping 1, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5794)
OS uptime: 0 days 0:38 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 140 stepping 1 microcode 0x9a, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv, rdtscp, rdpid, fsrm, gfni, avx512_bitalg, f16c, cet_ibt, cet_ss, avx512_ifma
Processor Information for processor 0
  Max Mhz: 2419, Current Mhz: 907, Mhz Limit: 2419
Processor Information for processor 1
  Max Mhz: 2419, Current Mhz: 2419, Mhz Limit: 2419
Processor Information for processor 2
  Max Mhz: 2419, Current Mhz: 2419, Mhz Limit: 2419
Processor Information for processor 3
  Max Mhz: 2419, Current Mhz: 907, Mhz Limit: 2419
Processor Information for processor 4
  Max Mhz: 2419, Current Mhz: 2419, Mhz Limit: 2419
Processor Information for processor 5
  Max Mhz: 2419, Current Mhz: 2419, Mhz Limit: 2419
Processor Information for processor 6
  Max Mhz: 2419, Current Mhz: 907, Mhz Limit: 2419
Processor Information for processor 7
  Max Mhz: 2419, Current Mhz: 907, Mhz Limit: 2419

Memory: 4k page, system-wide physical 7975M (1259M free)
TotalPageFile size 11375M (AvailPageFile size 2M)
current process WorkingSet (physical memory assigned to process): 16M, peak: 16M
current process commit charge ("private bytes"): 149M, peak: 150M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
