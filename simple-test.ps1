# Simple Todo API Test Script
Write-Host "=== Todo API Test Suite ===" -ForegroundColor Green

# Test 1: Check if API is running
Write-Host "Test 1: Checking if API is running..." -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5047/todos" -Method GET
    Write-Host "✅ API is running! Response: $($response | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "❌ API is not running. Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please start the API first with: dotnet run --project ToDo" -ForegroundColor Yellow
    exit 1
}

# Test 2: Create a todo
Write-Host "`nTest 2: Creating a todo..." -ForegroundColor Cyan
try {
    $body = @{
        title = "Test Todo"
        description = "This is a test todo"
    } | ConvertTo-Json
    
    $response = Invoke-RestMethod -Uri "http://localhost:5047/todos" -Method POST -Body $body -ContentType "application/json"
    Write-Host "✅ Todo created! Response: $($response | ConvertTo-Json)" -ForegroundColor Green
    $todoId = $response.id
} catch {
    Write-Host "❌ Failed to create todo. Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Get all todos
Write-Host "`nTest 3: Getting all todos..." -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5047/todos" -Method GET
    Write-Host "✅ Got todos! Count: $($response.Count)" -ForegroundColor Green
    Write-Host "Response: $($response | ConvertTo-Json)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Failed to get todos. Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Get specific todo
Write-Host "`nTest 4: Getting specific todo (ID: $todoId)..." -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5047/todos/$todoId" -Method GET
    Write-Host "✅ Got specific todo! Response: $($response | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to get specific todo. Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Update todo
Write-Host "`nTest 5: Updating todo status to Completed..." -ForegroundColor Cyan
try {
    $body = @{
        status = "Completed"
    } | ConvertTo-Json
    
    $response = Invoke-RestMethod -Uri "http://localhost:5047/todos/$todoId" -Method PUT -Body $body -ContentType "application/json"
    Write-Host "✅ Todo updated! Response: $($response | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to update todo. Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Get completion stats
Write-Host "`nTest 6: Getting completion statistics..." -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5047/todos/stats" -Method GET
    Write-Host "✅ Got stats! Response: $($response | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to get stats. Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 7: Filter by status
Write-Host "`nTest 7: Filtering todos by completed status..." -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5047/todos?status=completed" -Method GET
    Write-Host "✅ Filtered todos! Count: $($response.Count)" -ForegroundColor Green
    Write-Host "Response: $($response | ConvertTo-Json)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Failed to filter todos. Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 8: Create another todo
Write-Host "`nTest 8: Creating another todo..." -ForegroundColor Cyan
try {
    $body = @{
        title = "Second Todo"
        description = "This is a second test todo"
    } | ConvertTo-Json
    
    $response = Invoke-RestMethod -Uri "http://localhost:5047/todos" -Method POST -Body $body -ContentType "application/json"
    Write-Host "✅ Second todo created! Response: $($response | ConvertTo-Json)" -ForegroundColor Green
    $secondTodoId = $response.id
} catch {
    Write-Host "❌ Failed to create second todo. Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 9: Filter by pending status
Write-Host "`nTest 9: Filtering todos by pending status..." -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5047/todos?status=pending" -Method GET
    Write-Host "✅ Filtered pending todos! Count: $($response.Count)" -ForegroundColor Green
    Write-Host "Response: $($response | ConvertTo-Json)" -ForegroundColor Gray
} catch {
    Write-Host "❌ Failed to filter pending todos. Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 10: Updated completion stats
Write-Host "`nTest 10: Getting updated completion statistics..." -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5047/todos/stats" -Method GET
    Write-Host "✅ Got updated stats! Response: $($response | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to get updated stats. Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 11: Delete a todo
Write-Host "`nTest 11: Deleting a todo (ID: $secondTodoId)..." -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5047/todos/$secondTodoId" -Method DELETE
    Write-Host "✅ Todo deleted successfully!" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to delete todo. Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 12: Verify deletion
Write-Host "`nTest 12: Verifying todo was deleted..." -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5047/todos/$secondTodoId" -Method GET
    Write-Host "❌ Todo still exists! This should not happen." -ForegroundColor Red
} catch {
    if ($_.Exception.Response.StatusCode -eq 404) {
        Write-Host "✅ Todo successfully deleted (404 Not Found)!" -ForegroundColor Green
    } else {
        Write-Host "❌ Unexpected error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 13: Final stats
Write-Host "`nTest 13: Getting final completion statistics..." -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5047/todos/stats" -Method GET
    Write-Host "✅ Got final stats! Response: $($response | ConvertTo-Json)" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to get final stats. Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 14: Error handling - Invalid status filter
Write-Host "`nTest 14: Testing error handling with invalid status filter..." -ForegroundColor Cyan
try {
    $response = Invoke-RestMethod -Uri "http://localhost:5047/todos?status=invalid" -Method GET
    Write-Host "❌ Should have failed with invalid status!" -ForegroundColor Red
} catch {
    if ($_.Exception.Response.StatusCode -eq 400) {
        Write-Host "✅ Correctly handled invalid status (400 Bad Request)!" -ForegroundColor Green
    } else {
        Write-Host "❌ Unexpected error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Test 15: Error handling - Missing title
Write-Host "`nTest 15: Testing error handling with missing title..." -ForegroundColor Cyan
try {
    $body = @{
        description = "Todo without title"
    } | ConvertTo-Json
    
    $response = Invoke-RestMethod -Uri "http://localhost:5047/todos" -Method POST -Body $body -ContentType "application/json"
    Write-Host "❌ Should have failed with missing title!" -ForegroundColor Red
} catch {
    if ($_.Exception.Response.StatusCode -eq 400) {
        Write-Host "✅ Correctly handled missing title (400 Bad Request)!" -ForegroundColor Green
    } else {
        Write-Host "❌ Unexpected error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== Test Suite Complete ===" -ForegroundColor Green
