### Get all todos
GET http://localhost:5047/todos

###

### Get todos by status (pending)
GET http://localhost:5047/todos?status=pending

###

### Get todos by status (completed)
GET http://localhost:5047/todos?status=completed

###

### Create a new todo
POST http://localhost:5047/todos
Content-Type: application/json

{
  "title": "Learn C# Minimal APIs",
  "description": "Study and practice building minimal APIs with .NET Core"
}

###

### Create another todo
POST http://localhost:5047/todos
Content-Type: application/json

{
  "title": "Complete project documentation",
  "description": "Write comprehensive documentation for the Todo API"
}

###

### Get a specific todo by ID
GET http://localhost:5047/todos/1

###

### Update a todo (mark as completed)
PUT http://localhost:5047/todos/1
Content-Type: application/json

{
  "status": "Completed"
}

###

### Update a todo (change title and description)
PUT http://localhost:5047/todos/2
Content-Type: application/json

{
  "title": "Updated: Complete project documentation",
  "description": "Write comprehensive documentation for the Todo API including examples",
  "status": "Pending"
}

###

### Get completion statistics
GET http://localhost:5047/todos/stats

###

### Delete a todo
DELETE http://localhost:5047/todos/1

###

### Create a todo with minimal data (only title)
POST http://localhost:5047/todos
Content-Type: application/json

{
  "title": "Simple task"
}

###

### Try to create a todo without title (should fail)
POST http://localhost:5047/todos
Content-Type: application/json

{
  "description": "This should fail because title is missing"
}

###