# Todo REST API - HTTP Request Examples
#
# This file demonstrates all API endpoints with detailed explanations
# Each request shows different HTTP methods, status codes, and JSON structures
#
# Computer Science Concepts Demonstrated:
# - HTTP Protocol (GET, POST, PUT, DELETE methods)
# - RESTful API design patterns
# - JSON serialization/deserialization
# - CRUD operations (Create, Read, Update, Delete)
# - Query parameters for filtering
# - HTTP status codes and error handling

### TEST 1: Get all todos (READ operation)
# HTTP Method: GET (safe, idempotent)
# Expected: HTTP 200 OK with JSON array
# When empty: Returns []
# When populated: Returns array of todo objects
GET http://localhost:5047/todos

###

### TEST 2: Filter todos by status - Pending (READ with query parameter)
# HTTP Method: GET with query parameter
# Demonstrates: URL query string usage (?status=pending)
# Expected: HTTP 200 OK with filtered results
# Case-insensitive: "pending", "Pending", "PENDING" all work
GET http://localhost:5047/todos?status=pending

###

### TEST 3: Filter todos by status - Completed (READ with query parameter)
# HTTP Method: GET with query parameter
# Demonstrates: Filtering collections based on enum values
# Expected: HTTP 200 OK with completed todos only
GET http://localhost:5047/todos?status=completed

###

### TEST 4: Create a new todo (CREATE operation)
# HTTP Method: POST (not idempotent - creates new resource)
# Content-Type: application/json (tells server how to parse body)
# Expected: HTTP 201 Created with Location header
# Demonstrates: JSON request body, automatic ID assignment
POST http://localhost:5047/todos
Content-Type: application/json

{
  "title": "Learn C# Minimal APIs",
  "description": "Study and practice building minimal APIs with .NET Core"
}

###

### TEST 5: Create another todo (CREATE operation)
# Demonstrates: Multiple resource creation
# Each POST creates a new resource with unique ID
# Expected: HTTP 201 Created with incremented ID
POST http://localhost:5047/todos
Content-Type: application/json

{
  "title": "Complete project documentation",
  "description": "Write comprehensive documentation for the Todo API"
}

###

### TEST 6: Get a specific todo by ID (READ operation)
# HTTP Method: GET with route parameter
# URL Pattern: /todos/{id} where {id} is an integer
# Expected: HTTP 200 OK with single todo object
# If not found: HTTP 404 Not Found
GET http://localhost:5047/todos/1

###

### TEST 7: Update a todo - Change status only (UPDATE operation)
# HTTP Method: PUT (idempotent - same request produces same result)
# Demonstrates: Partial updates (only status field provided)
# Expected: HTTP 200 OK with updated todo
# Note: UpdatedAt timestamp will be set automatically
PUT http://localhost:5047/todos/1
Content-Type: application/json

{
  "status": "Completed"
}

###

### TEST 8: Update a todo - Change multiple fields (UPDATE operation)
# Demonstrates: Updating multiple fields simultaneously
# All fields are optional in PUT requests (partial updates)
# Expected: HTTP 200 OK with all changes applied
PUT http://localhost:5047/todos/2
Content-Type: application/json

{
  "title": "Updated: Complete project documentation",
  "description": "Write comprehensive documentation for the Todo API including examples",
  "status": "Pending"
}

###

### TEST 9: Get completion statistics (READ computed data)
# Demonstrates: Derived/calculated data endpoints
# Returns aggregated information about all todos
# Expected: HTTP 200 OK with statistics object
# Shows: totalTasks, completedTasks, completionPercentage
GET http://localhost:5047/todos/stats

###

### TEST 10: Delete a todo (DELETE operation)
# HTTP Method: DELETE (idempotent - multiple deletes have same effect)
# Expected: HTTP 204 No Content (successful deletion)
# If not found: HTTP 404 Not Found
# Note: No response body for successful deletion
DELETE http://localhost:5047/todos/1

###

### TEST 11: Create todo with minimal data (CREATE with optional fields)
# Demonstrates: Optional fields in JSON (description omitted)
# Only title is required, description defaults to empty string
# Expected: HTTP 201 Created with empty description
POST http://localhost:5047/todos
Content-Type: application/json

{
  "title": "Simple task"
}

###

### TEST 12: Error handling - Missing required field (INPUT VALIDATION)
# Demonstrates: Server-side validation and error responses
# Missing title should trigger validation error
# Expected: HTTP 400 Bad Request with error message
# Shows: Proper error handling and user feedback
POST http://localhost:5047/todos
Content-Type: application/json

{
  "description": "This should fail because title is missing"
}

###

### TEST 13: Error handling - Invalid status filter (QUERY VALIDATION)
# Demonstrates: Query parameter validation
# Invalid enum values should return error
# Expected: HTTP 400 Bad Request with helpful error message
GET http://localhost:5047/todos?status=invalid

###

### TEST 14: Error handling - Non-existent resource (RESOURCE NOT FOUND)
# Demonstrates: 404 error handling for missing resources
# Expected: HTTP 404 Not Found
# Shows: Proper REST error responses
GET http://localhost:5047/todos/999

###

### TEST 15: Error handling - Update non-existent todo (UPDATE VALIDATION)
# Demonstrates: Validation before update operations
# Expected: HTTP 404 Not Found
# Shows: Defensive programming practices
PUT http://localhost:5047/todos/999
Content-Type: application/json

{
  "title": "This won't work"
}

###