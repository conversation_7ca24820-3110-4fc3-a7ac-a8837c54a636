version 2
JvmtiExport can_access_local_variables 0
JvmtiExport can_hotswap_or_post_breakpoint 1
JvmtiExport can_post_on_exceptions 0
# 280 ciObject found
instanceKlass java/io/BufferedInputStream
ciInstanceKlass java/lang/Cloneable 1 0 7 100 1 100 1 1 1
instanceKlass org/eclipse/core/internal/preferences/PreferencesService
instanceKlass org/eclipse/core/runtime/preferences/IPreferencesService
instanceKlass org/eclipse/core/internal/preferences/exchange/ILegacyPreferences
instanceKlass org/eclipse/core/internal/preferences/PreferencesOSGiUtils
instanceKlass org/eclipse/core/internal/preferences/Activator
instanceKlass org/eclipse/core/runtime/preferences/IScopeContext
instanceKlass org/eclipse/core/runtime/content/IContentTypeManager$ISelectionPolicy
instanceKlass org/eclipse/core/internal/content/ContentTypeCatalog
instanceKlass org/eclipse/core/internal/content/BasicDescription
instanceKlass org/apache/felix/scr/impl/inject/ValueUtils
instanceKlass org/eclipse/core/internal/content/ILazySource
instanceKlass org/eclipse/core/internal/adapter/AdapterManagerListener
instanceKlass org/eclipse/core/internal/runtime/IAdapterManagerProvider
instanceKlass org/eclipse/core/runtime/IRegistryEventListener
instanceKlass org/eclipse/core/internal/registry/osgi/RegistryCommandProvider
instanceKlass org/eclipse/osgi/framework/console/CommandProvider
instanceKlass org/eclipse/core/internal/registry/RegistryProviderFactory
instanceKlass org/eclipse/core/internal/registry/osgi/RegistryProviderOSGI
instanceKlass org/eclipse/core/internal/registry/TemporaryObjectManager
instanceKlass org/eclipse/core/internal/registry/RegistryIndexChildren
instanceKlass org/eclipse/core/internal/registry/RegistryIndexElement
instanceKlass org/eclipse/core/internal/registry/CombinedEventDelta
instanceKlass org/eclipse/core/internal/registry/Contribution
instanceKlass org/eclipse/core/runtime/Assert
instanceKlass java/lang/invoke/DirectMethodHandle$1
instanceKlass org/eclipse/core/internal/runtime/LocalizationUtils
instanceKlass org/eclipse/core/runtime/Status
instanceKlass  @bci jdk/internal/reflect/MethodHandleObjectFieldAccessorImpl set (Ljava/lang/Object;Ljava/lang/Object;)V 29 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002d11016c800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11016c400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11016c000
instanceKlass  @bci org/eclipse/osgi/util/NLS <clinit> ()V 7 <appendix> argL0 ; # org/eclipse/osgi/util/NLS$$Lambda+0x000002d1101332f8
instanceKlass org/eclipse/osgi/util/NLS
instanceKlass org/eclipse/core/internal/runtime/ResourceTranslator
instanceKlass org/eclipse/core/runtime/spi/RegistryContributor
instanceKlass org/eclipse/core/runtime/IContributor
instanceKlass org/eclipse/core/runtime/ContributorFactoryOSGi
instanceKlass org/eclipse/core/internal/registry/osgi/EclipseBundleListener
instanceKlass org/eclipse/core/internal/registry/HashtableOfStringAndInt
instanceKlass org/eclipse/core/internal/registry/KeyedHashSet
instanceKlass org/eclipse/core/runtime/IConfigurationElement
instanceKlass org/eclipse/core/internal/registry/Handle
instanceKlass org/eclipse/core/internal/registry/RegistryObjectManager
instanceKlass org/eclipse/core/internal/registry/IObjectManager
instanceKlass  @bci org/eclipse/core/internal/registry/RegistryProperties getContextProperty (Ljava/lang/String;)Ljava/lang/String; 18 <appendix> member <vmtarget> ; # org/eclipse/core/internal/registry/RegistryProperties$$Lambda+0x000002d110164a48
instanceKlass org/eclipse/core/internal/registry/RegistryTimestamp
instanceKlass org/eclipse/core/internal/registry/TableReader
instanceKlass org/eclipse/core/runtime/ListenerList
instanceKlass org/eclipse/core/internal/registry/ReadWriteMonitor
instanceKlass org/eclipse/core/runtime/IExtensionPoint
instanceKlass org/eclipse/core/runtime/ISafeRunnable
instanceKlass org/eclipse/core/runtime/IExtensionDelta
instanceKlass org/eclipse/core/internal/registry/RegistryObjectFactory
instanceKlass org/eclipse/core/runtime/IExtension
instanceKlass org/eclipse/core/internal/registry/RegistryObject
instanceKlass org/eclipse/core/internal/registry/KeyedElement
instanceKlass org/eclipse/core/internal/registry/ExtensionRegistry
instanceKlass org/eclipse/core/runtime/spi/IDynamicExtensionRegistry
instanceKlass org/eclipse/core/runtime/IExtensionRegistry
instanceKlass org/eclipse/core/runtime/RegistryFactory
instanceKlass org/eclipse/core/internal/registry/ReferenceMap$IEntry
instanceKlass org/eclipse/core/internal/registry/ReferenceMap
instanceKlass org/eclipse/core/internal/registry/osgi/OSGIUtils
instanceKlass org/eclipse/core/internal/registry/osgi/EquinoxUtils
instanceKlass org/eclipse/core/internal/registry/RegistryProperties
instanceKlass org/eclipse/core/runtime/spi/IRegistryProvider
instanceKlass org/eclipse/core/runtime/spi/RegistryStrategy
instanceKlass org/eclipse/core/internal/registry/osgi/Activator
instanceKlass org/eclipse/core/runtime/IRegistryChangeListener
instanceKlass org/eclipse/core/runtime/content/IContentType
instanceKlass org/eclipse/core/runtime/content/IContentTypeSettings
instanceKlass org/osgi/service/prefs/Preferences
instanceKlass org/eclipse/core/internal/content/IContentTypeInfo
instanceKlass org/eclipse/core/runtime/content/IContentDescription
instanceKlass org/apache/felix/scr/impl/inject/internal/ComponentConstructorImpl
instanceKlass org/apache/felix/scr/impl/inject/ReferenceMethods$1
instanceKlass org/apache/felix/scr/impl/inject/ReferenceMethod
instanceKlass org/apache/felix/scr/impl/inject/methods/BindMethods
instanceKlass org/apache/felix/scr/impl/inject/ReferenceMethods
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$NotApplicable
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$NotResolved
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod$State
instanceKlass org/apache/felix/scr/impl/inject/BaseParameter
instanceKlass org/apache/felix/scr/impl/inject/methods/BaseMethod
instanceKlass org/eclipse/core/internal/content/ContentTypeMatcher
instanceKlass org/eclipse/core/runtime/content/IContentTypeManager
instanceKlass org/eclipse/core/runtime/content/IContentTypeMatcher
instanceKlass org/apache/felix/scr/impl/helper/ComponentServiceObjectsHelper
instanceKlass org/apache/felix/scr/impl/manager/EdgeInfo
instanceKlass org/apache/felix/scr/impl/manager/ComponentContextImpl$ComponentInstanceImpl
instanceKlass org/osgi/service/component/ComponentInstance
instanceKlass org/apache/felix/scr/impl/manager/ComponentContextImpl
instanceKlass org/apache/felix/scr/impl/manager/RegistrationManager$RegStateWrapper
instanceKlass org/apache/felix/scr/impl/BundleComponentActivator$ListenerInfo
instanceKlass org/apache/felix/scr/impl/manager/ServiceTracker$AbstractTracked
instanceKlass org/apache/felix/scr/impl/manager/ExtendedServiceListener
instanceKlass org/apache/felix/scr/impl/manager/ServiceTracker
instanceKlass org/apache/felix/scr/impl/helper/Coercions
instanceKlass org/apache/felix/scr/impl/manager/DependencyManager$AbstractCustomizer
instanceKlass org/apache/felix/scr/impl/manager/DependencyManager$Customizer
instanceKlass org/apache/felix/scr/impl/manager/ServiceTrackerCustomizer
instanceKlass org/apache/felix/scr/impl/inject/RefPair
instanceKlass org/apache/felix/scr/impl/inject/OpenStatus
instanceKlass org/apache/felix/scr/impl/manager/DependencyManager
instanceKlass org/apache/felix/scr/impl/manager/ReferenceManager
instanceKlass org/osgi/util/promise/Deferred
instanceKlass org/apache/felix/scr/impl/manager/SingleComponentManager$SetImplementationObject
instanceKlass org/apache/felix/scr/impl/inject/ScrComponentContext
instanceKlass org/apache/felix/scr/component/ExtComponentContext
instanceKlass org/apache/felix/scr/impl/manager/RegistrationManager
instanceKlass org/apache/felix/scr/impl/helper/ConfigAdminTracker$1
instanceKlass org/apache/felix/scr/impl/helper/ConfigAdminTracker
instanceKlass java/util/Timer$ThreadReaper
instanceKlass java/util/TaskQueue
instanceKlass java/util/Timer
instanceKlass org/apache/felix/scr/impl/inject/ComponentConstructor
instanceKlass org/apache/felix/scr/impl/inject/LifecycleMethod
instanceKlass org/apache/felix/scr/impl/inject/internal/ComponentMethodsImpl
instanceKlass org/apache/felix/scr/impl/metadata/TargetedPID
instanceKlass java/util/concurrent/CompletionStage
instanceKlass org/osgi/util/promise/PromiseImpl
instanceKlass org/osgi/util/promise/Promise
instanceKlass org/osgi/util/promise/PromiseFactory
instanceKlass org/osgi/util/promise/Promises
instanceKlass org/apache/felix/scr/impl/inject/ComponentMethods
instanceKlass org/osgi/service/component/ComponentFactory
instanceKlass org/apache/felix/scr/impl/manager/AbstractComponentManager
instanceKlass org/apache/felix/scr/impl/manager/ComponentManager
instanceKlass org/apache/felix/scr/impl/manager/ConfigurableComponentHolder
instanceKlass org/apache/felix/scr/impl/manager/ComponentContainer
instanceKlass org/apache/felix/scr/impl/ComponentRegistryKey
instanceKlass org/apache/felix/scr/impl/metadata/PropertyMetadata
instanceKlass org/apache/felix/scr/impl/metadata/ReferenceMetadata
instanceKlass org/apache/felix/scr/impl/metadata/ServiceMetadata
instanceKlass org/apache/felix/scr/impl/metadata/ComponentMetadata
instanceKlass org/apache/felix/scr/impl/xml/XmlConstants
instanceKlass com/sun/org/apache/xerces/internal/impl/Constants$ArrayEnumeration
instanceKlass com/sun/org/apache/xerces/internal/impl/Constants
instanceKlass com/sun/org/apache/xerces/internal/parsers/AbstractSAXParser$LocatorProxy
instanceKlass org/xml/sax/ext/Locator2
instanceKlass org/xml/sax/Locator
instanceKlass com/sun/org/apache/xerces/internal/util/XMLSymbols
instanceKlass com/sun/org/apache/xerces/internal/util/XMLChar
instanceKlass com/sun/xml/internal/stream/Entity
instanceKlass com/sun/xml/internal/stream/util/BufferAllocator
instanceKlass com/sun/xml/internal/stream/util/ThreadLocalBufferAllocator
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityManager$EncodingInfo
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLLimitAnalyzer
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLInputSource
instanceKlass com/sun/org/apache/xerces/internal/util/ErrorHandlerWrapper
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLErrorHandler
instanceKlass com/sun/org/apache/xerces/internal/impl/ExternalSubsetResolver
instanceKlass com/sun/org/apache/xerces/internal/util/EntityResolverWrapper
instanceKlass org/xml/sax/ext/EntityResolver2
instanceKlass org/xml/sax/InputSource
instanceKlass com/sun/org/apache/xerces/internal/parsers/AbstractSAXParser$AttributesProxy
instanceKlass org/xml/sax/ext/Attributes2
instanceKlass org/xml/sax/Attributes
instanceKlass org/xml/sax/AttributeList
instanceKlass com/sun/org/apache/xerces/internal/util/FeatureState
instanceKlass com/sun/org/apache/xerces/internal/util/PropertyState
instanceKlass com/sun/org/apache/xerces/internal/impl/msg/XMLMessageFormatter
instanceKlass com/sun/org/apache/xerces/internal/util/MessageFormatter
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLVersionDetector
instanceKlass com/sun/org/apache/xerces/internal/impl/validation/ValidationManager
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/NMTOKENDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/NOTATIONDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/ENTITYDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/ListDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/IDREFDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/IDDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/dtd/StringDatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/DatatypeValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/DTDDVFactory
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/DTDGrammarBucket
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLAttributeDecl
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLSimpleType
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLElementDecl
instanceKlass com/sun/org/apache/xerces/internal/impl/validation/ValidationState
instanceKlass com/sun/org/apache/xerces/internal/impl/dv/ValidationContext
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLDTDValidator
instanceKlass com/sun/org/apache/xerces/internal/impl/RevalidationHandler
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLDTDValidatorFilter
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDocumentFilter
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLEntityDecl
instanceKlass com/sun/org/apache/xerces/internal/impl/dtd/XMLDTDProcessor
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDContentModelFilter
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDFilter
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDScanner
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDContentModelSource
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDTDSource
instanceKlass com/sun/org/apache/xerces/internal/xni/grammars/XMLDTDDescription
instanceKlass com/sun/org/apache/xerces/internal/xni/grammars/XMLGrammarDescription
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentScannerImpl$TrailingMiscDriver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentScannerImpl$PrologDriver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentScannerImpl$XMLDeclDriver
instanceKlass com/sun/org/apache/xerces/internal/util/NamespaceSupport
instanceKlass com/sun/org/apache/xerces/internal/xni/NamespaceContext
instanceKlass com/sun/org/apache/xerces/internal/util/XMLAttributesImpl$Attribute
instanceKlass com/sun/org/apache/xerces/internal/util/XMLAttributesImpl
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLAttributes
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$FragmentContentDriver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$Driver
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$ElementStack2
instanceKlass com/sun/org/apache/xerces/internal/xni/QName
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLDocumentFragmentScannerImpl$ElementStack
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLString
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLScanner
instanceKlass com/sun/xml/internal/stream/XMLBufferListener
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityHandler
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDocumentScanner
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLDocumentSource
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLErrorReporter
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityScanner
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLLocator
instanceKlass com/sun/xml/internal/stream/XMLEntityStorage
instanceKlass com/sun/org/apache/xerces/internal/util/AugmentationsImpl$AugmentationsItemsContainer
instanceKlass com/sun/org/apache/xerces/internal/util/AugmentationsImpl
instanceKlass com/sun/org/apache/xerces/internal/xni/Augmentations
instanceKlass com/sun/org/apache/xerces/internal/util/XMLResourceIdentifierImpl
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLResourceIdentifier
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityManager
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLEntityResolver
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLComponent
instanceKlass com/sun/org/apache/xerces/internal/util/SymbolTable$Entry
instanceKlass com/sun/org/apache/xerces/internal/util/SymbolTable
instanceKlass jdk/xml/internal/JdkConstants
instanceKlass jdk/xml/internal/JdkXmlUtils
instanceKlass com/sun/org/apache/xerces/internal/util/ParserConfigurationSettings
instanceKlass com/sun/org/apache/xerces/internal/parsers/XML11Configurable
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLPullParserConfiguration
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLParserConfiguration
instanceKlass com/sun/org/apache/xerces/internal/xni/parser/XMLComponentManager
instanceKlass com/sun/org/apache/xerces/internal/parsers/XMLParser
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLDTDContentModelHandler
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLDTDHandler
instanceKlass com/sun/org/apache/xerces/internal/xni/XMLDocumentHandler
instanceKlass org/xml/sax/XMLReader
instanceKlass org/xml/sax/Parser
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityManager
instanceKlass javax/xml/parsers/SAXParser
instanceKlass com/sun/org/apache/xerces/internal/xs/PSVIProvider
instanceKlass com/sun/org/apache/xerces/internal/jaxp/JAXPConstants
instanceKlass  @bci javax/xml/parsers/FactoryFinder newInstance (Ljava/lang/Class;Ljava/lang/String;Ljava/lang/ClassLoader;ZZ)Ljava/lang/Object; 104 <appendix> member <vmtarget> ; # javax/xml/parsers/FactoryFinder$$Lambda+0x000002d1100dda38
instanceKlass  @bci jdk/xml/internal/SecuritySupport getContextClassLoader ()Ljava/lang/ClassLoader; 0 <appendix> argL0 ; # jdk/xml/internal/SecuritySupport$$Lambda+0x000002d1100dd5d0
instanceKlass  @bci javax/xml/parsers/FactoryFinder find (Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Object; 104 <appendix> member <vmtarget> ; # javax/xml/parsers/FactoryFinder$$Lambda+0x000002d1100dd3b8
instanceKlass javax/xml/parsers/FactoryFinder$1
instanceKlass  @bci jdk/xml/internal/SecuritySupport getFileInputStream (Ljava/io/File;)Ljava/io/FileInputStream; 1 <appendix> member <vmtarget> ; # jdk/xml/internal/SecuritySupport$$Lambda+0x000002d1100dcf80
instanceKlass  @bci jdk/xml/internal/SecuritySupport doesFileExist (Ljava/io/File;)Z 1 <appendix> member <vmtarget> ; # jdk/xml/internal/SecuritySupport$$Lambda+0x000002d1100dcd68
instanceKlass  @bci javax/xml/parsers/FactoryFinder find (Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Object; 6 <appendix> member <vmtarget> ; # javax/xml/parsers/FactoryFinder$$Lambda+0x000002d1100dcb50
instanceKlass  @bci jdk/xml/internal/SecuritySupport getSystemProperty (Ljava/lang/String;)Ljava/lang/String; 1 <appendix> member <vmtarget> ; # jdk/xml/internal/SecuritySupport$$Lambda+0x000002d1100dc938
instanceKlass jdk/xml/internal/SecuritySupport
instanceKlass javax/xml/parsers/FactoryFinder
instanceKlass javax/xml/parsers/SAXParserFactory
instanceKlass org/xml/sax/helpers/DefaultHandler
instanceKlass org/xml/sax/ErrorHandler
instanceKlass org/xml/sax/ContentHandler
instanceKlass org/xml/sax/DTDHandler
instanceKlass org/xml/sax/EntityResolver
instanceKlass org/apache/felix/scr/impl/BundleComponentActivator
instanceKlass org/apache/felix/scr/impl/manager/ComponentActivator
instanceKlass org/apache/felix/scr/impl/manager/ExtendedServiceListenerContext
instanceKlass org/eclipse/core/internal/runtime/IAdapterFactoryExt
instanceKlass org/eclipse/core/internal/runtime/AdapterFactoryBridge$LazyAdapterFactory
instanceKlass org/eclipse/core/internal/runtime/AdapterFactoryBridge
instanceKlass org/eclipse/core/runtime/IAdapterFactory
instanceKlass org/eclipse/core/internal/runtime/TracingOptions$1
instanceKlass org/eclipse/core/internal/runtime/TracingOptions
instanceKlass java/util/AbstractMap$1$1
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller current ()Ljava/util/Optional; 4 <appendix> argL0 ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x000002d110149018
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller$ReferenceAndService track ()Ljava/util/Optional; 45 <appendix> argL2 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002d11014a000
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller trackCurrent ()Ljava/util/Optional; 19 <appendix> member <vmtarget> ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x000002d110148de0
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller getCurrent ()Ljava/util/Optional; 12 <appendix> member <vmtarget> ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x000002d110148ba8
instanceKlass org/eclipse/core/runtime/ServiceCaller$ReferenceAndService
instanceKlass org/eclipse/core/internal/runtime/AdapterManager
instanceKlass org/eclipse/core/runtime/IAdapterManager
instanceKlass org/eclipse/core/internal/runtime/PlatformURLConverter
instanceKlass org/eclipse/core/internal/runtime/RuntimeLog
instanceKlass org/eclipse/core/runtime/IStatus
instanceKlass org/eclipse/core/internal/runtime/PlatformLogWriter
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogReaderServiceImpl
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller <init> (Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;)V 39 <appendix> argL0 ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x000002d110145000
instanceKlass  @bci org/osgi/framework/FrameworkUtil getBundle (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 26 <appendix> member <vmtarget> ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000002d1101325f0
instanceKlass  @bci org/osgi/framework/FrameworkUtil getBundle (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 17 <appendix> argL0 ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000002d1101323c0
instanceKlass  @bci org/osgi/framework/FrameworkUtil getBundle (Ljava/lang/Class;)Lorg/osgi/framework/Bundle; 1 <appendix> member <vmtarget> ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000002d1101321a8
instanceKlass  @bci org/eclipse/core/runtime/ServiceCaller <init> (Ljava/lang/Class;Ljava/lang/Class;Ljava/lang/String;)V 31 <appendix> argL0 ; # org/eclipse/core/runtime/ServiceCaller$$Lambda+0x000002d110144c88
instanceKlass org/eclipse/core/runtime/ServiceCaller
instanceKlass org/eclipse/core/internal/runtime/Activator
instanceKlass org/apache/felix/scr/impl/config/ScrMetaTypeProviderServiceFactory
instanceKlass org/apache/felix/scr/impl/config/ScrManagedServiceServiceFactory
instanceKlass org/apache/felix/scr/impl/ComponentCommands$2
instanceKlass org/apache/felix/scr/impl/ComponentCommands$1
instanceKlass org/apache/felix/scr/impl/ComponentCommands
instanceKlass org/apache/felix/scr/impl/Activator$ScrExtension
instanceKlass org/apache/felix/scr/impl/ComponentActorThread$1
instanceKlass org/apache/felix/scr/impl/ComponentActorThread
instanceKlass org/apache/felix/scr/impl/runtime/ServiceComponentRuntimeImpl
instanceKlass org/apache/felix/scr/impl/manager/ComponentHolder
instanceKlass java/util/TimerTask
instanceKlass org/apache/felix/scr/impl/manager/RegionConfigurationSupport
instanceKlass org/apache/felix/scr/impl/ComponentRegistry
instanceKlass org/apache/felix/scr/impl/logger/ScrLogManager$1
instanceKlass org/apache/felix/scr/impl/logger/LogManager$LogDomain
instanceKlass org/apache/felix/scr/impl/logger/LogManager$Lock
instanceKlass org/apache/felix/scr/impl/logger/LogManager$LoggerFacade
instanceKlass org/apache/felix/scr/impl/logger/BundleLogger
instanceKlass org/apache/felix/scr/impl/logger/ComponentLogger
instanceKlass org/apache/felix/scr/impl/logger/ScrLogger
instanceKlass org/apache/felix/scr/impl/logger/InternalLogger
instanceKlass org/apache/felix/scr/impl/logger/ScrLoggerFactory
instanceKlass org/osgi/service/component/ComponentContext
instanceKlass org/osgi/service/component/ComponentServiceObjects
instanceKlass org/apache/felix/scr/impl/inject/internal/ClassUtils
instanceKlass org/apache/felix/scr/impl/config/ScrConfigurationImpl
instanceKlass org/osgi/service/component/runtime/ServiceComponentRuntime
instanceKlass org/apache/felix/scr/impl/manager/ScrConfiguration
instanceKlass org/apache/felix/scr/impl/logger/LogConfiguration
instanceKlass org/apache/felix/scr/impl/AbstractExtender
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11013a000
instanceKlass org/eclipse/osgi/internal/loader/buddy/PolicyHandler
instanceKlass sun/nio/fs/WindowsFileCopy
instanceKlass org/apache/aries/spifly/WeavingData
instanceKlass org/apache/aries/spifly/ConsumerRestriction
instanceKlass org/apache/aries/spifly/MethodRestriction
instanceKlass org/apache/aries/spifly/ArgRestrictions
instanceKlass org/apache/aries/spifly/BundleDescriptor
instanceKlass  @bci org/apache/aries/spifly/BaseActivator addConsumerWeavingData (Lorg/osgi/framework/Bundle;Ljava/lang/String;)V 177 <appendix> member <vmtarget> ; # org/apache/aries/spifly/BaseActivator$$Lambda+0x000002d11013d148
instanceKlass  @bci org/apache/aries/spifly/BaseActivator addConsumerWeavingData (Lorg/osgi/framework/Bundle;Ljava/lang/String;)V 161 <appendix> member <vmtarget> ; # org/apache/aries/spifly/BaseActivator$$Lambda+0x000002d11013cf00
instanceKlass  @bci org/apache/aries/spifly/BaseActivator addConsumerWeavingData (Lorg/osgi/framework/Bundle;Ljava/lang/String;)V 149 <appendix> argL0 ; # org/apache/aries/spifly/BaseActivator$$Lambda+0x000002d11013ccf0
instanceKlass  @bci org/apache/aries/spifly/BaseActivator addConsumerWeavingData (Lorg/osgi/framework/Bundle;Ljava/lang/String;)V 141 <appendix> argL0 ; # org/apache/aries/spifly/BaseActivator$$Lambda+0x000002d11013cac0
instanceKlass org/apache/aries/spifly/ConsumerBundleTrackerCustomizer
instanceKlass java/time/LocalTime$1
instanceKlass sun/util/locale/provider/LocaleResources
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getDecimalFormatSymbolsProvider ()Ljava/text/spi/DecimalFormatSymbolsProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x800000066
instanceKlass java/text/DecimalFormatSymbols
instanceKlass sun/util/resources/Bundles$CacheKeyReference
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter applyAliases (Ljava/util/Locale;)Ljava/util/Locale; 4 <appendix> argL0 ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x80000005f
instanceKlass sun/util/resources/Bundles$CacheKey
instanceKlass sun/util/resources/Bundles
instanceKlass sun/util/resources/LocaleData$LocaleDataStrategy
instanceKlass sun/util/resources/Bundles$Strategy
instanceKlass sun/util/resources/LocaleData$1
instanceKlass sun/util/resources/LocaleData
instanceKlass java/util/Locale$Builder
instanceKlass sun/util/locale/provider/CalendarDataUtility
instanceKlass sun/util/locale/provider/AvailableLanguageTags
instanceKlass  @bci sun/util/locale/provider/JRELocaleProviderAdapter getDateFormatSymbolsProvider ()Ljava/text/spi/DateFormatSymbolsProvider; 8 <appendix> member <vmtarget> ; # sun/util/locale/provider/JRELocaleProviderAdapter$$Lambda+0x800000065
instanceKlass sun/util/resources/cldr/provider/CLDRLocaleDataMetaInfo
instanceKlass jdk/internal/module/ModulePatcher$PatchedModuleReader
instanceKlass  @bci sun/util/cldr/CLDRLocaleProviderAdapter <init> ()V 4 <appendix> argL0 ; # sun/util/cldr/CLDRLocaleProviderAdapter$$Lambda+0x800000061
instanceKlass sun/util/locale/BaseLocale$Key
instanceKlass sun/util/locale/InternalLocaleBuilder$CaseInsensitiveChar
instanceKlass sun/util/locale/InternalLocaleBuilder
instanceKlass sun/util/locale/StringTokenIterator
instanceKlass sun/util/locale/ParseStatus
instanceKlass sun/util/locale/LanguageTag
instanceKlass sun/util/cldr/CLDRBaseLocaleDataMetaInfo
instanceKlass sun/util/locale/provider/LocaleDataMetaInfo
instanceKlass sun/util/locale/provider/ResourceBundleBasedAdapter
instanceKlass sun/util/locale/provider/LocaleProviderAdapter
instanceKlass java/util/spi/LocaleServiceProvider
instanceKlass java/text/DateFormatSymbols
instanceKlass java/time/LocalDate$1
instanceKlass java/time/ZonedDateTime$1
instanceKlass java/util/Calendar
instanceKlass java/util/Formatter$DateTime
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleProviderHelper loadPropertyResourceBundle (Ljava/lang/Module;Ljava/lang/Module;Ljava/lang/String;Ljava/util/Locale;)Ljava/util/ResourceBundle; 14 <appendix> member <vmtarget> ; # java/util/ResourceBundle$ResourceBundleProviderHelper$$Lambda+0x000002d1100d93b8
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleProviderHelper newResourceBundle (Ljava/lang/Class;)Ljava/util/ResourceBundle; 22 <appendix> member <vmtarget> ; # java/util/ResourceBundle$ResourceBundleProviderHelper$$Lambda+0x800000010
instanceKlass  @bci java/util/ResourceBundle$ResourceBundleProviderHelper loadResourceBundle (Ljava/lang/Module;Ljava/lang/Module;Ljava/lang/String;Ljava/util/Locale;)Ljava/util/ResourceBundle; 13 <appendix> member <vmtarget> ; # java/util/ResourceBundle$ResourceBundleProviderHelper$$Lambda+0x000002d1100d8cf8
instanceKlass java/util/ResourceBundle$ResourceBundleProviderHelper
instanceKlass java/util/ResourceBundle$3
instanceKlass  @bci java/util/ResourceBundle getLoader (Ljava/lang/Module;)Ljava/lang/ClassLoader; 6 <appendix> member <vmtarget> ; # java/util/ResourceBundle$$Lambda+0x000002d1100d88b8
instanceKlass java/util/ResourceBundle$CacheKeyReference
instanceKlass java/util/ResourceBundle$CacheKey
instanceKlass sun/util/locale/LocaleObjectCache
instanceKlass java/util/ResourceBundle$Control
instanceKlass java/util/logging/Level$RbAccess
instanceKlass  @bci java/util/logging/LogRecord inferCaller ()V 18 <appendix> member <vmtarget> ; # java/util/logging/LogRecord$$Lambda+0x000002d1100d7e48
instanceKlass java/lang/StackStreamFactory$FrameBuffer
instanceKlass java/lang/StackStreamFactory
instanceKlass  @bci java/util/logging/LogRecord$CallerFinder get ()Ljava/util/Optional; 4 <appendix> member <vmtarget> ; # java/util/logging/LogRecord$CallerFinder$$Lambda+0x000002d1100d66f8
instanceKlass  @bci java/util/logging/LogRecord$CallerFinder <clinit> ()V 0 <appendix> argL0 ; # java/util/logging/LogRecord$CallerFinder$$Lambda+0x000002d1100d60c8
instanceKlass  @bci org/eclipse/osgi/util/NLS <clinit> ()V 7 <bsm> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d110139800
instanceKlass java/util/logging/LogRecord$CallerFinder
instanceKlass java/time/zone/Ser
instanceKlass java/io/Externalizable
instanceKlass java/time/zone/ZoneRulesProvider$1
instanceKlass java/time/zone/ZoneRulesProvider
instanceKlass sun/util/calendar/ZoneInfoFile$ZoneOffsetTransitionRule
instanceKlass jdk/internal/util/ByteArray
instanceKlass sun/util/calendar/ZoneInfoFile$1
instanceKlass sun/util/calendar/ZoneInfoFile
instanceKlass java/util/TimeZone
instanceKlass java/util/logging/LogManager$CloseOnReset
instanceKlass java/util/logging/StreamHandler$1
instanceKlass java/util/logging/Handler$1
instanceKlass java/util/logging/ErrorManager
instanceKlass jdk/internal/logger/SimpleConsoleLogger$Formatting
instanceKlass  @bci java/util/logging/SimpleFormatter <init> ()V 5 <appendix> argL0 ; # java/util/logging/SimpleFormatter$$Lambda+0x000002d1100d3d98
instanceKlass java/util/logging/Formatter
instanceKlass java/time/Clock
instanceKlass java/time/InstantSource
instanceKlass java/util/logging/LogRecord
instanceKlass org/apache/aries/spifly/ProviderServiceFactory
instanceKlass ch/qos/logback/classic/spi/LogbackServiceProvider
instanceKlass org/slf4j/spi/SLF4JServiceProvider
instanceKlass org/eclipse/osgi/internal/framework/EquinoxBundle$1
instanceKlass org/apache/aries/spifly/ProviderBundleTrackerCustomizer$ServiceDetails
instanceKlass org/eclipse/osgi/storage/bundlefile/ZipBundleFile$1
instanceKlass  @bci org/eclipse/osgi/storage/bundlefile/ZipBundleFile getPaths ()Ljava/lang/Iterable; 1 <appendix> member <vmtarget> ; # org/eclipse/osgi/storage/bundlefile/ZipBundleFile$$Lambda+0x000002d110131500
instanceKlass  @bci org/apache/aries/spifly/BaseActivator registerProviderBundle (Ljava/lang/String;Lorg/osgi/framework/Bundle;Ljava/util/Map;)V 33 <appendix> member <vmtarget> ; # org/apache/aries/spifly/BaseActivator$$Lambda+0x000002d11013c210
instanceKlass org/apache/aries/spifly/Pair
instanceKlass  @bci org/apache/aries/spifly/BaseActivator registerProviderBundle (Ljava/lang/String;Lorg/osgi/framework/Bundle;Ljava/util/Map;)V 5 <appendix> argL0 ; # org/apache/aries/spifly/BaseActivator$$Lambda+0x000002d110137ce8
instanceKlass  @bci org/osgi/framework/FrameworkUtil <clinit> ()V 27 <appendix> member <vmtarget> ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000002d1101312d8
instanceKlass org/osgi/framework/connect/FrameworkUtilHelper
instanceKlass  @bci org/osgi/framework/FrameworkUtil <clinit> ()V 8 <appendix> argL0 ; # org/osgi/framework/FrameworkUtil$$Lambda+0x000002d110130ed8
instanceKlass org/osgi/framework/FrameworkUtil
instanceKlass  @bci aQute/bnd/header/Attrs mergeWith (LaQute/bnd/header/Attrs;Z)V 4 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002d110138800
instanceKlass  @bci aQute/bnd/header/Attrs mergeWith (LaQute/bnd/header/Attrs;Z)V 4 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d110138400
instanceKlass  @bci aQute/bnd/header/Attrs mergeWith (LaQute/bnd/header/Attrs;Z)V 4 <appendix> member <vmtarget> ; # aQute/bnd/header/Attrs$$Lambda+0x000002d110137ac0
instanceKlass  @cpi aQute/bnd/header/Attrs 639 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d110138000
instanceKlass  @bci org/apache/aries/spifly/ProviderBundleTrackerCustomizer getFromAutoProviderProperty (Lorg/osgi/framework/Bundle;Ljava/util/Map;)Ljava/util/Map$Entry; 58 <appendix> argL0 ; # org/apache/aries/spifly/ProviderBundleTrackerCustomizer$$Lambda+0x000002d1101378b0
instanceKlass  @bci org/apache/aries/spifly/ProviderBundleTrackerCustomizer getFromAutoProviderProperty (Lorg/osgi/framework/Bundle;Ljava/util/Map;)Ljava/util/Map$Entry; 50 <appendix> member <vmtarget> ; # org/apache/aries/spifly/ProviderBundleTrackerCustomizer$$Lambda+0x000002d110137678
instanceKlass  @bci aQute/bnd/stream/EntryPipeline values ()Ljava/util/stream/Stream; 4 <appendix> argL0 ; # aQute/bnd/stream/EntryPipeline$$Lambda+0x000002d110137448
instanceKlass  @bci aQute/bnd/stream/EntryPipeline filterKey (Ljava/util/function/Predicate;)LaQute/bnd/stream/MapStream; 14 <appendix> member <vmtarget> ; # aQute/bnd/stream/EntryPipeline$$Lambda+0x000002d110137200
instanceKlass  @bci org/apache/aries/spifly/ProviderBundleTrackerCustomizer getFromAutoProviderProperty (Lorg/osgi/framework/Bundle;Ljava/util/Map;)Ljava/util/Map$Entry; 27 <appendix> member <vmtarget> ; # org/apache/aries/spifly/ProviderBundleTrackerCustomizer$$Lambda+0x000002d110136fb8
instanceKlass sun/invoke/util/VerifyAccess$1
instanceKlass aQute/bnd/stream/EntryPipeline
instanceKlass  @bci org/apache/aries/spifly/ProviderBundleTrackerCustomizer getFromAutoProviderProperty (Lorg/osgi/framework/Bundle;Ljava/util/Map;)Ljava/util/Map$Entry; 15 <appendix> argL0 ; # org/apache/aries/spifly/ProviderBundleTrackerCustomizer$$Lambda+0x000002d110136978
instanceKlass  @bci org/apache/aries/spifly/ProviderBundleTrackerCustomizer getFromAutoProviderProperty (Lorg/osgi/framework/Bundle;Ljava/util/Map;)Ljava/util/Map$Entry; 7 <appendix> argL0 ; # org/apache/aries/spifly/ProviderBundleTrackerCustomizer$$Lambda+0x000002d110136748
instanceKlass aQute/bnd/stream/MapStream
instanceKlass org/apache/aries/spifly/SpiFlyConstants
instanceKlass org/apache/aries/spifly/ConsumerHeaderProcessor
instanceKlass  @bci aQute/bnd/header/OSGiHeader parseHeader (Ljava/lang/String;LaQute/service/reporter/Reporter;LaQute/bnd/header/Parameters;)LaQute/bnd/header/Parameters; 437 <appendix> member <vmtarget> ; # aQute/bnd/header/OSGiHeader$$Lambda+0x000002d110135d60
instanceKlass  @bci aQute/bnd/header/Attrs <clinit> ()V 72 <appendix> argL0 ; # aQute/bnd/header/Attrs$$Lambda+0x000002d110135b50
instanceKlass  @bci aQute/bnd/header/Attrs <clinit> ()V 64 <appendix> argL0 ; # aQute/bnd/header/Attrs$$Lambda+0x000002d110135940
instanceKlass  @bci aQute/bnd/header/Attrs <clinit> ()V 56 <appendix> argL0 ; # aQute/bnd/header/Attrs$$Lambda+0x000002d110135730
instanceKlass  @bci aQute/bnd/header/Attrs <clinit> ()V 48 <appendix> argL0 ; # aQute/bnd/header/Attrs$$Lambda+0x000002d110135520
instanceKlass  @bci aQute/bnd/header/Attrs <clinit> ()V 40 <appendix> argL0 ; # aQute/bnd/header/Attrs$$Lambda+0x000002d110135310
instanceKlass  @bci aQute/bnd/header/Attrs <clinit> ()V 32 <appendix> argL0 ; # aQute/bnd/header/Attrs$$Lambda+0x000002d110135100
instanceKlass  @bci aQute/bnd/header/Attrs <clinit> ()V 24 <appendix> argL0 ; # aQute/bnd/header/Attrs$$Lambda+0x000002d110134ef0
instanceKlass  @bci aQute/bnd/header/Attrs <clinit> ()V 16 <appendix> argL0 ; # aQute/bnd/header/Attrs$$Lambda+0x000002d110134ce0
instanceKlass aQute/bnd/header/Attrs$DataType
instanceKlass aQute/bnd/header/Attrs
instanceKlass aQute/libg/qtokens/QuotedTokenizer
instanceKlass  @bci java/util/regex/CharPredicates ASCII_WORD ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x000002d1100d22f8
instanceKlass aQute/bnd/header/OSGiHeader
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$CaseInsentiveEntry
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$EntryIterator
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$KeyIterator
instanceKlass java/util/ResourceBundle$1
instanceKlass jdk/internal/access/JavaUtilResourceBundleAccess
instanceKlass java/util/ResourceBundle
instanceKlass  @bci org/eclipse/osgi/storage/Storage lambda$7 (Ljava/util/List;Ljava/lang/String;)Ljava/util/stream/Stream; 7 <appendix> member <vmtarget> ; # org/eclipse/osgi/storage/Storage$$Lambda+0x000002d11012f000
instanceKlass org/eclipse/osgi/internal/container/InternalUtils$1
instanceKlass  @bci org/eclipse/osgi/storage/Storage findEntries (Ljava/util/List;Ljava/lang/String;Ljava/lang/String;I)Ljava/util/Enumeration; 101 <appendix> argL0 ; # org/eclipse/osgi/storage/Storage$$Lambda+0x000002d11012bae8
instanceKlass  @bci org/eclipse/osgi/storage/Storage findEntries (Ljava/util/List;Ljava/lang/String;Ljava/lang/String;I)Ljava/util/Enumeration; 91 <appendix> member <vmtarget> ; # org/eclipse/osgi/storage/Storage$$Lambda+0x000002d11012b8b0
instanceKlass org/apache/aries/spifly/ProviderBundleTrackerCustomizer
instanceKlass org/osgi/util/tracker/BundleTracker
instanceKlass  @bci org/apache/aries/spifly/BaseActivator start (Lorg/osgi/framework/BundleContext;Ljava/lang/String;)V 46 <appendix> argL0 ; # org/apache/aries/spifly/BaseActivator$$Lambda+0x000002d11012e648
instanceKlass  @bci org/apache/aries/spifly/BaseActivator start (Lorg/osgi/framework/BundleContext;Ljava/lang/String;)V 20 <appendix> argL0 ; # org/apache/aries/spifly/BaseActivator$$Lambda+0x000002d11012e418
instanceKlass aQute/bnd/header/Parameters
instanceKlass  @bci org/eclipse/osgi/internal/weaving/WovenClassImpl notifyWovenClassListeners ()V 1 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/weaving/WovenClassImpl$$Lambda+0x000002d11012b160
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistrationImpl$FrameworkHookRegistration getSafeService (Lorg/eclipse/osgi/internal/framework/BundleContextImpl;Lorg/eclipse/osgi/internal/serviceregistry/ServiceConsumer;)Ljava/lang/Object; 17 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/serviceregistry/ServiceRegistrationImpl$FrameworkHookRegistration$$Lambda+0x000002d11012af18
instanceKlass org/apache/aries/spifly/Util
instanceKlass org/objectweb/asm/Opcodes
instanceKlass org/objectweb/asm/ClassVisitor
instanceKlass org/apache/aries/spifly/dynamic/ClientWeavingHook
instanceKlass java/util/logging/Logger$SystemLoggerHelper$1
instanceKlass java/util/logging/Logger$SystemLoggerHelper
instanceKlass java/util/logging/LogManager$4
instanceKlass jdk/internal/logger/BootstrapLogger$BootstrapExecutors
instanceKlass jdk/internal/logger/LoggerFinderLoader
instanceKlass  @bci java/lang/System$LoggerFinder accessProvider ()Ljava/lang/System$LoggerFinder; 8 <appendix> argL0 ; # java/lang/System$LoggerFinder$$Lambda+0x000002d1100d0f08
instanceKlass  @bci java/util/logging/Level$KnownLevel findByName (Ljava/lang/String;Ljava/util/function/Function;)Ljava/util/Optional; 29 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000023
instanceKlass  @bci java/util/logging/Level findLevel (Ljava/lang/String;)Ljava/util/logging/Level; 13 <appendix> argL0 ; # java/util/logging/Level$$Lambda+0x800000011
instanceKlass java/util/logging/LogManager$LoggerContext$1
instanceKlass java/util/logging/LogManager$VisitedLoggers
instanceKlass java/util/logging/LogManager$2
instanceKlass java/util/logging/LogManager$LoggingProviderAccess
instanceKlass java/util/logging/LogManager$LogNode
instanceKlass java/util/logging/LogManager$LoggerContext
instanceKlass java/util/logging/LogManager$1
instanceKlass java/util/logging/LogManager
instanceKlass java/util/logging/Logger$ConfigurationData
instanceKlass java/util/logging/Logger$LoggerBundle
instanceKlass  @bci java/util/logging/Level$KnownLevel add (Ljava/util/logging/Level;)V 49 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000022
instanceKlass  @bci java/util/logging/Level$KnownLevel add (Ljava/util/logging/Level;)V 19 <appendix> argL0 ; # java/util/logging/Level$KnownLevel$$Lambda+0x800000021
instanceKlass java/util/logging/Level
instanceKlass java/util/logging/Handler
instanceKlass java/util/logging/Logger
instanceKlass org/osgi/util/tracker/BundleTrackerCustomizer
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult
instanceKlass org/apache/aries/spifly/BaseActivator
instanceKlass org/eclipse/osgi/internal/weaving/WeavingHookConfigurator$WovenClassContext
instanceKlass org/eclipse/osgi/internal/weaving/WovenClassImpl
instanceKlass org/osgi/framework/hooks/weaving/WovenClass
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathManager$DefineContext
instanceKlass org/eclipse/osgi/internal/loader/BundleLoader$3
instanceKlass org/eclipse/osgi/container/ModuleContainer$ContainerStartLevel$2
instanceKlass java/util/concurrent/CountDownLatch
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainerAdaptor$1$1
instanceKlass org/eclipse/osgi/internal/resolver/StateImpl
instanceKlass org/eclipse/osgi/service/resolver/BundleDescription
instanceKlass org/eclipse/osgi/service/resolver/HostSpecification
instanceKlass org/eclipse/osgi/service/resolver/GenericDescription
instanceKlass org/eclipse/osgi/service/resolver/GenericSpecification
instanceKlass org/eclipse/osgi/service/resolver/BundleSpecification
instanceKlass org/eclipse/osgi/service/resolver/NativeCodeSpecification
instanceKlass org/eclipse/osgi/service/resolver/ExportPackageDescription
instanceKlass org/eclipse/osgi/service/resolver/ImportPackageSpecification
instanceKlass org/eclipse/osgi/service/resolver/VersionConstraint
instanceKlass org/eclipse/osgi/service/resolver/NativeCodeDescription
instanceKlass org/eclipse/osgi/service/resolver/BaseDescription
instanceKlass org/eclipse/osgi/internal/resolver/StateObjectFactoryImpl
instanceKlass org/eclipse/osgi/service/resolver/Resolver
instanceKlass org/eclipse/osgi/service/resolver/State
instanceKlass org/eclipse/osgi/service/resolver/StateObjectFactory
instanceKlass org/eclipse/osgi/compatibility/state/PlatformAdminImpl
instanceKlass org/eclipse/osgi/service/resolver/PlatformAdmin
instanceKlass org/eclipse/osgi/compatibility/state/Activator
instanceKlass org/eclipse/osgi/internal/url/URLStreamHandlerProxy$1
instanceKlass java/util/Formatter$Flags
instanceKlass java/util/Formattable
instanceKlass java/util/Formatter$FormatSpecifier
instanceKlass java/util/Formatter$Conversion
instanceKlass java/util/Formatter$FixedString
instanceKlass java/util/Formatter$FormatString
instanceKlass  @bci java/util/regex/Pattern union (Ljava/util/regex/Pattern$CharPredicate;Ljava/util/regex/Pattern$CharPredicate;Z)Ljava/util/regex/Pattern$CharPredicate; 6 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000032
instanceKlass  @bci java/util/regex/Pattern Range (II)Ljava/util/regex/Pattern$CharPredicate; 23 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x80000002a
instanceKlass java/util/Formatter
instanceKlass org/eclipse/osgi/internal/url/URLStreamHandlerSetter
instanceKlass org/eclipse/osgi/internal/url/NullURLStreamHandlerService
instanceKlass org/osgi/service/url/URLStreamHandlerSetter
instanceKlass org/osgi/service/url/URLStreamHandlerService
instanceKlass  @bci org/eclipse/osgi/internal/url/URLStreamHandlerFactoryImpl createInternalURLStreamHandler (Ljava/lang/String;)Ljava/net/URLStreamHandler; 18 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/url/URLStreamHandlerFactoryImpl$$Lambda+0x000002d1101271a8
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110121400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110121000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110120c00
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$3
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$7
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver removeSubstitutedCapabilities (Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;Lorg/eclipse/osgi/internal/container/NamespaceList;)Ljava/util/Collection; 60 <appendix> member <vmtarget> ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x000002d110126ad8
instanceKlass org/apache/felix/resolver/util/ArrayMap$1$1
instanceKlass org/apache/felix/resolver/ResolverImpl$UsedBlames
instanceKlass java/util/AbstractList$Itr
instanceKlass org/apache/felix/resolver/util/CopyOnWriteSet$1
instanceKlass org/apache/felix/resolver/WrappedCapability
instanceKlass org/eclipse/osgi/container/ModuleResolutionReport$EntryImpl
instanceKlass org/eclipse/osgi/report/resolution/ResolutionReport$Entry
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver addPayloadContent (Ljava/util/List;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;)V 74 <appendix> argL0 ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x000002d110124900
instanceKlass java/util/function/BiPredicate
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver addPayloadContent (Ljava/util/List;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;)V 69 <appendix> argL0 ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x000002d1101246c0
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver addPayloadContent (Ljava/util/List;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;)V 64 <appendix> argL0 ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x000002d110124480
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver addPayloadContent (Ljava/util/List;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;)V 44 <appendix> argL0 ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x000002d110124240
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver addPayloadContent (Ljava/util/List;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;)V 39 <appendix> argL0 ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x000002d110124000
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver removePayloadContent (Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;)V 10 <appendix> argL0 ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x000002d11011fc28
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$5
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver removePayloadContent (Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;)V 1 <appendix> argL0 ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x000002d11011f790
instanceKlass org/eclipse/osgi/container/ModuleResolver$ResolveProcess$DynamicFragments
instanceKlass java/util/Collections$ReverseComparator2
instanceKlass java/util/Collections$ReverseComparator
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver$ResolveProcess resolveNonPayLoadFragments ()Ljava/util/Map; 84 <appendix> argL0 ; # org/eclipse/osgi/container/ModuleResolver$ResolveProcess$$Lambda+0x000002d11011f350
instanceKlass  @bci org/eclipse/osgi/internal/framework/OSGiFrameworkHooks$CoreResolverHookFactory getHookReferences (Lorg/eclipse/osgi/internal/serviceregistry/ServiceRegistry;Lorg/eclipse/osgi/internal/framework/BundleContextImpl;)[Lorg/eclipse/osgi/internal/serviceregistry/ServiceReferenceImpl; 2 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/framework/OSGiFrameworkHooks$CoreResolverHookFactory$$Lambda+0x000002d11011f138
instanceKlass org/eclipse/osgi/container/ModuleDatabase$2
instanceKlass org/eclipse/osgi/internal/container/ComputeNodeOrder$Digraph$Vertex
instanceKlass org/eclipse/osgi/internal/container/ComputeNodeOrder$Digraph
instanceKlass org/eclipse/osgi/internal/container/ComputeNodeOrder
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110120800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110120400
instanceKlass  @cpi java/util/logging/LogRecord 420 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d110120000
instanceKlass org/eclipse/osgi/container/ModuleContainer$ContainerWiring$1
instanceKlass org/osgi/dto/DTO
instanceKlass org/eclipse/osgi/container/builders/OSGiManifestBuilderFactory$NativeClause
instanceKlass org/eclipse/osgi/storage/ManifestLocalization$BundleResourceBundle
instanceKlass org/eclipse/osgi/storage/ManifestLocalization
instanceKlass org/eclipse/osgi/framework/eventmgr/EventManager$EventThread$Queued
instanceKlass  @bci org/eclipse/osgi/framework/eventmgr/EventManager getEventThread ()Lorg/eclipse/osgi/framework/eventmgr/EventManager$EventThread; 24 <appendix> member <vmtarget> ; # org/eclipse/osgi/framework/eventmgr/EventManager$$Lambda+0x000002d11011b4e8
instanceKlass  @bci org/eclipse/osgi/internal/framework/EquinoxEventPublisher notifyEventHooksPrivileged (Lorg/osgi/framework/BundleEvent;Ljava/util/Collection;)V 98 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/framework/EquinoxEventPublisher$$Lambda+0x000002d11011afa0
instanceKlass org/osgi/framework/VersionRange
instanceKlass  @bci org/eclipse/osgi/internal/framework/BundleContextImpl notifyFindHooksPriviledged (Lorg/eclipse/osgi/internal/framework/BundleContextImpl;Ljava/util/Collection;)V 73 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/framework/BundleContextImpl$$Lambda+0x000002d11011ab30
instanceKlass org/eclipse/osgi/framework/util/FilePath
instanceKlass org/eclipse/core/runtime/internal/adaptor/ConsoleManager
instanceKlass org/eclipse/core/runtime/internal/adaptor/DefaultStartupMonitor
instanceKlass org/eclipse/osgi/service/runnable/StartupMonitor
instanceKlass java/lang/Thread$Builder$OfVirtual
instanceKlass java/lang/Thread$Builder$OfPlatform
instanceKlass java/lang/Thread$Builder
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceFactoryUse$1
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceConsumer$2
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceConsumer$1
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceConsumer
instanceKlass org/eclipse/osgi/service/security/TrustEngine
instanceKlass org/eclipse/osgi/internal/signedcontent/SignedContentConstants
instanceKlass org/eclipse/osgi/internal/signedcontent/SignedBundleHook$1
instanceKlass org/eclipse/osgi/internal/framework/XMLParsingServiceFactory
instanceKlass org/eclipse/osgi/storage/BundleLocalizationImpl
instanceKlass org/eclipse/osgi/service/localization/BundleLocalization
instanceKlass org/eclipse/osgi/storage/url/BundleURLConverter
instanceKlass org/eclipse/osgi/service/urlconversion/URLConverter
instanceKlass org/eclipse/osgi/internal/framework/legacy/StartLevelImpl
instanceKlass org/eclipse/osgi/internal/framework/legacy/PackageAdminImpl
instanceKlass org/osgi/service/condition/ConditionImpl
instanceKlass org/osgi/service/condition/Condition
instanceKlass java/net/ContentHandler
instanceKlass java/net/ContentHandlerFactory
instanceKlass org/eclipse/osgi/internal/url/EquinoxFactoryManager
instanceKlass org/eclipse/osgi/internal/log/ConfigAdminListener
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyFindHooksPrivileged (Lorg/eclipse/osgi/internal/framework/BundleContextImpl;Ljava/lang/String;Ljava/lang/String;ZLjava/util/Collection;)V 101 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002d110111400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002d110111000
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyFindHooksPrivileged (Lorg/eclipse/osgi/internal/framework/BundleContextImpl;Ljava/lang/String;Ljava/lang/String;ZLjava/util/Collection;)V 101 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d110110c00
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyFindHooksPrivileged (Lorg/eclipse/osgi/internal/framework/BundleContextImpl;Ljava/lang/String;Ljava/lang/String;ZLjava/util/Collection;)V 101 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$$Lambda+0x000002d110117d40
instanceKlass  @cpi org/eclipse/osgi/internal/serviceregistry/ServiceRegistry 1105 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d110110800
instanceKlass org/eclipse/osgi/internal/serviceregistry/ShrinkableCollection
instanceKlass org/osgi/util/tracker/AbstractTracked
instanceKlass org/osgi/util/tracker/ServiceTracker
instanceKlass org/eclipse/osgi/internal/log/EventAdminAdapter
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$2
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap$Snapshot$SnapshotIterator
instanceKlass org/eclipse/osgi/framework/eventmgr/ListenerQueue
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyEventListenerHooksPrivileged (Lorg/osgi/framework/ServiceEvent;Ljava/util/Map;)V 77 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$$Lambda+0x000002d110116568
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyEventHooksPrivileged (Lorg/osgi/framework/ServiceEvent;Ljava/util/Collection;)V 77 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$$Lambda+0x000002d110115fa0
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap$Snapshot
instanceKlass org/osgi/framework/PrototypeServiceFactory
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceReferenceImpl
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceUse
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyListenerHooksPrivileged (Ljava/util/Collection;Z)V 106 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d110110400
instanceKlass  @bci org/eclipse/osgi/internal/serviceregistry/ServiceRegistry notifyListenerHooksPrivileged (Ljava/util/Collection;Z)V 106 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$$Lambda+0x000002d110114000
instanceKlass  @cpi org/eclipse/osgi/internal/serviceregistry/ServiceRegistry 1128 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d110110000
instanceKlass org/eclipse/osgi/internal/serviceregistry/HookContext
instanceKlass org/osgi/framework/UnfilteredServiceListener
instanceKlass org/eclipse/osgi/internal/serviceregistry/FilteredServiceListener
instanceKlass org/osgi/framework/hooks/service/ListenerHook$ListenerInfo
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap$Entry
instanceKlass org/eclipse/osgi/framework/eventmgr/CopyOnWriteIdentityMap
instanceKlass org/eclipse/osgi/internal/log/OrderedExecutor
instanceKlass org/eclipse/osgi/internal/framework/BundleContextImpl$2
instanceKlass org/osgi/service/startlevel/StartLevel
instanceKlass org/osgi/service/packageadmin/PackageAdmin
instanceKlass org/eclipse/osgi/internal/framework/SystemBundleActivator
instanceKlass org/eclipse/osgi/internal/loader/classpath/TitleVersionVendor
instanceKlass org/eclipse/osgi/internal/loader/classpath/ManifestPackageAttributes
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathEntry$PDEData
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathEntry
instanceKlass org/eclipse/osgi/internal/loader/classpath/FragmentClasspath
instanceKlass org/eclipse/osgi/internal/loader/classpath/ClasspathManager
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader$ClassNameLock$1
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader$ClassNameLock
instanceKlass org/eclipse/osgi/internal/container/KeyBasedLockStore
instanceKlass org/eclipse/osgi/internal/loader/BundleLoaderSources
instanceKlass org/eclipse/osgi/internal/loader/BundleLoader$1
instanceKlass org/eclipse/osgi/internal/loader/sources/PackageSource
instanceKlass java/lang/ApplicationShutdownHooks$1
instanceKlass java/lang/ApplicationShutdownHooks
instanceKlass org/eclipse/osgi/internal/framework/StorageSaver$StorageSaverTask
instanceKlass org/eclipse/osgi/internal/framework/StorageSaver
instanceKlass java/util/concurrent/RunnableScheduledFuture
instanceKlass java/util/concurrent/ScheduledFuture
instanceKlass java/util/concurrent/Delayed
instanceKlass java/util/concurrent/ScheduledExecutorService
instanceKlass org/eclipse/osgi/internal/framework/ContextFinder$1
instanceKlass org/osgi/framework/ServiceObjects
instanceKlass org/eclipse/osgi/internal/framework/BundleContextImpl
instanceKlass org/osgi/framework/hooks/weaving/WovenClassListener
instanceKlass org/osgi/framework/hooks/weaving/WeavingHook
instanceKlass org/osgi/framework/hooks/service/FindHook
instanceKlass org/osgi/framework/hooks/service/EventListenerHook
instanceKlass org/osgi/framework/hooks/service/EventHook
instanceKlass org/osgi/framework/hooks/bundle/FindHook
instanceKlass org/osgi/framework/hooks/bundle/EventHook
instanceKlass org/osgi/framework/hooks/bundle/CollisionHook
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceRegistry$FrameworkHookHolder
instanceKlass org/osgi/framework/hooks/service/ListenerHook
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceRegistrationImpl
instanceKlass org/osgi/framework/ServiceRegistration
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceRegistry
instanceKlass org/eclipse/osgi/framework/eventmgr/EventManager
instanceKlass org/eclipse/osgi/internal/framework/EquinoxEventPublisher
instanceKlass org/eclipse/osgi/container/ModuleResolutionReport
instanceKlass org/eclipse/osgi/container/ModuleWiring$LoaderInitializer
instanceKlass org/eclipse/osgi/container/ModuleWiring
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$1
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver removeNonEffectiveRequirements (Lorg/eclipse/osgi/internal/container/NamespaceList$Builder;Lorg/eclipse/osgi/internal/container/NamespaceList;)V 57 <appendix> member <vmtarget> ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x000002d1101069e0
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$6
instanceKlass org/eclipse/osgi/container/ModuleWire
instanceKlass org/osgi/framework/wiring/BundleWire
instanceKlass org/apache/felix/resolver/WrappedRequirement
instanceKlass org/apache/felix/resolver/WireImpl
instanceKlass org/apache/felix/resolver/ResolverImpl$6
instanceKlass org/apache/felix/resolver/ResolverImpl$5
instanceKlass org/apache/felix/resolver/ResolverImpl$4
instanceKlass org/apache/felix/resolver/ResolverImpl$Blame
instanceKlass org/apache/felix/resolver/ResolverImpl$3
instanceKlass org/apache/felix/resolver/ResolverImpl$Packages
instanceKlass jdk/internal/util/random/RandomSupport
instanceKlass org/apache/felix/resolver/ResolverImpl$WireCandidate
instanceKlass jdk/internal/vm/ThreadContainers
instanceKlass jdk/internal/vm/StackableScope
instanceKlass java/util/concurrent/ThreadPoolExecutor$AbortPolicy
instanceKlass java/util/concurrent/AbstractExecutorService
instanceKlass java/util/concurrent/ExecutorService
instanceKlass java/util/concurrent/ThreadPoolExecutor$CallerRunsPolicy
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainerAdaptor$1$2
instanceKlass java/util/concurrent/Executors$RunnableAdapter
instanceKlass java/util/concurrent/Executors
instanceKlass org/apache/felix/resolver/ResolverImpl$EnhancedExecutor$1
instanceKlass java/util/concurrent/FutureTask$WaitNode
instanceKlass java/util/concurrent/FutureTask
instanceKlass java/util/concurrent/RunnableFuture
instanceKlass java/util/concurrent/Future
instanceKlass org/apache/felix/resolver/ResolverImpl$1Computer
instanceKlass java/util/concurrent/ConcurrentLinkedQueue$Node
instanceKlass org/apache/felix/resolver/ResolverImpl$EnhancedExecutor
instanceKlass org/apache/felix/resolver/WrappedResource
instanceKlass java/util/ArrayList$SubList$1
instanceKlass org/apache/felix/resolver/util/OpenHashMap$1
instanceKlass org/apache/felix/resolver/util/CopyOnWriteSet
instanceKlass org/apache/felix/resolver/util/CandidateSelector
instanceKlass org/apache/felix/resolver/util/OpenHashMap$MapEntry
instanceKlass org/apache/felix/resolver/util/OpenHashMap$MapIterator
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver$ResolveProcess removeSubstituted (Ljava/util/List;)V 2 <appendix> member <vmtarget> ; # org/eclipse/osgi/container/ModuleResolver$ResolveProcess$$Lambda+0x000002d1100bfba0
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver removeNonEffectiveCapabilities (Ljava/util/Collection;)V 2 <appendix> member <vmtarget> ; # org/eclipse/osgi/container/ModuleResolver$$Lambda+0x000002d1100bf958
instanceKlass  @bci org/eclipse/osgi/container/ModuleResolver$ResolveProcess filterDisabled (Ljava/util/List;)V 2 <appendix> member <vmtarget> ; # org/eclipse/osgi/container/ModuleResolver$ResolveProcess$$Lambda+0x000002d1100bf710
instanceKlass  @bci org/eclipse/osgi/internal/framework/FilterImpl$Equal compare_Version (Lorg/osgi/framework/Version;)Z 3 <appendix> argL0 ; # org/eclipse/osgi/internal/framework/FilterImpl$Equal$$Lambda+0x000002d1100bf4e0
instanceKlass org/eclipse/osgi/internal/framework/FilterImpl$Parser
instanceKlass org/eclipse/osgi/internal/framework/FilterImpl
instanceKlass org/apache/felix/resolver/Candidates$PopulateResult
instanceKlass org/osgi/service/resolver/HostedCapability
instanceKlass org/apache/felix/resolver/Candidates
instanceKlass org/apache/felix/resolver/Util
instanceKlass org/apache/felix/resolver/ResolverImpl$ResolveSession
instanceKlass org/osgi/resource/Wire
instanceKlass org/apache/felix/resolver/util/OpenHashMap
instanceKlass org/apache/felix/resolver/ResolutionError
instanceKlass org/apache/felix/resolver/ResolverImpl
instanceKlass org/osgi/service/resolver/Resolver
instanceKlass java/util/LinkedList$ListItr
instanceKlass java/util/LinkedList$Node
instanceKlass org/eclipse/osgi/internal/framework/OSGiFrameworkHooks$CoreResolverHookFactory$CoreResolverHook
instanceKlass org/eclipse/osgi/report/resolution/ResolutionReport$Listener
instanceKlass org/eclipse/osgi/container/ModuleResolutionReport$Builder
instanceKlass org/apache/felix/resolver/Logger
instanceKlass org/osgi/service/resolver/ResolveContext
instanceKlass org/eclipse/osgi/container/ModuleDatabase$1
instanceKlass org/eclipse/osgi/container/ModuleContainer$ResolutionLock$Permits
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntry
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1
instanceKlass org/eclipse/osgi/internal/container/Capabilities$NamespaceSet
instanceKlass org/eclipse/osgi/container/ModuleRevision$2
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$8
instanceKlass java/util/AbstractMap$SimpleImmutableEntry
instanceKlass org/eclipse/osgi/container/ModuleCapability
instanceKlass org/osgi/framework/wiring/BundleCapability
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$3
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$2
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$1
instanceKlass org/eclipse/osgi/internal/container/NamespaceList
instanceKlass org/eclipse/osgi/container/ModuleRevision$1
instanceKlass org/osgi/framework/wiring/BundleWiring
instanceKlass org/osgi/resource/Wiring
instanceKlass org/eclipse/osgi/container/ModuleRevision
instanceKlass org/eclipse/osgi/container/ModuleRevisions
instanceKlass org/osgi/framework/wiring/BundleRevisions
instanceKlass org/lombokweb/asm/Opcodes
instanceKlass org/lombokweb/asm/Handler
instanceKlass lombok/patcher/MethodLogistics
instanceKlass org/lombokweb/asm/Label
instanceKlass org/lombokweb/asm/Type
instanceKlass org/lombokweb/asm/Frame
instanceKlass org/lombokweb/asm/Context
instanceKlass org/lombokweb/asm/Attribute
instanceKlass lombok/patcher/scripts/ExitFromMethodEarlyScript$1
instanceKlass org/lombokweb/asm/ByteVector
instanceKlass org/lombokweb/asm/Symbol
instanceKlass org/lombokweb/asm/SymbolTable
instanceKlass org/lombokweb/asm/FieldVisitor
instanceKlass org/lombokweb/asm/MethodVisitor
instanceKlass org/lombokweb/asm/AnnotationVisitor
instanceKlass org/lombokweb/asm/ModuleVisitor
instanceKlass org/lombokweb/asm/RecordComponentVisitor
instanceKlass org/lombokweb/asm/ClassReader
instanceKlass org/eclipse/osgi/internal/framework/EquinoxBundle
instanceKlass org/osgi/resource/Capability
instanceKlass org/eclipse/osgi/container/ModuleRequirement
instanceKlass org/osgi/framework/wiring/BundleRequirement
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder$2
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$CaseInsensitiveKey
instanceKlass org/eclipse/osgi/storage/bundlefile/BundleEntry
instanceKlass org/eclipse/osgi/container/ModuleRevisionBuilder$GenericInfo$1
instanceKlass org/eclipse/osgi/container/ModuleRevisionBuilder$GenericInfo
instanceKlass org/eclipse/osgi/container/ModuleRevisionBuilder
instanceKlass org/eclipse/osgi/container/builders/OSGiManifestBuilderFactory
instanceKlass java/util/ComparableTimSort
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d1100ad000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d1100acc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d1100ac800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d1100ac400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002d1100ac000
instanceKlass java/lang/reflect/AnnotatedType
instanceKlass java/lang/reflect/TypeVariable
instanceKlass org/eclipse/osgi/storage/BundleInfo$Generation
instanceKlass org/eclipse/osgi/internal/container/LockSet$LockHolder
instanceKlass org/eclipse/osgi/storage/BundleInfo
instanceKlass org/eclipse/osgi/container/ModuleContainer$ContainerStartLevel
instanceKlass org/eclipse/osgi/container/ModuleContainer$ContainerWiring
instanceKlass org/eclipse/osgi/container/ModuleResolver
instanceKlass org/eclipse/osgi/container/ModuleContainer$ResolutionLock
instanceKlass org/eclipse/osgi/internal/container/LockSet
instanceKlass org/osgi/framework/wiring/FrameworkWiring
instanceKlass org/osgi/resource/Requirement
instanceKlass org/osgi/framework/startlevel/FrameworkStartLevel
instanceKlass org/eclipse/osgi/report/resolution/ResolutionReport
instanceKlass org/eclipse/osgi/container/ModuleContainer
instanceKlass org/eclipse/osgi/internal/container/Capabilities
instanceKlass org/eclipse/osgi/container/Module
instanceKlass org/osgi/framework/startlevel/BundleStartLevel
instanceKlass org/eclipse/osgi/container/ModuleDatabase
instanceKlass java/util/concurrent/LinkedBlockingQueue$Node
instanceKlass java/util/concurrent/RejectedExecutionHandler
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainerAdaptor$1
instanceKlass java/util/concurrent/LinkedTransferQueue$DualNode
instanceKlass java/util/concurrent/TransferQueue
instanceKlass java/util/concurrent/atomic/AtomicReference
instanceKlass org/eclipse/osgi/internal/container/AtomicLazyInitializer
instanceKlass org/eclipse/osgi/internal/framework/OSGiFrameworkHooks$BundleCollisionHook
instanceKlass org/osgi/framework/ServiceReference
instanceKlass org/osgi/framework/hooks/resolver/ResolverHook
instanceKlass org/eclipse/osgi/internal/framework/OSGiFrameworkHooks$CoreResolverHookFactory
instanceKlass org/osgi/framework/hooks/resolver/ResolverHookFactory
instanceKlass org/eclipse/osgi/container/ModuleCollisionHook
instanceKlass org/eclipse/osgi/internal/framework/OSGiFrameworkHooks
instanceKlass org/eclipse/osgi/container/ModuleContainerAdaptor$1
instanceKlass org/eclipse/osgi/container/ModuleLoader
instanceKlass java/util/concurrent/Callable
instanceKlass java/util/concurrent/BlockingQueue
instanceKlass java/util/concurrent/Executor
instanceKlass org/eclipse/osgi/internal/permadmin/SecurityRow
instanceKlass org/eclipse/osgi/internal/permadmin/PermissionAdminTable
instanceKlass org/osgi/service/permissionadmin/PermissionInfo
instanceKlass org/osgi/service/condpermadmin/ConditionalPermissionUpdate
instanceKlass org/osgi/service/condpermadmin/ConditionalPermissionInfo
instanceKlass org/eclipse/osgi/internal/permadmin/SecurityAdmin
instanceKlass org/osgi/service/condpermadmin/ConditionalPermissionAdmin
instanceKlass org/osgi/service/permissionadmin/PermissionAdmin
instanceKlass org/eclipse/osgi/storage/PermissionData
instanceKlass java/lang/Shutdown$Lock
instanceKlass java/lang/Shutdown
instanceKlass java/io/DeleteOnExitHook$1
instanceKlass java/io/DeleteOnExitHook
instanceKlass  @bci org/eclipse/osgi/framework/internal/reliablefile/ReliableFile createTempFile (Ljava/lang/String;Ljava/lang/String;Ljava/io/File;)Ljava/io/File; 61 <appendix> argL0 ; # org/eclipse/osgi/framework/internal/reliablefile/ReliableFile$$Lambda+0x000002d11009b750
instanceKlass org/eclipse/osgi/framework/internal/reliablefile/ReliableFile
instanceKlass sun/nio/ch/FileKey
instanceKlass sun/nio/ch/FileLockTable
instanceKlass java/nio/channels/FileLock
instanceKlass org/eclipse/osgi/internal/location/Locker_JavaNio
instanceKlass org/eclipse/osgi/storagemanager/StorageManager
instanceKlass java/util/HashMap$HashMapSpliterator
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 92 <appendix> member <vmtarget> ; # java/lang/SecurityManager$$Lambda+0x000002d1100c0698
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 76 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x000002d1100c0468
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 66 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x000002d1100c0228
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 47 <appendix> member <vmtarget> ; # java/lang/SecurityManager$$Lambda+0x000002d1100c0000
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 31 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x000002d11003f858
instanceKlass  @bci java/lang/SecurityManager nonExportedPkgs (Ljava/lang/module/ModuleDescriptor;)Ljava/util/Set; 21 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x000002d11003f618
instanceKlass  @cpi java/lang/SecurityManager 539 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d1100a3000
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 59 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x000002d11003f3f8
instanceKlass  @cpi java/lang/SecurityManager 535 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d1100a2c00
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 49 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x000002d11003f1c8
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 39 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x000002d11003ef98
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 29 <appendix> member <vmtarget> ; # java/lang/SecurityManager$$Lambda+0x000002d11003ed50
instanceKlass  @cpi java/lang/SecurityManager 518 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d1100a2800
instanceKlass  @bci java/lang/SecurityManager addNonExportedPackages (Ljava/lang/ModuleLayer;)V 17 <appendix> argL0 ; # java/lang/SecurityManager$$Lambda+0x000002d11003eb20
# instanceKlass java/net/SetAccessible+0x000002d1100a2400
instanceKlass jdk/internal/org/objectweb/asm/ClassReader
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d1100a2000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d1100a1c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d1100a1800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d1100a1400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002d1100a1000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002d1100a0c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d1100a0800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d1100a0400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002d1100a0000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110099c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002d110099800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110099400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110099000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110098c00
instanceKlass jdk/internal/vm/annotation/ForceInline
instanceKlass java/lang/annotation/Documented
instanceKlass java/lang/Deprecated
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 22 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000043
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 17 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000041
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 12 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x80000003e
instanceKlass  @bci java/util/stream/Collectors joining (Ljava/lang/CharSequence;Ljava/lang/CharSequence;Ljava/lang/CharSequence;)Ljava/util/stream/Collector; 7 <appendix> member <vmtarget> ; # java/util/stream/Collectors$$Lambda+0x800000046
instanceKlass  @bci java/lang/Class methodToString (Ljava/lang/String;[Ljava/lang/Class;)Ljava/lang/String; 42 <appendix> argL0 ; # java/lang/Class$$Lambda+0x000002d11003dbf0
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110098800
instanceKlass sun/misc/Unsafe
instanceKlass org/eclipse/osgi/internal/url/MultiplexingFactory
instanceKlass org/eclipse/osgi/storage/FrameworkExtensionInstaller
instanceKlass org/eclipse/osgi/storage/bundlefile/MRUBundleFileList
instanceKlass org/eclipse/osgi/framework/eventmgr/EventDispatcher
instanceKlass org/osgi/framework/Filter
instanceKlass org/eclipse/osgi/storage/ContentProvider
instanceKlass org/eclipse/osgi/storage/bundlefile/BundleFile
instanceKlass org/eclipse/osgi/container/ModuleContainerAdaptor
instanceKlass org/eclipse/osgi/storage/Storage
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002d110098400
instanceKlass java/lang/invoke/MethodHandle$1
instanceKlass  @bci org/eclipse/osgi/internal/cds/CDSHookConfigurator addHooks (Lorg/eclipse/osgi/internal/hookregistry/HookRegistry;)V 77 <appendix> argL0 ; # org/eclipse/osgi/internal/cds/CDSHookConfigurator$$Lambda+0x000002d11009d190
instanceKlass org/eclipse/osgi/signedcontent/SignedContent
instanceKlass org/eclipse/osgi/internal/hookregistry/StorageHookFactory$StorageHook
instanceKlass org/eclipse/osgi/internal/hookregistry/StorageHookFactory
instanceKlass org/osgi/framework/BundleActivator
instanceKlass org/eclipse/osgi/internal/cds/CDSHookConfigurator
instanceKlass org/eclipse/osgi/internal/signedcontent/SignedBundleHook
instanceKlass org/eclipse/osgi/signedcontent/SignedContentFactory
instanceKlass org/eclipse/osgi/internal/hookregistry/ActivatorHookFactory
instanceKlass org/osgi/framework/wiring/BundleRevision
instanceKlass org/osgi/resource/Resource
instanceKlass org/eclipse/osgi/internal/connect/ConnectHookConfigurator
instanceKlass org/eclipse/osgi/internal/hookregistry/HookConfigurator
instanceKlass java/net/URLClassLoader$3$1
instanceKlass java/net/URLClassLoader$3
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainer$ConnectModules
instanceKlass  @bci org/eclipse/osgi/internal/log/ExtendedLogServiceImpl applyLogLevels (Lorg/eclipse/osgi/internal/log/ExtendedLogServiceFactory$EquinoxLoggerContext;)V 20 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/log/ExtendedLogServiceImpl$$Lambda+0x000002d1100968d0
instanceKlass  @bci org/eclipse/osgi/internal/log/ExtendedLogServiceImpl applyLogLevels (Lorg/eclipse/osgi/internal/log/ExtendedLogServiceFactory$EquinoxLoggerContext;)V 5 <appendix> member <vmtarget> ; # org/eclipse/osgi/internal/log/ExtendedLogServiceImpl$$Lambda+0x000002d1100966a8
instanceKlass  @cpi org/eclipse/osgi/internal/log/ExtendedLogServiceImpl 376 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d110098000
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceFactory$EquinoxLoggerContext
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogFactory$1
instanceKlass org/eclipse/osgi/framework/log/FrameworkLog
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogFactory
instanceKlass org/osgi/service/log/FormatterLogger
instanceKlass org/eclipse/osgi/internal/log/LoggerImpl
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceImpl
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceFactory$EquinoxLoggerAdmin
instanceKlass org/osgi/service/log/admin/LoggerContext
instanceKlass org/eclipse/osgi/internal/log/LoggerContextTargetMap
instanceKlass org/eclipse/osgi/internal/log/LogServiceManager$MockSystemBundle
instanceKlass org/osgi/service/log/admin/LoggerAdmin
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogServiceFactory
instanceKlass org/eclipse/osgi/framework/util/ArrayMap
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$WriteLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$ReadLock
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock
instanceKlass java/util/concurrent/locks/ReadWriteLock
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogReaderServiceFactory$1
instanceKlass org/osgi/service/log/LogEntry
instanceKlass org/eclipse/osgi/internal/log/ExtendedLogReaderServiceFactory
instanceKlass org/osgi/framework/ServiceFactory
instanceKlass org/eclipse/equinox/log/ExtendedLogReaderService
instanceKlass org/osgi/service/log/LogReaderService
instanceKlass org/eclipse/equinox/log/ExtendedLogService
instanceKlass org/eclipse/equinox/log/Logger
instanceKlass org/osgi/service/log/Logger
instanceKlass org/osgi/service/log/LogService
instanceKlass org/osgi/service/log/LoggerFactory
instanceKlass org/eclipse/osgi/internal/log/LogServiceManager
instanceKlass org/osgi/framework/AllServiceListener
instanceKlass org/osgi/framework/ServiceListener
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogWriter
instanceKlass org/eclipse/equinox/log/LogFilter
instanceKlass org/eclipse/equinox/log/SynchronousLogListener
instanceKlass org/osgi/service/log/LogListener
instanceKlass org/eclipse/osgi/internal/log/EquinoxLogServices
instanceKlass org/eclipse/osgi/util/ManifestElement
instanceKlass org/eclipse/osgi/internal/debug/Debug
instanceKlass org/eclipse/osgi/service/debug/DebugOptionsListener
instanceKlass org/eclipse/osgi/service/debug/DebugTrace
instanceKlass org/eclipse/osgi/internal/debug/FrameworkDebugOptions
instanceKlass org/osgi/util/tracker/ServiceTrackerCustomizer
instanceKlass java/nio/file/FileVisitor
instanceKlass org/eclipse/osgi/storage/StorageUtil
instanceKlass org/eclipse/osgi/internal/location/BasicLocation
instanceKlass org/eclipse/osgi/internal/location/EquinoxLocations
instanceKlass java/util/concurrent/atomic/AtomicBoolean
instanceKlass java/util/UUID
instanceKlass java/util/Random
instanceKlass java/util/random/RandomGenerator
instanceKlass org/eclipse/osgi/internal/container/InternalUtils
instanceKlass org/osgi/framework/Version
instanceKlass org/eclipse/osgi/internal/location/Locker
instanceKlass org/eclipse/osgi/internal/location/LocationHelper
instanceKlass org/eclipse/osgi/internal/framework/EquinoxConfiguration$ConfigValues
instanceKlass org/eclipse/osgi/internal/util/Tokenizer
instanceKlass java/nio/charset/CoderResult
instanceKlass sun/util/logging/PlatformLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge$LoggerConfiguration
instanceKlass jdk/internal/logger/BootstrapLogger$RedirectedLoggers
instanceKlass jdk/internal/logger/LazyLoggers$LazyLoggerAccessor
instanceKlass jdk/internal/logger/LazyLoggers$LoggerAccessor
instanceKlass jdk/internal/logger/AbstractLoggerWrapper
instanceKlass java/util/ServiceLoader$ProviderImpl
instanceKlass java/util/ServiceLoader$Provider
instanceKlass java/util/ServiceLoader$1
instanceKlass sun/util/logging/internal/LoggingProviderImpl$LogManagerAccess
instanceKlass java/util/concurrent/CopyOnWriteArrayList$COWIterator
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend$1
instanceKlass jdk/internal/logger/BootstrapLogger$DetectBackend
instanceKlass jdk/internal/logger/BootstrapLogger
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge
instanceKlass sun/util/logging/PlatformLogger$Bridge
instanceKlass jdk/internal/logger/DefaultLoggerFinder$1
instanceKlass java/lang/System$LoggerFinder
instanceKlass jdk/internal/logger/LazyLoggers$LazyLoggerFactories
instanceKlass jdk/internal/logger/LazyLoggers$1
instanceKlass jdk/internal/logger/LazyLoggers
instanceKlass jdk/internal/event/EventHelper$ThreadTrackHolder
instanceKlass java/net/URLClassLoader$2
instanceKlass org/eclipse/osgi/internal/framework/AliasMapper
instanceKlass org/eclipse/osgi/framework/util/KeyedElement
instanceKlass org/eclipse/osgi/internal/hookregistry/ClassLoaderHook
instanceKlass org/eclipse/osgi/internal/hookregistry/HookRegistry
instanceKlass org/eclipse/osgi/service/datalocation/Location
instanceKlass org/eclipse/osgi/service/debug/DebugOptions
instanceKlass org/eclipse/osgi/internal/framework/EquinoxConfiguration
instanceKlass org/eclipse/osgi/service/environment/EnvironmentInfo
# instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainer$$InjectedInvoker+0x000002d11008c400
instanceKlass java/lang/invoke/MethodHandleImpl$BindCaller$InjectedInvokerHolder
instanceKlass java/lang/invoke/MethodHandleImpl$BindCaller
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11008c000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11008ac00
instanceKlass java/lang/annotation/Target
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$1
instanceKlass jdk/internal/org/objectweb/asm/Edge
instanceKlass  @bci java/lang/reflect/ProxyGenerator addProxyMethod (Ljava/lang/reflect/Method;Ljava/lang/Class;)V 23 <appendix> argL0 ; # java/lang/reflect/ProxyGenerator$$Lambda+0x000002d110035cd8
instanceKlass  @bci java/lang/reflect/ProxyGenerator addProxyMethod (Ljava/lang/reflect/ProxyGenerator$ProxyMethod;)V 10 <appendix> argL0 ; # java/lang/reflect/ProxyGenerator$$Lambda+0x000002d110035aa8
instanceKlass java/lang/reflect/ProxyGenerator$ProxyMethod
instanceKlass  @bci java/lang/reflect/Proxy getLoader (Ljava/lang/Module;)Ljava/lang/ClassLoader; 6 <appendix> member <vmtarget> ; # java/lang/reflect/Proxy$$Lambda+0x000002d110035380
instanceKlass  @bci java/lang/module/ModuleDescriptor$Builder packages (Ljava/util/Set;)Ljava/lang/module/ModuleDescriptor$Builder; 17 <appendix> argL0 ; # java/lang/module/ModuleDescriptor$Builder$$Lambda+0x800000002
instanceKlass java/lang/module/ModuleDescriptor$Builder
instanceKlass  @bci java/lang/reflect/Proxy$ProxyBuilder getDynamicModule (Ljava/lang/ClassLoader;)Ljava/lang/Module; 4 <appendix> argL0 ; # java/lang/reflect/Proxy$ProxyBuilder$$Lambda+0x000002d110034f60
instanceKlass java/lang/PublicMethods
instanceKlass java/lang/reflect/Proxy$ProxyBuilder
instanceKlass  @bci java/lang/reflect/Proxy getProxyConstructor (Ljava/lang/Class;Ljava/lang/ClassLoader;[Ljava/lang/Class;)Ljava/lang/reflect/Constructor; 35 <appendix> argL0 ; # java/lang/reflect/Proxy$$Lambda+0x000002d110034708
instanceKlass java/lang/reflect/Proxy
instanceKlass sun/reflect/annotation/AnnotationInvocationHandler
instanceKlass java/lang/reflect/InvocationHandler
instanceKlass sun/reflect/annotation/AnnotationParser$1
instanceKlass sun/reflect/annotation/ExceptionProxy
instanceKlass java/lang/annotation/Inherited
instanceKlass java/lang/annotation/Retention
instanceKlass sun/reflect/annotation/AnnotationType$1
instanceKlass sun/reflect/annotation/AnnotationType
instanceKlass java/lang/reflect/GenericArrayType
instanceKlass sun/reflect/generics/visitor/Reifier
instanceKlass sun/reflect/generics/visitor/TypeTreeVisitor
instanceKlass sun/reflect/generics/factory/CoreReflectionFactory
instanceKlass sun/reflect/generics/factory/GenericsFactory
instanceKlass sun/reflect/generics/scope/AbstractScope
instanceKlass sun/reflect/generics/scope/Scope
instanceKlass sun/reflect/generics/tree/ClassTypeSignature
instanceKlass sun/reflect/generics/tree/SimpleClassTypeSignature
instanceKlass sun/reflect/generics/tree/FieldTypeSignature
instanceKlass sun/reflect/generics/tree/BaseType
instanceKlass sun/reflect/generics/tree/TypeSignature
instanceKlass sun/reflect/generics/tree/ReturnType
instanceKlass sun/reflect/generics/tree/TypeArgument
instanceKlass sun/reflect/generics/tree/TypeTree
instanceKlass sun/reflect/generics/tree/Tree
instanceKlass sun/reflect/generics/parser/SignatureParser
instanceKlass org/eclipse/osgi/framework/util/SecureAction$1
instanceKlass org/eclipse/osgi/framework/util/SecureAction
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainer
instanceKlass org/eclipse/osgi/launch/Equinox
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11008a800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11008a400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11008a000
instanceKlass org/eclipse/core/runtime/adaptor/EclipseStarter$InitialBundle
instanceKlass org/eclipse/core/runtime/adaptor/EclipseStarter$StartupEventListener
instanceKlass org/osgi/framework/FrameworkListener
instanceKlass org/osgi/framework/SynchronousBundleListener
instanceKlass java/util/concurrent/Semaphore
instanceKlass org/osgi/framework/BundleContext
instanceKlass org/osgi/framework/BundleReference
instanceKlass java/util/EventObject
instanceKlass org/osgi/framework/BundleListener
instanceKlass java/util/EventListener
instanceKlass org/osgi/framework/launch/Framework
instanceKlass org/osgi/framework/Bundle
instanceKlass org/eclipse/core/runtime/adaptor/EclipseStarter
instanceKlass  @bci java/io/FilePermissionCollection add (Ljava/security/Permission;)V 68 <appendix> argL0 ; # java/io/FilePermissionCollection$$Lambda+0x000002d11002e9d0
instanceKlass sun/security/util/SecurityProperties
instanceKlass sun/security/util/FilePermCompat
instanceKlass java/io/FilePermission$1
instanceKlass jdk/internal/access/JavaIOFilePermissionAccess
instanceKlass java/net/URLClassLoader$1
instanceKlass  @bci org/eclipse/equinox/launcher/Main basicRun ([Ljava/lang/String;)V 162 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002d1100848f0
instanceKlass java/nio/file/FileStore
instanceKlass sun/nio/fs/WindowsSecurity
instanceKlass sun/nio/fs/AbstractAclFileAttributeView
instanceKlass java/nio/file/attribute/AclFileAttributeView
instanceKlass java/nio/file/attribute/FileOwnerAttributeView
instanceKlass sun/nio/fs/WindowsLinkSupport
instanceKlass sun/nio/fs/WindowsFileSystemProvider$1
instanceKlass org/eclipse/equinox/launcher/JNIBridge
instanceKlass sun/nio/ch/IOStatus
instanceKlass sun/nio/ch/Util$BufferCache
instanceKlass sun/nio/ch/Util
instanceKlass sun/nio/ch/NativeThread
instanceKlass java/nio/channels/NetworkChannel
instanceKlass sun/nio/ch/SelChImpl
instanceKlass sun/nio/ch/Streams
instanceKlass java/nio/channels/Channels
instanceKlass sun/nio/ch/FileChannelImpl$Closer
instanceKlass sun/nio/ch/NativeThreadSet
instanceKlass sun/nio/ch/IOUtil
instanceKlass sun/nio/ch/NativeDispatcher
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel
instanceKlass java/nio/channels/InterruptibleChannel
instanceKlass java/nio/channels/ScatteringByteChannel
instanceKlass java/nio/channels/GatheringByteChannel
instanceKlass java/nio/channels/SeekableByteChannel
instanceKlass java/nio/channels/ByteChannel
instanceKlass java/nio/channels/WritableByteChannel
instanceKlass java/nio/channels/ReadableByteChannel
instanceKlass java/nio/channels/Channel
instanceKlass sun/nio/fs/WindowsChannelFactory$2
instanceKlass sun/nio/fs/WindowsChannelFactory$Flags
instanceKlass sun/nio/fs/WindowsChannelFactory$1
instanceKlass sun/nio/fs/WindowsChannelFactory
instanceKlass sun/nio/fs/WindowsPath$1
instanceKlass java/nio/file/Path$1
instanceKlass sun/nio/fs/WindowsSecurityDescriptor
instanceKlass java/nio/file/attribute/FileAttribute
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryFromFragment (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 96 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002d110084470
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryFromFragment (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 86 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002d110084240
instanceKlass  @bci java/util/zip/ZipFile stream ()Ljava/util/stream/Stream; 25 <appendix> member <vmtarget> ; # java/util/zip/ZipFile$$Lambda+0x000002d110025730
instanceKlass java/util/Spliterators$AbstractSpliterator
instanceKlass  @bci java/util/stream/StreamSpliterators$WrappingSpliterator initPartialTraversalState ()V 37 <appendix> member <vmtarget> ; # java/util/stream/StreamSpliterators$WrappingSpliterator$$Lambda+0x000002d110025518
instanceKlass java/util/function/BooleanSupplier
instanceKlass java/util/stream/Sink$ChainedInt
instanceKlass java/util/stream/Sink$OfInt
instanceKlass java/util/function/IntConsumer
instanceKlass  @bci java/util/stream/StreamSpliterators$WrappingSpliterator initPartialTraversalState ()V 24 <appendix> member <vmtarget> ; # java/util/stream/StreamSpliterators$WrappingSpliterator$$Lambda+0x000002d110024598
instanceKlass java/util/stream/StreamSpliterators
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryPath (Ljava/lang/String;Ljava/util/List;)Ljava/lang/String; 190 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002d110084000
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryPath (Ljava/lang/String;Ljava/util/List;)Ljava/lang/String; 180 <appendix> member <vmtarget> ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002d11001bbf0
instanceKlass java/util/stream/Stream$Builder
instanceKlass java/util/stream/Streams$AbstractStreamBuilderImpl
instanceKlass java/util/stream/Streams$2
instanceKlass java/util/stream/Streams
instanceKlass java/util/stream/StreamSpliterators$AbstractWrappingSpliterator
instanceKlass  @bci java/util/stream/AbstractPipeline spliterator ()Ljava/util/Spliterator; 103 <appendix> member <vmtarget> ; # java/util/stream/AbstractPipeline$$Lambda+0x000002d110023a58
instanceKlass java/util/stream/Streams$ConcatSpliterator
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryPath (Ljava/lang/String;Ljava/util/List;)Ljava/lang/String; 143 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002d11001b9c0
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryPath (Ljava/lang/String;Ljava/util/List;)Ljava/lang/String; 133 <appendix> member <vmtarget> ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002d11001b7a8
instanceKlass  @cpi org/eclipse/equinox/launcher/Main 2098 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d110082c00
instanceKlass  @bci org/eclipse/equinox/launcher/Main getLibraryPath (Ljava/lang/String;Ljava/util/List;)Ljava/lang/String; 117 <appendix> member <vmtarget> ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002d11001b570
instanceKlass java/util/function/IntUnaryOperator
instanceKlass java/util/stream/Streams$RangeIntSpliterator
instanceKlass java/util/stream/IntStream
instanceKlass  @bci org/eclipse/equinox/launcher/Main readFrameworkExtensions (Ljava/net/URL;Ljava/util/List;)V 337 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002d110082800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110082400
instanceKlass  @bci org/eclipse/equinox/launcher/Main readFrameworkExtensions (Ljava/net/URL;Ljava/util/List;)V 337 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002d110082000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110081c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110081800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110081400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110081000
instanceKlass  @bci org/eclipse/equinox/launcher/Main readFrameworkExtensions (Ljava/net/URL;Ljava/util/List;)V 337 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002d110080c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110080800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110080400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110080000
instanceKlass java/io/RandomAccessFile$1
instanceKlass  @bci org/eclipse/core/internal/registry/ExtensionsParser parseExtensionPointAttributes (Lorg/xml/sax/Attributes;)V 147 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002d11001fc00
instanceKlass java/lang/invoke/LambdaFormEditor$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11001f800
instanceKlass  @bci org/eclipse/core/internal/registry/ExtensionsParser parseExtensionPointAttributes (Lorg/xml/sax/Attributes;)V 147 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002d11001f400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11001f000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11001ec00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11001e800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11001e400
instanceKlass  @bci org/eclipse/core/internal/registry/ExtensionsParser parseExtensionPointAttributes (Lorg/xml/sax/Attributes;)V 147 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002d11001e000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11001dc00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11001d800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11001d400
instanceKlass  @bci org/eclipse/equinox/launcher/Main searchFor (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String; 95 <appendix> member <vmtarget> ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002d11001b338
instanceKlass  @bci org/eclipse/equinox/launcher/Main findMax (Ljava/lang/String;Ljava/util/List;)Ljava/util/Optional; 34 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002d11001b108
instanceKlass java/util/stream/ReduceOps$2ReducingSink
instanceKlass  @bci java/util/function/BinaryOperator maxBy (Ljava/util/Comparator;)Ljava/util/function/BinaryOperator; 6 <appendix> member <vmtarget> ; # java/util/function/BinaryOperator$$Lambda+0x000002d110020dd0
instanceKlass  @bci java/util/Comparator comparing (Ljava/util/function/Function;Ljava/util/Comparator;)Ljava/util/Comparator; 12 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x000002d110020b40
instanceKlass  @cpi java/util/Comparator 256 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d11001d000
instanceKlass  @bci java/util/Comparator comparing (Ljava/util/function/Function;)Ljava/util/Comparator; 6 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x000002d1100208b0
instanceKlass  @bci org/eclipse/equinox/launcher/Main$Identifier <clinit> ()V 34 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$Identifier$$Lambda+0x000002d11001aed8
instanceKlass  @bci org/eclipse/equinox/launcher/Main$Identifier <clinit> ()V 18 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$Identifier$$Lambda+0x000002d11001acc8
instanceKlass  @bci java/util/Comparator thenComparing (Ljava/util/Comparator;)Ljava/util/Comparator; 7 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x000002d110020620
instanceKlass  @cpi java/util/Comparator 251 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d11001cc00
instanceKlass  @bci org/eclipse/equinox/launcher/Main$Identifier <clinit> ()V 8 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$Identifier$$Lambda+0x000002d11001aab8
instanceKlass  @bci java/util/Comparator comparingInt (Ljava/util/function/ToIntFunction;)Ljava/util/Comparator; 6 <appendix> member <vmtarget> ; # java/util/Comparator$$Lambda+0x000002d110020390
instanceKlass  @bci java/lang/invoke/BootstrapMethodInvoker invoke (Ljava/lang/Class;Ljava/lang/invoke/MethodHandle;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object; 462 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002d11001c800
instanceKlass  @cpi org/eclipse/osgi/container/ModuleResolver 741 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d11001c400
instanceKlass  @bci org/eclipse/equinox/launcher/Main$Identifier <clinit> ()V 0 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$Identifier$$Lambda+0x000002d11001a8a8
instanceKlass java/util/function/ToIntFunction
instanceKlass  @bci org/eclipse/equinox/launcher/Main findMax (Ljava/lang/String;Ljava/util/List;)Ljava/util/Optional; 18 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002d11001a678
instanceKlass  @cpi aQute/bnd/stream/EntryPipeline 557 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d11001c000
instanceKlass  @bci org/eclipse/equinox/launcher/Main findMax (Ljava/lang/String;Ljava/util/List;)Ljava/util/Optional; 8 <appendix> member <vmtarget> ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002d11001a240
instanceKlass java/util/ArrayList$ArrayListSpliterator
instanceKlass  @bci org/eclipse/core/internal/registry/osgi/Activator startRegistry ()V 110 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002d110019c00
instanceKlass java/util/stream/AbstractSpinedBuffer
instanceKlass java/util/stream/Node$Builder
instanceKlass java/util/stream/Node$OfDouble
instanceKlass java/util/stream/Node$OfLong
instanceKlass java/util/stream/Node$OfInt
instanceKlass java/util/stream/Node$OfPrimitive
instanceKlass java/util/stream/Nodes$EmptyNode
instanceKlass java/util/stream/Node
instanceKlass java/util/stream/Nodes
instanceKlass  @bci java/util/stream/ReferencePipeline toArray ()[Ljava/lang/Object; 1 <appendix> argL0 ; # java/util/stream/ReferencePipeline$$Lambda+0x000002d11007f558
instanceKlass java/util/ImmutableCollections$Access$1
instanceKlass jdk/internal/access/JavaUtilCollectionAccess
instanceKlass java/util/ImmutableCollections$Access
instanceKlass  @bci org/eclipse/equinox/launcher/Main getArrayFromList (Ljava/lang/String;)Ljava/util/List; 32 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002d11001a000
instanceKlass  @bci org/eclipse/equinox/launcher/Main getArrayFromList (Ljava/lang/String;)Ljava/util/List; 22 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002d110015cb0
instanceKlass java/util/regex/Pattern$1MatcherIterator
instanceKlass sun/net/www/MimeEntry
instanceKlass java/util/Hashtable$Enumerator
instanceKlass java/util/Collections$SynchronizedCollection
instanceKlass java/util/Properties$EntrySet
instanceKlass sun/net/www/MimeTable$DefaultInstanceHolder$1
instanceKlass sun/net/www/MimeTable$DefaultInstanceHolder
instanceKlass sun/net/www/MimeTable$2
instanceKlass sun/net/www/MimeTable$1
instanceKlass sun/net/www/MimeTable
instanceKlass java/net/URLConnection$1
instanceKlass java/net/FileNameMap
instanceKlass  @bci org/eclipse/equinox/launcher/Main getInstallLocation ()Ljava/net/URL; 287 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002d110019800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110019400
instanceKlass  @bci org/eclipse/equinox/launcher/Main getInstallLocation ()Ljava/net/URL; 287 <appendix> argL3 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002d110019000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110018c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110018800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110018400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110018000
instanceKlass  @bci org/eclipse/equinox/launcher/Main getInstallLocation ()Ljava/net/URL; 287 <appendix> argL1 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002d110017c00
instanceKlass  @bci org/eclipse/core/internal/registry/ExtensionsParser parseExtensionPointAttributes (Lorg/xml/sax/Attributes;)V 147 <appendix> argL1 argL0 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002d110017800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110017400
instanceKlass  @bci org/eclipse/equinox/launcher/Main getInstallLocation ()Ljava/net/URL; 287 <appendix> argL1 argL0 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002d110017000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110016c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110016800
instanceKlass java/lang/Long$LongCache
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110016400
instanceKlass java/util/Collections$3
instanceKlass jdk/internal/loader/URLClassPath$1
instanceKlass java/lang/CompoundEnumeration
instanceKlass jdk/internal/loader/BuiltinClassLoader$1
instanceKlass java/util/Collections$EmptyEnumeration
instanceKlass java/util/ServiceLoader$3
instanceKlass java/util/ServiceLoader$2
instanceKlass java/util/ServiceLoader$LazyClassPathLookupIterator
instanceKlass java/util/Spliterators$1Adapter
instanceKlass java/util/Spliterators$ArraySpliterator
instanceKlass java/util/ServiceLoader$ModuleServicesLookupIterator
instanceKlass java/util/ServiceLoader
instanceKlass java/net/spi/URLStreamHandlerProvider
instanceKlass java/net/URL$1
instanceKlass java/net/URL$2
instanceKlass java/net/URL$ThreadTrackHolder
instanceKlass  @bci org/eclipse/equinox/launcher/Main basicRun ([Ljava/lang/String;)V 29 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002d110015aa0
instanceKlass java/util/function/IntFunction
instanceKlass  @bci org/eclipse/equinox/launcher/Main processCommandLine (Ljava/util/List;)Ljava/util/List; 832 <appendix> argL0 ; # org/eclipse/equinox/launcher/Main$$Lambda+0x000002d110015880
instanceKlass  @cpi org/eclipse/equinox/launcher/Main 2269 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d110016000
instanceKlass  @bci java/util/ArrayDeque copyElements (Ljava/util/Collection;)V 2 <appendix> member <vmtarget> ; # java/util/ArrayDeque$$Lambda+0x000002d11007ceb0
instanceKlass java/lang/Thread$ThreadNumbering
instanceKlass java/nio/file/attribute/PosixFilePermissions
instanceKlass java/security/Policy
instanceKlass jdk/internal/misc/PreviewFeatures
instanceKlass jdk/internal/misc/MainMethodFinder
instanceKlass org/eclipse/equinox/launcher/Main
instanceKlass sun/security/util/ManifestEntryVerifier$SunProviderHolder
instanceKlass java/util/Base64$Encoder
instanceKlass java/util/Base64$Decoder
instanceKlass java/util/Base64
instanceKlass javax/crypto/SecretKey
instanceKlass sun/security/util/Length
instanceKlass sun/security/util/KeyUtil
instanceKlass java/security/interfaces/XECKey
instanceKlass java/security/interfaces/ECKey
instanceKlass sun/security/util/JarConstraintsParameters
instanceKlass sun/security/util/ConstraintsParameters
instanceKlass java/security/CodeSigner
instanceKlass java/security/Timestamp
instanceKlass sun/security/timestamp/TimestampToken
instanceKlass java/security/cert/CertPath
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110014c00
instanceKlass java/math/MutableBigInteger
instanceKlass sun/security/rsa/RSAPadding
instanceKlass sun/security/rsa/RSACore
instanceKlass java/security/interfaces/RSAPrivateCrtKey
instanceKlass sun/security/pkcs/PKCS8Key
instanceKlass sun/security/util/InternalPrivateKey
instanceKlass java/security/interfaces/RSAPrivateKey
instanceKlass java/security/PrivateKey
instanceKlass javax/security/auth/Destroyable
instanceKlass jdk/internal/icu/util/CodePointTrie$Data
instanceKlass jdk/internal/icu/util/CodePointMap
instanceKlass jdk/internal/icu/util/VersionInfo
instanceKlass  @bci jdk/internal/module/SystemModuleFinders$SystemModuleReader open (Ljava/lang/String;)Ljava/util/Optional; 6 <appendix> member <vmtarget> ; # jdk/internal/module/SystemModuleFinders$SystemModuleReader$$Lambda+0x000002d110075230
instanceKlass jdk/internal/jimage/decompressor/ZipDecompressor
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressor
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressorFactory
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressorRepository
instanceKlass jdk/internal/jimage/decompressor/CompressedResourceHeader
instanceKlass  @bci jdk/internal/jimage/BasicImageReader getResourceBuffer (Ljdk/internal/jimage/ImageLocation;)Ljava/nio/ByteBuffer; 168 <appendix> member <vmtarget> ; # jdk/internal/jimage/BasicImageReader$$Lambda+0x000002d1100741f0
instanceKlass jdk/internal/jimage/decompressor/ResourceDecompressor$StringsProvider
instanceKlass java/util/TimSort
instanceKlass java/util/Arrays$LegacyMergeSort
instanceKlass java/util/AbstractMap$SimpleEntry
instanceKlass jdk/internal/jimage/ImageBufferCache$2
instanceKlass jdk/internal/jimage/ImageBufferCache
instanceKlass jdk/internal/module/Checks
instanceKlass jdk/internal/icu/impl/ICUBinary$1
instanceKlass jdk/internal/icu/impl/ICUBinary
instanceKlass jdk/internal/icu/impl/NormalizerImpl$IsAcceptable
instanceKlass jdk/internal/icu/impl/ICUBinary$Authenticate
instanceKlass jdk/internal/icu/impl/NormalizerImpl
instanceKlass jdk/internal/icu/impl/Norm2AllModes$Norm2AllModesSingleton
instanceKlass jdk/internal/icu/impl/Norm2AllModes$NFKCSingleton
instanceKlass jdk/internal/icu/impl/Norm2AllModes
instanceKlass jdk/internal/icu/text/Normalizer2
instanceKlass jdk/internal/icu/text/NormalizerBase$ModeImpl
instanceKlass jdk/internal/icu/text/NormalizerBase$NFKDModeImpl
instanceKlass jdk/internal/icu/text/NormalizerBase$1
instanceKlass jdk/internal/icu/text/NormalizerBase$Mode
instanceKlass jdk/internal/icu/text/NormalizerBase
instanceKlass java/text/Normalizer
instanceKlass sun/security/x509/AVAKeyword
instanceKlass java/util/StringJoiner
instanceKlass sun/security/jca/ServiceId
instanceKlass java/security/Signature$1
instanceKlass jdk/internal/access/JavaSecuritySignatureAccess
instanceKlass java/security/SignatureSpi
instanceKlass sun/security/util/SignatureUtil
instanceKlass java/lang/invoke/VarHandle$AccessDescriptor
instanceKlass sun/security/provider/ByteArrayAccess$BE
instanceKlass sun/security/provider/ByteArrayAccess
instanceKlass sun/security/util/MessageDigestSpi2
instanceKlass java/security/MessageDigestSpi
instanceKlass sun/security/pkcs/SigningCertificateInfo$ESSCertId
instanceKlass sun/security/pkcs/SigningCertificateInfo
instanceKlass sun/security/pkcs/PKCS9Attribute
instanceKlass sun/security/pkcs/PKCS9Attributes
instanceKlass java/time/Instant
instanceKlass java/time/zone/ZoneOffsetTransition
instanceKlass java/time/LocalTime
instanceKlass java/time/temporal/ValueRange
instanceKlass java/time/Duration
instanceKlass java/time/temporal/TemporalAmount
instanceKlass java/time/temporal/TemporalUnit
instanceKlass java/time/temporal/TemporalField
instanceKlass java/time/LocalDate
instanceKlass java/time/chrono/ChronoLocalDate
instanceKlass java/time/ZonedDateTime
instanceKlass java/time/chrono/ChronoZonedDateTime
instanceKlass java/time/LocalDateTime
instanceKlass java/time/chrono/ChronoLocalDateTime
instanceKlass java/time/temporal/Temporal
instanceKlass java/time/zone/ZoneOffsetTransitionRule
instanceKlass java/time/zone/ZoneRules
instanceKlass  @bci java/time/ZoneOffset ofTotalSeconds (I)Ljava/time/ZoneOffset; 37 <appendix> argL0 ; # java/time/ZoneOffset$$Lambda+0x80000000c
instanceKlass java/time/temporal/TemporalAdjuster
instanceKlass java/time/temporal/TemporalAccessor
instanceKlass java/time/ZoneId
instanceKlass  @bci java/util/regex/CharPredicates ASCII_DIGIT ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x800000025
instanceKlass  @bci java/util/regex/CharPredicates ASCII_SPACE ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 <appendix> argL0 ; # java/util/regex/CharPredicates$$Lambda+0x800000026
instanceKlass java/util/regex/CharPredicates
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraints$Holder
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraint
instanceKlass java/util/StringTokenizer
instanceKlass java/security/spec/ECFieldF2m
instanceKlass java/security/spec/ECParameterSpec
instanceKlass java/security/spec/ECPoint
instanceKlass java/security/spec/EllipticCurve
instanceKlass java/security/spec/ECFieldFp
instanceKlass java/security/spec/ECField
instanceKlass sun/security/util/CurveDB
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraints
instanceKlass java/util/TreeMap$PrivateEntryIterator
instanceKlass java/util/NavigableSet
instanceKlass java/util/SortedSet
instanceKlass sun/security/util/AbstractAlgorithmConstraints$1
instanceKlass sun/security/util/AlgorithmDecomposer
instanceKlass sun/security/util/DisabledAlgorithmConstraints$JarHolder
instanceKlass  @bci java/util/regex/Pattern DOT ()Ljava/util/regex/Pattern$CharPredicate; 0 <appendix> argL0 ; # java/util/regex/Pattern$$Lambda+0x000002d110066ef8
instanceKlass java/util/regex/ASCII
instanceKlass sun/security/util/AbstractAlgorithmConstraints
instanceKlass java/security/AlgorithmConstraints
instanceKlass sun/security/pkcs/SignerInfo
instanceKlass java/security/cert/PolicyQualifierInfo
instanceKlass sun/security/x509/CertificatePolicyId
instanceKlass sun/security/x509/PolicyInformation
instanceKlass sun/security/x509/DistributionPoint
instanceKlass sun/security/x509/DNSName
instanceKlass sun/security/x509/URIName
instanceKlass sun/security/x509/GeneralName
instanceKlass sun/security/x509/AccessDescription
instanceKlass sun/security/x509/GeneralNames
instanceKlass java/lang/invoke/VarForm
instanceKlass java/lang/invoke/VarHandleGuards
instanceKlass java/lang/invoke/VarHandles
instanceKlass java/lang/System$Logger
instanceKlass jdk/internal/event/EventHelper
instanceKlass sun/security/jca/JCAUtil
instanceKlass sun/security/util/MemoryCache$CacheEntry
instanceKlass sun/security/x509/KeyIdentifier
instanceKlass java/util/TreeMap$Entry
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110014800
instanceKlass sun/security/x509/OIDMap$OIDInfo
instanceKlass sun/security/x509/PKIXExtensions
instanceKlass sun/security/x509/OIDMap
instanceKlass sun/security/x509/Extension
instanceKlass java/security/cert/Extension
instanceKlass java/util/Collections$SynchronizedMap
instanceKlass java/util/NavigableMap
instanceKlass java/util/SortedMap
instanceKlass sun/security/x509/CertificateExtensions
instanceKlass sun/security/rsa/RSAUtil
instanceKlass java/security/interfaces/RSAPublicKey
instanceKlass java/security/interfaces/RSAKey
instanceKlass java/security/spec/PSSParameterSpec
instanceKlass java/security/spec/AlgorithmParameterSpec
instanceKlass java/security/spec/RSAPrivateKeySpec
instanceKlass java/security/spec/RSAPublicKeySpec
instanceKlass java/security/KeyFactorySpi
instanceKlass sun/security/rsa/SunRsaSignEntries
instanceKlass sun/security/jca/ProviderList$ServiceList$1
instanceKlass java/security/KeyFactory
instanceKlass  @bci java/security/spec/EncodedKeySpec <clinit> ()V 0 <appendix> argL0 ; # java/security/spec/EncodedKeySpec$$Lambda+0x000002d11005d8e8
instanceKlass  @cpi java/security/spec/EncodedKeySpec 79 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d110014400
instanceKlass jdk/internal/access/JavaSecuritySpecAccess
instanceKlass java/security/spec/EncodedKeySpec
instanceKlass java/security/spec/KeySpec
instanceKlass sun/security/util/BitArray
instanceKlass sun/security/x509/X509Key
instanceKlass java/security/PublicKey
instanceKlass java/security/Key
instanceKlass sun/security/x509/CertificateX509Key
instanceKlass java/util/Date
instanceKlass sun/util/calendar/CalendarUtils
instanceKlass sun/util/calendar/CalendarDate
instanceKlass sun/util/calendar/CalendarSystem$GregorianHolder
instanceKlass sun/util/calendar/CalendarSystem
instanceKlass sun/security/x509/CertificateValidity
instanceKlass sun/security/x509/AVA
instanceKlass sun/security/x509/RDN
instanceKlass javax/security/auth/x500/X500Principal
instanceKlass  @bci sun/security/x509/X500Name <clinit> ()V 153 <appendix> argL0 ; # sun/security/x509/X500Name$$Lambda+0x000002d11005b4f0
instanceKlass sun/security/x509/X500Name
instanceKlass sun/security/x509/GeneralNameInterface
instanceKlass sun/security/x509/CertificateAlgorithmId
instanceKlass sun/security/x509/SerialNumber
instanceKlass sun/security/x509/CertificateSerialNumber
instanceKlass sun/security/x509/CertificateVersion
instanceKlass sun/security/x509/X509CertInfo
instanceKlass sun/security/util/Cache$EqualByteArray
instanceKlass java/security/cert/X509Extension
instanceKlass java/lang/Byte$ByteCache
instanceKlass  @bci sun/security/util/DerInputStream seeOptionalContextSpecific (I)Z 2 <appendix> member <vmtarget> ; # sun/security/util/DerInputStream$$Lambda+0x000002d110058f20
instanceKlass  @cpi sun/security/util/DerInputStream 295 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d110014000
instanceKlass sun/security/jca/GetInstance$Instance
instanceKlass sun/security/util/Cache
instanceKlass jdk/internal/event/Event
instanceKlass sun/security/jca/GetInstance
instanceKlass java/security/cert/CertificateFactorySpi
instanceKlass java/security/cert/CertificateFactory
instanceKlass sun/security/x509/AlgorithmId
instanceKlass sun/security/util/ByteArrayTagOrder
instanceKlass sun/security/util/ByteArrayLexOrder
instanceKlass sun/security/util/DerValue
instanceKlass sun/security/util/ObjectIdentifier
instanceKlass sun/security/pkcs/ContentInfo
instanceKlass sun/security/util/DerEncoder
instanceKlass sun/security/util/DerInputStream
instanceKlass sun/security/pkcs/PKCS7
instanceKlass java/util/Collections$EmptyIterator
instanceKlass java/util/LinkedHashMap$LinkedHashIterator
instanceKlass sun/security/util/SecurityProviderConstants
instanceKlass java/security/Provider$UString
instanceKlass java/security/Provider$Service
instanceKlass sun/security/provider/NativePRNG$NonBlocking
instanceKlass sun/security/provider/NativePRNG$Blocking
instanceKlass sun/security/provider/NativePRNG
instanceKlass sun/security/provider/SunEntries$1
instanceKlass sun/security/provider/SunEntries
instanceKlass sun/security/util/SecurityConstants
instanceKlass java/security/Security$1
instanceKlass jdk/internal/access/JavaSecurityPropertiesAccess
instanceKlass java/util/concurrent/ConcurrentHashMap$MapEntry
instanceKlass java/io/FileInputStream$1
instanceKlass java/util/Properties$LineReader
instanceKlass  @bci java/security/Security <clinit> ()V 9 <appendix> argL0 ; # java/security/Security$$Lambda+0x80000000b
instanceKlass java/security/Security
instanceKlass sun/security/jca/ProviderList$2
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$PreparedASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryConverter
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ExceptionalBinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIConverter
instanceKlass jdk/internal/math/FloatingDecimal
instanceKlass javax/security/auth/login/Configuration$Parameters
instanceKlass java/security/Policy$Parameters
instanceKlass java/security/cert/CertStoreParameters
instanceKlass java/security/SecureRandomParameters
instanceKlass java/security/Provider$EngineDescription
instanceKlass java/security/Provider$ServiceKey
instanceKlass sun/security/jca/ProviderConfig
instanceKlass sun/security/jca/ProviderList
instanceKlass sun/security/jca/Providers
instanceKlass  @bci sun/security/util/ManifestDigester <init> ([B)V 350 <appendix> argL0 ; # sun/security/util/ManifestDigester$$Lambda+0x000002d11004ce80
instanceKlass sun/security/util/ManifestDigester$Section
instanceKlass sun/security/util/ManifestDigester$Entry
instanceKlass sun/security/util/ManifestDigester$Position
instanceKlass sun/security/util/ManifestDigester
instanceKlass sun/security/util/ManifestEntryVerifier
instanceKlass jdk/internal/misc/ThreadTracker
instanceKlass java/util/jar/JarFile$ThreadTrackHolder
instanceKlass java/util/jar/JarVerifier
instanceKlass sun/nio/cs/ArrayDecoder
instanceKlass java/nio/charset/CharsetDecoder
instanceKlass sun/launcher/LauncherHelper
instanceKlass lombok/patcher/scripts/ScriptBuilder$SetSymbolDuringMethodCallBuilder
instanceKlass lombok/patcher/scripts/ScriptBuilder$ReplaceMethodCallBuilder
instanceKlass lombok/eclipse/agent/EclipsePatcher$4
instanceKlass lombok/eclipse/agent/EclipsePatcher$3
instanceKlass lombok/patcher/scripts/ScriptBuilder$WrapMethodCallBuilder
instanceKlass lombok/patcher/ScriptManager$WitnessAction
instanceKlass lombok/patcher/scripts/ScriptBuilder$WrapReturnValueBuilder
instanceKlass lombok/patcher/ClassRootFinder
instanceKlass lombok/patcher/scripts/ScriptBuilder$AddFieldBuilder
instanceKlass java/util/Collections$1
instanceKlass lombok/patcher/PatchScript$MethodPatcherFactory
instanceKlass org/lombokweb/asm/ClassVisitor
instanceKlass lombok/patcher/Hook
instanceKlass  @bci java/util/regex/Pattern negate (Ljava/util/regex/Pattern$CharPredicate;)Ljava/util/regex/Pattern$CharPredicate; 1 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000031
instanceKlass java/util/regex/Pattern$BitClass
instanceKlass lombok/patcher/MethodTarget
instanceKlass lombok/patcher/scripts/ScriptBuilder$ExitEarlyBuilder
instanceKlass lombok/patcher/scripts/ScriptBuilder
instanceKlass lombok/eclipse/agent/EclipseLoaderPatcher
instanceKlass lombok/eclipse/agent/EclipsePatcher$2
instanceKlass lombok/eclipse/agent/EclipsePatcher$1
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11000d800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11000d400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11000d000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d11000cc00
# instanceKlass java/lang/invoke/LambdaForm$BMH+0x000002d11000c800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002d11000c400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002d11000c000
instanceKlass java/lang/instrument/ClassDefinition
instanceKlass lombok/patcher/ScriptManager$OurClassFileTransformer
instanceKlass lombok/patcher/Filter$1
instanceKlass lombok/patcher/TransplantMapper$1
instanceKlass java/lang/instrument/ClassFileTransformer
instanceKlass lombok/patcher/ScriptManager
instanceKlass lombok/patcher/TransplantMapper
instanceKlass lombok/patcher/Filter
instanceKlass lombok/patcher/PatchScript
instanceKlass lombok/patcher/TargetMatcher
instanceKlass lombok/eclipse/agent/EclipsePatcher
instanceKlass lombok/core/AgentLauncher$AgentLaunchable
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110008400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110008000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110007c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110007800
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002d110007400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002d110007000
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002d110006c00
instanceKlass java/lang/ClassValue$Version
instanceKlass java/lang/ClassValue$Identity
instanceKlass java/lang/ClassValue
instanceKlass java/lang/invoke/MethodHandleImpl$ArrayAccessor
instanceKlass java/lang/invoke/MethodHandleImpl$LoopClauses
instanceKlass java/lang/invoke/MethodHandleImpl$CasesHolder
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110006800
instanceKlass  @cpi org/eclipse/osgi/container/ModuleResolver$ResolveProcess 1261 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d110006400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110006000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110005c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110005800
# instanceKlass java/lang/invoke/LambdaForm$BMH+0x000002d110005400
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002d110005000
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110004c00
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002d110004800
instanceKlass java/lang/invoke/ClassSpecializer$Factory$1Var
instanceKlass java/lang/invoke/MethodHandles$1
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002d110004400
instanceKlass sun/invoke/util/ValueConversions$1
instanceKlass sun/invoke/util/ValueConversions$WrapperCache
# instanceKlass java/lang/invoke/LambdaForm$DMH+0x000002d110004000
instanceKlass jdk/internal/foreign/MemorySessionImpl
instanceKlass java/lang/foreign/MemorySegment$Scope
instanceKlass lombok/core/AgentLauncher$AgentInfo
instanceKlass lombok/core/AgentLauncher
instanceKlass java/util/IdentityHashMap$IdentityHashMapIterator
instanceKlass lombok/launch/ClassFileMetaData
instanceKlass sun/net/www/MessageHeader
instanceKlass sun/net/www/protocol/jar/JarFileFactory
instanceKlass sun/net/www/protocol/jar/URLJarFile$URLJarFileCloseController
instanceKlass java/net/URLConnection
instanceKlass java/util/zip/ZipFile$ZipEntryIterator
instanceKlass java/util/WeakHashMap$HashIterator
instanceKlass java/net/URLDecoder
instanceKlass java/util/regex/IntHashSet
instanceKlass java/util/regex/Matcher
instanceKlass java/util/regex/MatchResult
instanceKlass java/util/regex/Pattern$TreeInfo
instanceKlass  @bci java/util/regex/Pattern Single (I)Ljava/util/regex/Pattern$BmpCharPredicate; 1 <appendix> member <vmtarget> ; # java/util/regex/Pattern$$Lambda+0x800000029
instanceKlass java/util/regex/Pattern$BmpCharPredicate
instanceKlass java/util/regex/Pattern$CharPredicate
instanceKlass java/util/regex/Pattern$Node
instanceKlass java/util/regex/Pattern
instanceKlass jdk/internal/jimage/ImageLocation
instanceKlass jdk/internal/jimage/decompressor/Decompressor
instanceKlass jdk/internal/jimage/ImageStringsReader
instanceKlass jdk/internal/jimage/ImageStrings
instanceKlass jdk/internal/jimage/ImageHeader
instanceKlass jdk/internal/jimage/NativeImageBuffer$1
instanceKlass jdk/internal/jimage/NativeImageBuffer
instanceKlass jdk/internal/jimage/BasicImageReader$1
instanceKlass jdk/internal/jimage/BasicImageReader
instanceKlass jdk/internal/jimage/ImageReader
instanceKlass jdk/internal/jimage/ImageReaderFactory$1
instanceKlass java/net/URI$Parser
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder$1
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder
instanceKlass java/nio/file/FileSystems
instanceKlass java/nio/file/Paths
instanceKlass jdk/internal/jimage/ImageReaderFactory
instanceKlass jdk/internal/module/SystemModuleFinders$SystemImage
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleReader
instanceKlass java/lang/module/ModuleReader
instanceKlass jdk/internal/loader/BuiltinClassLoader$5
instanceKlass jdk/internal/loader/BuiltinClassLoader$2
instanceKlass jdk/internal/module/Resources
instanceKlass java/util/Arrays$ArrayItr
instanceKlass lombok/launch/PackageShader
instanceKlass java/io/Reader
instanceKlass java/lang/Readable
instanceKlass lombok/launch/Main
instanceKlass  @bci jdk/internal/reflect/DirectMethodHandleAccessor invokeImpl (Ljava/lang/Object;[Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object; 136 <adapter> ; # java/lang/invoke/LambdaForm$MH+0x000002d110002c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110002800
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110002400
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110002000
instanceKlass  @bci org/eclipse/core/internal/boot/PlatformURLConnection <clinit> ()V 23 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002d110001c00
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110001800
instanceKlass sun/instrument/InstrumentationImpl$1
instanceKlass lombok/launch/Agent
instanceKlass java/security/SecureClassLoader$DebugHolder
instanceKlass java/security/Permission
instanceKlass java/security/Guard
instanceKlass java/security/PermissionCollection
instanceKlass java/security/SecureClassLoader$1
instanceKlass java/util/zip/Checksum$1
instanceKlass java/util/zip/CRC32
instanceKlass java/util/zip/Checksum
instanceKlass sun/nio/ByteBuffered
instanceKlass java/lang/Package$VersionInfo
instanceKlass java/lang/NamedPackage
instanceKlass java/util/jar/Attributes$Name
instanceKlass java/util/jar/Attributes
instanceKlass jdk/internal/loader/Resource
instanceKlass sun/security/action/GetIntegerAction
instanceKlass sun/security/util/Debug
instanceKlass sun/security/util/SignatureFileVerifier
instanceKlass java/util/zip/ZipFile$InflaterCleanupAction
instanceKlass java/util/zip/Inflater$InflaterZStreamRef
instanceKlass java/util/zip/Inflater
instanceKlass java/util/zip/ZipEntry
instanceKlass java/util/zip/ZipFile$2
instanceKlass java/nio/Bits$1
instanceKlass jdk/internal/misc/VM$BufferPool
instanceKlass java/nio/Bits
instanceKlass sun/nio/ch/DirectBuffer
instanceKlass jdk/internal/perf/PerfCounter$CoreCounters
instanceKlass jdk/internal/perf/Perf
instanceKlass jdk/internal/perf/Perf$GetPerfAction
instanceKlass jdk/internal/perf/PerfCounter
instanceKlass sun/util/locale/LocaleUtils
instanceKlass sun/util/locale/BaseLocale
instanceKlass java/util/Locale
instanceKlass java/nio/file/attribute/FileTime
instanceKlass java/lang/StringCoding
instanceKlass java/util/zip/ZipUtils
instanceKlass java/util/zip/ZipFile$Source$End
instanceKlass java/io/RandomAccessFile$2
instanceKlass jdk/internal/access/JavaIORandomAccessFileAccess
instanceKlass java/io/RandomAccessFile
instanceKlass java/io/DataInput
instanceKlass java/io/DataOutput
instanceKlass sun/nio/fs/WindowsNativeDispatcher$CompletionStatus
instanceKlass sun/nio/fs/WindowsNativeDispatcher$AclInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$Account
instanceKlass sun/nio/fs/WindowsNativeDispatcher$DiskFreeSpace
instanceKlass sun/nio/fs/WindowsNativeDispatcher$VolumeInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstStream
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstFile
instanceKlass java/util/Enumeration
instanceKlass java/util/concurrent/ConcurrentHashMap$Traverser
instanceKlass sun/nio/fs/WindowsNativeDispatcher
instanceKlass sun/nio/fs/NativeBuffer$Deallocator
instanceKlass sun/nio/fs/NativeBuffer
instanceKlass java/lang/ThreadLocal$ThreadLocalMap
instanceKlass java/lang/ThreadLocal
instanceKlass sun/nio/fs/NativeBuffers
instanceKlass sun/nio/fs/WindowsFileAttributes
instanceKlass java/nio/file/attribute/DosFileAttributes
instanceKlass sun/nio/fs/AbstractBasicFileAttributeView
instanceKlass sun/nio/fs/DynamicFileAttributeView
instanceKlass sun/nio/fs/WindowsFileAttributeViews
instanceKlass sun/nio/fs/Util
instanceKlass java/nio/file/attribute/BasicFileAttributeView
instanceKlass java/nio/file/attribute/FileAttributeView
instanceKlass java/nio/file/attribute/AttributeView
instanceKlass java/nio/file/Files
instanceKlass java/nio/file/CopyOption
instanceKlass java/nio/file/attribute/BasicFileAttributes
instanceKlass sun/nio/fs/WindowsPath
instanceKlass java/util/zip/ZipFile$Source$Key
instanceKlass sun/nio/fs/WindowsPathParser$Result
instanceKlass sun/nio/fs/WindowsPathParser
instanceKlass java/nio/file/FileSystem
instanceKlass java/nio/file/OpenOption
instanceKlass java/nio/file/spi/FileSystemProvider
instanceKlass sun/nio/fs/DefaultFileSystemProvider
instanceKlass java/util/zip/ZipFile$Source
instanceKlass java/util/zip/ZipCoder
instanceKlass java/util/zip/ZipFile$CleanableResource
instanceKlass java/lang/Runtime$Version
instanceKlass java/util/jar/JavaUtilJarAccessImpl
instanceKlass jdk/internal/access/JavaUtilJarAccess
instanceKlass jdk/internal/loader/FileURLMapper
instanceKlass jdk/internal/loader/URLClassPath$JarLoader$1
instanceKlass java/util/zip/ZipFile$1
instanceKlass jdk/internal/access/JavaUtilZipFileAccess
instanceKlass java/util/zip/ZipFile
instanceKlass java/util/zip/ZipConstants
instanceKlass jdk/internal/loader/URLClassPath$Loader
instanceKlass jdk/internal/loader/URLClassPath$3
instanceKlass java/security/PrivilegedExceptionAction
instanceKlass sun/net/util/URLUtil
instanceKlass sun/instrument/TransformerManager$TransformerInfo
instanceKlass sun/instrument/TransformerManager
instanceKlass jdk/internal/loader/NativeLibraries$3
instanceKlass jdk/internal/loader/NativeLibrary
instanceKlass java/util/ArrayDeque$DeqIterator
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryContext$1
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryContext
instanceKlass jdk/internal/loader/NativeLibraries$2
instanceKlass jdk/internal/loader/NativeLibraries$1
instanceKlass jdk/internal/loader/NativeLibraries$LibraryPaths
instanceKlass  @bci sun/instrument/InstrumentationImpl <clinit> ()V 16 <appendix> argL0 ; # sun/instrument/InstrumentationImpl$$Lambda+0x000002d110042ca0
instanceKlass sun/instrument/InstrumentationImpl
instanceKlass java/lang/instrument/Instrumentation
instanceKlass java/lang/invoke/StringConcatFactory
instanceKlass jdk/internal/module/ModuleBootstrap$SafeModuleFinder
instanceKlass  @bci java/lang/WeakPairMap computeIfAbsent (Ljava/lang/Object;Ljava/lang/Object;Ljava/util/function/BiFunction;)Ljava/lang/Object; 18 <appendix> member <vmtarget> ; # java/lang/WeakPairMap$$Lambda+0x000002d110042558
instanceKlass  @bci java/lang/Module implAddExportsOrOpens (Ljava/lang/String;Ljava/lang/Module;ZZ)V 145 <appendix> argL0 ; # java/lang/Module$$Lambda+0x000002d110041c40
instanceKlass  @bci jdk/internal/module/ModuleBootstrap decode (Ljava/lang/String;Ljava/lang/String;Z)Ljava/util/Map; 193 <appendix> argL0 ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x000002d110041a10
instanceKlass java/lang/ModuleLayer$Controller
instanceKlass java/util/concurrent/CopyOnWriteArrayList
instanceKlass jdk/internal/module/ServicesCatalog$ServiceProvider
instanceKlass jdk/internal/loader/AbstractClassLoaderValue$Memoizer
instanceKlass jdk/internal/module/ModuleLoaderMap$Modules
instanceKlass jdk/internal/module/ModuleLoaderMap$Mapper
instanceKlass jdk/internal/module/ModuleLoaderMap
instanceKlass java/lang/module/ResolvedModule
instanceKlass java/util/Collections$UnmodifiableCollection$1
instanceKlass java/util/SequencedMap
instanceKlass java/util/SequencedSet
instanceKlass java/lang/ModuleLayer
instanceKlass java/util/ImmutableCollections$ListItr
instanceKlass java/util/ListIterator
instanceKlass java/lang/module/ModuleFinder$1
instanceKlass java/nio/file/Path
instanceKlass java/nio/file/Watchable
instanceKlass java/lang/module/Resolver
instanceKlass java/lang/module/Configuration
instanceKlass java/util/stream/ForEachOps$ForEachOp
instanceKlass java/util/stream/ForEachOps
instanceKlass  @bci jdk/internal/module/ModuleBootstrap boot2 ()Ljava/lang/ModuleLayer; 791 <appendix> member <vmtarget> ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x000002d110041030
instanceKlass  @cpi org/eclipse/core/internal/registry/RegistryProperties 118 form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$DMH+0x000002d110000c00
instanceKlass  @bci jdk/internal/module/ModuleBootstrap boot2 ()Ljava/lang/ModuleLayer; 779 <appendix> member <vmtarget> ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x000002d110040de8
instanceKlass  @bci jdk/internal/module/ModuleBootstrap boot2 ()Ljava/lang/ModuleLayer; 767 <appendix> argL0 ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x000002d110040bb8
instanceKlass  @bci jdk/internal/module/ModuleBootstrap boot2 ()Ljava/lang/ModuleLayer; 757 <appendix> argL0 ; # jdk/internal/module/ModuleBootstrap$$Lambda+0x000002d110040988
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 43 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x800000048
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 38 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004a
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 16 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x800000049
instanceKlass  @bci java/util/stream/FindOps$FindSink$OfRef <clinit> ()V 11 <appendix> argL0 ; # java/util/stream/FindOps$FindSink$OfRef$$Lambda+0x80000004b
instanceKlass java/util/stream/FindOps$FindOp
instanceKlass java/util/stream/FindOps$FindSink
instanceKlass java/util/stream/FindOps
instanceKlass  @bci jdk/internal/module/DefaultRoots exportsAPI (Ljava/lang/module/ModuleDescriptor;)Z 9 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x800000051
instanceKlass java/util/stream/Sink$ChainedReference
instanceKlass java/util/stream/ReduceOps$AccumulatingSink
instanceKlass java/util/stream/TerminalSink
instanceKlass java/util/stream/Sink
instanceKlass java/util/function/Consumer
instanceKlass java/util/stream/ReduceOps$Box
instanceKlass java/util/stream/ReduceOps$ReduceOp
instanceKlass java/util/stream/TerminalOp
instanceKlass java/util/stream/ReduceOps
instanceKlass  @bci java/util/stream/Collectors castingIdentity ()Ljava/util/function/Function; 0 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000042
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 14 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000040
instanceKlass java/util/function/BinaryOperator
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 9 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000039
instanceKlass java/util/function/BiConsumer
instanceKlass  @bci java/util/stream/Collectors toSet ()Ljava/util/stream/Collector; 4 <appendix> argL0 ; # java/util/stream/Collectors$$Lambda+0x800000045
instanceKlass java/util/stream/Collector
instanceKlass java/util/Collections$UnmodifiableCollection
instanceKlass java/util/stream/Collectors
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 42 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x80000004e
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 32 <appendix> member <vmtarget> ; # jdk/internal/module/DefaultRoots$$Lambda+0x800000052
instanceKlass java/util/concurrent/ForkJoinPool$ManagedBlocker
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$Node
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 21 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x80000004f
instanceKlass java/lang/ref/Cleaner$Cleanable
instanceKlass jdk/internal/ref/CleanerImpl
instanceKlass java/lang/ref/Cleaner$1
instanceKlass java/lang/ref/Cleaner
instanceKlass jdk/internal/ref/CleanerFactory$1
instanceKlass java/util/concurrent/ThreadFactory
instanceKlass jdk/internal/ref/CleanerFactory
instanceKlass  @bci org/eclipse/osgi/util/NLS <clinit> ()V 7 <appendix> form vmentry <vmtarget> ; # java/lang/invoke/LambdaForm$MH+0x000002d110000800
instanceKlass  @bci jdk/internal/module/DefaultRoots compute (Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set; 11 <appendix> argL0 ; # jdk/internal/module/DefaultRoots$$Lambda+0x800000050
instanceKlass java/lang/invoke/LambdaProxyClassArchive
instanceKlass java/lang/invoke/InfoFromMemberName
instanceKlass java/lang/invoke/MethodHandleInfo
instanceKlass jdk/internal/org/objectweb/asm/ConstantDynamic
instanceKlass jdk/internal/org/objectweb/asm/Handle
instanceKlass sun/security/action/GetBooleanAction
instanceKlass java/lang/invoke/AbstractValidatingLambdaMetafactory
instanceKlass java/lang/invoke/BootstrapMethodInvoker
instanceKlass java/util/function/Predicate
instanceKlass java/lang/WeakPairMap$Pair$Lookup
instanceKlass java/lang/WeakPairMap$Pair
instanceKlass java/lang/WeakPairMap
instanceKlass java/lang/Module$ReflectionData
instanceKlass java/lang/invoke/LambdaMetafactory
# instanceKlass java/lang/invoke/LambdaForm$MH+0x000002d110000400
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassDefiner
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassFile
instanceKlass jdk/internal/org/objectweb/asm/Handler
instanceKlass jdk/internal/org/objectweb/asm/Attribute
instanceKlass jdk/internal/org/objectweb/asm/FieldVisitor
instanceKlass java/util/ArrayList$Itr
instanceKlass sun/invoke/empty/Empty
instanceKlass sun/invoke/util/VerifyType
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$ClassData
instanceKlass jdk/internal/org/objectweb/asm/AnnotationVisitor
instanceKlass jdk/internal/org/objectweb/asm/Frame
instanceKlass jdk/internal/org/objectweb/asm/Label
instanceKlass jdk/internal/org/objectweb/asm/Type
instanceKlass jdk/internal/org/objectweb/asm/MethodVisitor
instanceKlass sun/invoke/util/BytecodeDescriptor
instanceKlass jdk/internal/org/objectweb/asm/ByteVector
instanceKlass jdk/internal/org/objectweb/asm/Symbol
instanceKlass jdk/internal/org/objectweb/asm/SymbolTable
instanceKlass jdk/internal/org/objectweb/asm/ClassVisitor
instanceKlass java/lang/invoke/LambdaFormBuffer
instanceKlass java/lang/invoke/LambdaFormEditor$TransformKey
instanceKlass java/lang/invoke/LambdaFormEditor
instanceKlass java/lang/invoke/Invokers$Holder
instanceKlass java/lang/invoke/DelegatingMethodHandle$Holder
instanceKlass java/lang/invoke/DirectMethodHandle$2
instanceKlass java/lang/invoke/ClassSpecializer$Factory
instanceKlass java/lang/invoke/ClassSpecializer$SpeciesData
instanceKlass java/lang/invoke/ClassSpecializer$1
instanceKlass java/lang/invoke/ClassSpecializer
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$1
instanceKlass java/lang/invoke/InvokerBytecodeGenerator
instanceKlass java/lang/invoke/LambdaForm$Holder
instanceKlass java/lang/invoke/LambdaForm$Name
instanceKlass java/lang/reflect/Array
instanceKlass java/lang/invoke/Invokers
instanceKlass sun/invoke/util/ValueConversions
instanceKlass java/lang/invoke/DirectMethodHandle$Holder
instanceKlass java/lang/Void
instanceKlass sun/invoke/util/Wrapper$Format
instanceKlass java/lang/invoke/MethodHandleImpl$1
instanceKlass jdk/internal/access/JavaLangInvokeAccess
instanceKlass java/lang/invoke/LambdaForm$NamedFunction
instanceKlass java/lang/invoke/MethodHandleImpl
instanceKlass jdk/internal/reflect/MethodHandleAccessorFactory$LazyStaticHolder
instanceKlass java/lang/invoke/MethodTypeForm
instanceKlass jdk/internal/util/StrongReferenceKey
instanceKlass jdk/internal/util/ReferenceKey
instanceKlass jdk/internal/util/ReferencedKeyMap
instanceKlass java/lang/invoke/MethodType$1
instanceKlass sun/reflect/annotation/AnnotationParser
instanceKlass java/lang/Class$3
instanceKlass java/lang/PublicMethods$Key
instanceKlass java/lang/PublicMethods$MethodList
instanceKlass java/util/EnumMap$1
instanceKlass java/util/stream/StreamOpFlag$MaskBuilder
instanceKlass java/util/stream/Stream
instanceKlass java/util/stream/BaseStream
instanceKlass java/util/stream/PipelineHelper
instanceKlass java/util/stream/StreamSupport
instanceKlass java/util/Spliterators$IteratorSpliterator
instanceKlass java/util/Spliterator$OfDouble
instanceKlass java/util/Spliterator$OfLong
instanceKlass java/util/Spliterator$OfInt
instanceKlass java/util/Spliterator$OfPrimitive
instanceKlass java/util/Spliterator
instanceKlass java/util/Spliterators$EmptySpliterator
instanceKlass java/util/Spliterators
instanceKlass jdk/internal/module/DefaultRoots
instanceKlass jdk/internal/loader/BuiltinClassLoader$LoadedModule
instanceKlass jdk/internal/loader/AbstractClassLoaderValue
instanceKlass jdk/internal/module/ServicesCatalog
instanceKlass java/util/Deque
instanceKlass java/util/Queue
instanceKlass sun/net/util/IPAddressUtil$MASKS
instanceKlass sun/net/util/IPAddressUtil
instanceKlass java/net/URLStreamHandler
instanceKlass sun/net/www/ParseUtil
instanceKlass java/net/URL$3
instanceKlass jdk/internal/access/JavaNetURLAccess
instanceKlass java/net/URL$DefaultFactory
instanceKlass java/net/URLStreamHandlerFactory
instanceKlass jdk/internal/loader/URLClassPath
instanceKlass java/security/Principal
instanceKlass java/security/ProtectionDomain$Key
instanceKlass java/security/ProtectionDomain$JavaSecurityAccessImpl
instanceKlass jdk/internal/access/JavaSecurityAccess
instanceKlass java/lang/ClassLoader$ParallelLoaders
instanceKlass java/security/cert/Certificate
instanceKlass jdk/internal/loader/ArchivedClassLoaders
instanceKlass java/util/concurrent/ConcurrentHashMap$CollectionView
instanceKlass jdk/internal/loader/ClassLoaderHelper
instanceKlass jdk/internal/loader/NativeLibraries
instanceKlass java/lang/Module$EnableNativeAccess
instanceKlass jdk/internal/loader/BootLoader
instanceKlass java/util/Optional
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleFinder
instanceKlass java/lang/module/ModuleFinder
instanceKlass jdk/internal/module/SystemModuleFinders$3
instanceKlass jdk/internal/module/ModuleHashes$HashSupplier
instanceKlass jdk/internal/module/SystemModuleFinders$2
instanceKlass java/util/function/Supplier
instanceKlass java/lang/module/ModuleReference
instanceKlass jdk/internal/module/ModuleResolution
instanceKlass java/util/Collections$UnmodifiableMap
instanceKlass jdk/internal/module/ModuleHashes$Builder
instanceKlass jdk/internal/module/ModuleHashes
instanceKlass jdk/internal/module/ModuleTarget
instanceKlass java/util/ImmutableCollections$Set12$1
instanceKlass java/lang/reflect/AccessFlag$18
instanceKlass java/lang/reflect/AccessFlag$17
instanceKlass java/lang/reflect/AccessFlag$16
instanceKlass java/lang/reflect/AccessFlag$15
instanceKlass java/lang/reflect/AccessFlag$14
instanceKlass java/lang/reflect/AccessFlag$13
instanceKlass java/lang/reflect/AccessFlag$12
instanceKlass java/lang/reflect/AccessFlag$11
instanceKlass java/lang/reflect/AccessFlag$10
instanceKlass java/lang/reflect/AccessFlag$9
instanceKlass java/lang/reflect/AccessFlag$8
instanceKlass java/lang/reflect/AccessFlag$7
instanceKlass java/lang/reflect/AccessFlag$6
instanceKlass java/lang/reflect/AccessFlag$5
instanceKlass java/lang/reflect/AccessFlag$4
instanceKlass java/lang/reflect/AccessFlag$3
instanceKlass java/lang/reflect/AccessFlag$2
instanceKlass java/lang/reflect/AccessFlag$1
instanceKlass java/lang/module/ModuleDescriptor$Version
instanceKlass java/lang/module/ModuleDescriptor$Provides
instanceKlass java/lang/module/ModuleDescriptor$Opens
instanceKlass java/util/ImmutableCollections$SetN$SetNIterator
instanceKlass java/lang/module/ModuleDescriptor$Exports
instanceKlass java/lang/module/ModuleDescriptor$Requires
instanceKlass jdk/internal/module/Builder
instanceKlass jdk/internal/module/SystemModules$all
instanceKlass jdk/internal/module/SystemModules
instanceKlass jdk/internal/module/SystemModulesMap
instanceKlass java/net/URI$1
instanceKlass jdk/internal/access/JavaNetUriAccess
instanceKlass java/net/URI
instanceKlass jdk/internal/module/SystemModuleFinders
instanceKlass jdk/internal/module/ArchivedModuleGraph
instanceKlass jdk/internal/module/ArchivedBootLayer
instanceKlass jdk/internal/module/ModuleBootstrap$Counters
instanceKlass jdk/internal/module/ModulePatcher
instanceKlass java/io/FileSystem
instanceKlass java/io/DefaultFileSystem
instanceKlass java/io/File
instanceKlass java/lang/module/ModuleDescriptor$1
instanceKlass jdk/internal/access/JavaLangModuleAccess
instanceKlass sun/invoke/util/VerifyAccess
instanceKlass java/util/KeyValueHolder
instanceKlass java/util/ImmutableCollections$MapN$MapNIterator
instanceKlass java/lang/StrictMath
instanceKlass java/lang/invoke/MethodHandles$Lookup
instanceKlass java/lang/invoke/MemberName$Factory
instanceKlass java/lang/invoke/MethodHandles
instanceKlass java/lang/module/ModuleDescriptor
instanceKlass jdk/internal/module/ModuleBootstrap
instanceKlass java/lang/Character$CharacterCache
instanceKlass java/util/HexFormat
instanceKlass jdk/internal/util/ClassFileDumper
instanceKlass sun/security/action/GetPropertyAction
instanceKlass java/lang/invoke/MethodHandleStatics
instanceKlass jdk/internal/misc/Blocker
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject
instanceKlass java/util/concurrent/locks/Condition
instanceKlass java/util/Collections
instanceKlass java/lang/Thread$ThreadIdentifiers
instanceKlass sun/io/Win32ErrorMode
instanceKlass jdk/internal/misc/OSEnvironment
instanceKlass java/lang/Integer$IntegerCache
instanceKlass jdk/internal/misc/Signal$NativeHandler
instanceKlass java/util/Hashtable$Entry
instanceKlass jdk/internal/misc/Signal
instanceKlass java/lang/Terminator$1
instanceKlass jdk/internal/misc/Signal$Handler
instanceKlass java/lang/Terminator
instanceKlass java/nio/ByteOrder
instanceKlass java/nio/Buffer$2
instanceKlass jdk/internal/access/JavaNioAccess
instanceKlass java/nio/Buffer$1
instanceKlass jdk/internal/misc/ScopedMemoryAccess
instanceKlass java/nio/charset/CodingErrorAction
instanceKlass sun/nio/cs/SingleByte
instanceKlass java/lang/StringUTF16
instanceKlass sun/nio/cs/MS1252$Holder
instanceKlass sun/nio/cs/ArrayEncoder
instanceKlass java/nio/charset/CharsetEncoder
instanceKlass java/io/Writer
instanceKlass java/io/PrintStream$1
instanceKlass jdk/internal/access/JavaIOPrintStreamAccess
instanceKlass jdk/internal/misc/InternalLock
instanceKlass java/io/OutputStream
instanceKlass java/io/Flushable
instanceKlass java/io/FileDescriptor$1
instanceKlass jdk/internal/access/JavaIOFileDescriptorAccess
instanceKlass java/io/FileDescriptor
instanceKlass jdk/internal/util/StaticProperty
instanceKlass jdk/internal/reflect/MethodHandleAccessorFactory
instanceKlass java/lang/reflect/Modifier
instanceKlass java/lang/Class$1
instanceKlass java/lang/Class$Atomic
instanceKlass java/lang/Class$ReflectionData
instanceKlass java/nio/charset/StandardCharsets
instanceKlass sun/nio/cs/HistoricallyNamedCharset
instanceKlass java/nio/charset/spi/CharsetProvider
instanceKlass java/nio/charset/Charset
instanceKlass java/util/HashMap$HashIterator
instanceKlass java/util/concurrent/locks/LockSupport
instanceKlass java/util/concurrent/ConcurrentHashMap$Node
instanceKlass java/util/concurrent/ConcurrentHashMap$CounterCell
instanceKlass java/util/concurrent/locks/ReentrantLock
instanceKlass java/util/concurrent/locks/Lock
instanceKlass java/lang/CharacterData
instanceKlass java/util/Arrays
instanceKlass jdk/internal/util/Preconditions$3
instanceKlass jdk/internal/util/Preconditions$2
instanceKlass jdk/internal/util/Preconditions$4
instanceKlass java/util/function/BiFunction
instanceKlass jdk/internal/util/Preconditions$1
instanceKlass java/util/function/Function
instanceKlass jdk/internal/util/Preconditions
instanceKlass java/lang/Runtime
instanceKlass java/lang/VersionProps
instanceKlass java/lang/StringConcatHelper
instanceKlass java/util/HashMap$Node
instanceKlass java/util/Map$Entry
instanceKlass jdk/internal/util/ArraysSupport
instanceKlass jdk/internal/util/SystemProps$Raw
instanceKlass jdk/internal/util/SystemProps
instanceKlass java/lang/System$2
instanceKlass jdk/internal/access/JavaLangAccess
instanceKlass java/lang/ref/NativeReferenceQueue$Lock
instanceKlass java/lang/ref/ReferenceQueue
instanceKlass java/lang/ref/Reference$1
instanceKlass jdk/internal/access/JavaLangRefAccess
instanceKlass jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/Math
instanceKlass java/lang/StringLatin1
instanceKlass jdk/internal/reflect/Reflection
instanceKlass jdk/internal/reflect/ReflectionFactory$GetReflectionFactoryAction
instanceKlass java/security/PrivilegedAction
instanceKlass jdk/internal/access/SharedSecrets
instanceKlass java/lang/reflect/ReflectAccess
instanceKlass jdk/internal/access/JavaLangReflectAccess
instanceKlass java/util/ImmutableCollections
instanceKlass java/util/Objects
instanceKlass java/util/Set
instanceKlass jdk/internal/misc/CDS
instanceKlass java/lang/Module$ArchivedData
instanceKlass jdk/internal/misc/VM
instanceKlass java/lang/String$CaseInsensitiveComparator
instanceKlass java/util/Comparator
instanceKlass java/io/ObjectStreamField
instanceKlass jdk/internal/vm/FillerObject
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload
instanceKlass jdk/internal/vm/vector/VectorSupport
instanceKlass java/lang/reflect/RecordComponent
instanceKlass java/util/Iterator
instanceKlass java/lang/Number
instanceKlass java/lang/Character
instanceKlass java/lang/Boolean
instanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer
instanceKlass java/lang/LiveStackFrame
instanceKlass java/lang/StackFrameInfo
instanceKlass java/lang/StackWalker$StackFrame
instanceKlass java/lang/StackStreamFactory$AbstractStackWalker
instanceKlass java/lang/StackWalker
instanceKlass java/nio/Buffer
instanceKlass java/lang/StackTraceElement
instanceKlass java/util/RandomAccess
instanceKlass java/util/List
instanceKlass java/util/SequencedCollection
instanceKlass java/util/AbstractCollection
instanceKlass java/util/Collection
instanceKlass java/lang/Iterable
instanceKlass java/util/concurrent/ConcurrentMap
instanceKlass java/util/AbstractMap
instanceKlass java/security/CodeSource
instanceKlass jdk/internal/loader/ClassLoaders
instanceKlass java/util/jar/Manifest
instanceKlass java/lang/Enum
instanceKlass java/net/URL
instanceKlass java/io/InputStream
instanceKlass java/io/Closeable
instanceKlass java/lang/AutoCloseable
instanceKlass jdk/internal/module/Modules
instanceKlass jdk/internal/misc/Unsafe
instanceKlass jdk/internal/misc/UnsafeConstants
instanceKlass java/lang/AbstractStringBuilder
instanceKlass java/lang/Appendable
instanceKlass java/lang/AssertionStatusDirectives
instanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext
instanceKlass jdk/internal/foreign/abi/ABIDescriptor
instanceKlass jdk/internal/foreign/abi/NativeEntryPoint
instanceKlass java/lang/invoke/CallSite
instanceKlass java/lang/invoke/MethodType
instanceKlass java/lang/invoke/TypeDescriptor$OfMethod
instanceKlass java/lang/invoke/LambdaForm
instanceKlass java/lang/invoke/MethodHandleNatives
instanceKlass java/lang/invoke/ResolvedMethodName
instanceKlass java/lang/invoke/MemberName
instanceKlass java/lang/invoke/VarHandle
instanceKlass java/lang/invoke/MethodHandle
instanceKlass jdk/internal/reflect/CallerSensitive
instanceKlass java/lang/annotation/Annotation
instanceKlass jdk/internal/reflect/FieldAccessor
instanceKlass jdk/internal/reflect/ConstantPool
instanceKlass jdk/internal/reflect/ConstructorAccessor
instanceKlass jdk/internal/reflect/MethodAccessor
instanceKlass jdk/internal/reflect/MagicAccessorImpl
instanceKlass jdk/internal/vm/StackChunk
instanceKlass jdk/internal/vm/Continuation
instanceKlass jdk/internal/vm/ContinuationScope
instanceKlass java/lang/reflect/Parameter
instanceKlass java/lang/reflect/Member
instanceKlass java/lang/reflect/AccessibleObject
instanceKlass java/lang/Module
instanceKlass java/util/Map
instanceKlass java/util/Dictionary
instanceKlass java/lang/ThreadGroup
instanceKlass java/lang/Thread$UncaughtExceptionHandler
instanceKlass java/lang/Thread$Constants
instanceKlass java/lang/Thread$FieldHolder
instanceKlass java/lang/Thread
instanceKlass java/lang/Runnable
instanceKlass java/lang/ref/Reference
instanceKlass java/lang/Record
instanceKlass java/security/AccessController
instanceKlass java/security/AccessControlContext
instanceKlass java/security/ProtectionDomain
instanceKlass java/lang/SecurityManager
instanceKlass java/lang/Throwable
instanceKlass java/lang/System
instanceKlass java/lang/ClassLoader
instanceKlass java/lang/Cloneable
instanceKlass java/lang/Class
instanceKlass java/lang/invoke/TypeDescriptor$OfField
instanceKlass java/lang/invoke/TypeDescriptor
instanceKlass java/lang/reflect/Type
instanceKlass java/lang/reflect/GenericDeclaration
instanceKlass java/lang/reflect/AnnotatedElement
instanceKlass java/lang/String
instanceKlass java/lang/constant/ConstantDesc
instanceKlass java/lang/constant/Constable
instanceKlass java/lang/CharSequence
instanceKlass java/lang/Comparable
instanceKlass java/io/Serializable
ciInstanceKlass java/lang/Object 1 1 124 7 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 3 8 1 7 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 3 1 1
ciInstanceKlass java/io/Serializable 1 0 7 100 1 100 1 1 1
ciInstanceKlass java/lang/System 1 1 834 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 7 12 1 1 1 18 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 7 1 10 12 1 8 1 10 12 1 10 12 1 1 100 1 10 12 10 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 12 1 100 1 8 1 10 10 12 1 100 1 8 1 10 8 1 10 100 12 1 1 8 1 10 12 100 1 8 1 10 10 12 1 1 10 7 12 1 1 1 100 1 18 12 1 100 1 9 100 12 1 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 1 7 1 8 1 10 9 12 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 8 1 11 12 1 10 12 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 1 7 1 11 12 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 11 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 9 12 1 8 1 10 7 12 1 1 8 1 7 1 9 7 12 1 1 1 10 12 1 7 1 9 12 10 9 12 7 1 10 12 9 12 1 1 8 1 10 12 1 1 8 1 10 7 12 1 1 10 12 1 10 12 1 1 11 7 12 1 1 10 12 10 7 12 1 1 1 9 12 1 1 7 1 8 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 8 1 8 1 10 8 1 8 1 8 1 8 1 10 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 7 1 8 1 10 10 10 12 1 1 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 9 12 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 1 1 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/System in Ljava/io/InputStream; java/io/BufferedInputStream
staticfield java/lang/System out Ljava/io/PrintStream; java/io/PrintStream
staticfield java/lang/System err Ljava/io/PrintStream; java/io/PrintStream
instanceKlass org/eclipse/osgi/internal/permadmin/EquinoxSecurityManager
instanceKlass org/eclipse/osgi/internal/loader/BundleLoader$ClassContext
instanceKlass org/eclipse/osgi/internal/framework/ContextFinder$Finder
instanceKlass org/eclipse/osgi/internal/url/MultiplexingFactory$InternalSecurityManager
ciInstanceKlass java/lang/SecurityManager 1 1 576 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 1 10 100 1 10 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 100 1 8 1 10 9 12 1 1 9 12 1 8 1 9 12 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 10 12 1 1 100 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 7 12 1 1 1 10 12 1 1 8 1 100 1 8 1 10 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 8 1 100 1 8 1 8 1 10 8 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 11 7 12 1 1 1 18 12 1 1 11 7 12 1 1 1 18 12 1 1 11 12 1 1 18 18 11 12 1 18 12 1 11 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 7 1 10 7 12 1 1 10 12 1 10 12 1 18 12 1 18 10 7 12 1 1 1 18 12 1 10 12 1 18 18 8 1 10 12 1 9 12 1 1 11 7 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 8 1 100 1 10 9 12 1 8 1 10 12 1 8 1 100 1 10 10 7 12 1 1 10 7 1 9 7 12 1 1 1 11 12 1 1 10 12 1 11 12 1 10 12 1 7 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 7 12 1 1 1 16 1 16 15 10 12 16 1 15 10 12 16 15 11 7 1 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 1 16 1 15 11 12 1 15 10 12 16 15 10 16 1 15 10 7 12 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/SecurityManager packageAccessLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/SecurityManager packageDefinitionLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/SecurityManager nonExportedPkgs Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
ciInstanceKlass java/security/AccessController 1 1 295 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 1 10 11 7 12 1 1 1 10 7 12 1 1 11 7 1 100 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 10 100 12 1 1 1 8 1 7 1 10 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 3 1 1 1
staticfield java/security/AccessController $assertionsDisabled Z 1
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader$GenerationProtectionDomain
ciInstanceKlass java/security/ProtectionDomain 1 1 348 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 7 1 9 12 1 1 9 12 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 9 100 12 1 1 10 12 1 1 10 100 1 10 12 1 1 8 1 7 1 8 1 10 12 1 10 11 10 7 12 1 1 1 10 12 1 1 8 1 11 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 8 1 10 7 12 1 1 1 9 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 100 1 18 12 1 1 10 7 12 1 1 1 10 7 1 10 12 1 10 12 1 1 11 100 12 1 1 11 12 1 100 1 11 7 12 1 1 1 10 12 1 10 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 100 12 1 1 11 12 1 10 12 10 12 1 8 1 8 1 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 7 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 100 1 1 16 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/security/ProtectionDomain filePermCompatInPD Z 0
ciInstanceKlass java/security/CodeSource 1 1 398 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 10 7 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 8 1 8 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 12 1 10 12 10 12 1 1 10 100 12 1 1 10 12 1 7 1 10 12 10 100 12 1 1 1 10 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 7 1 8 1 8 1 10 10 12 1 1 10 100 12 1 1 1 7 1 10 12 10 12 1 1 11 7 12 1 1 10 10 12 1 11 10 12 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 11 12 1 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Boolean 1 1 152 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 8 1 10 7 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 9 100 12 1 1 9 12 10 100 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
staticfield java/lang/Boolean TRUE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean FALSE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Comparable 1 0 12 100 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/constant/Constable 1 0 11 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/util/Map 1 1 263 11 7 12 1 1 1 11 12 1 1 10 100 12 1 1 11 12 1 1 11 7 12 1 1 1 11 100 12 1 1 1 11 12 1 1 7 1 11 12 1 11 12 1 100 1 100 1 10 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 11 12 1 10 12 1 1 11 12 1 11 100 12 1 9 7 12 1 1 1 7 1 10 12 7 1 7 1 10 12 1 7 1 10 7 1 11 12 1 11 12 1 1 11 12 1 1 7 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Class 1 1 1698 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 7 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 18 12 1 1 11 7 12 1 1 1 8 1 8 1 8 1 10 7 12 1 1 1 11 12 1 1 8 1 10 12 1 10 11 100 12 1 1 1 11 7 12 1 1 1 11 8 1 18 8 1 10 12 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 7 12 1 10 12 1 1 10 7 1 7 1 10 12 1 1 9 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 7 1 7 1 10 10 12 1 1 10 12 1 1 100 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 100 1 10 12 1 10 12 1 10 12 1 1 10 9 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 7 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 9 100 12 1 1 1 9 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 1 10 10 10 12 1 1 10 12 1 1 10 12 10 10 12 1 1 7 1 8 1 10 10 12 1 1 10 12 1 100 1 11 12 1 10 100 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 7 1 9 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 11 7 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 100 1 7 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 10 12 7 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 10 12 1 1 100 1 10 8 1 10 12 1 11 11 12 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 10 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 9 12 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 100 12 1 1 10 100 12 1 1 9 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 100 1 10 10 12 1 1 7 1 10 12 1 1 100 11 7 1 9 12 1 1 9 12 1 7 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 7 1 10 10 12 1 1 10 10 12 1 10 12 10 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 8 10 7 8 1 18 8 1 8 1 10 12 1 9 12 1 9 12 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 7 1 10 10 12 1 10 7 1 9 12 1 8 1 10 12 1 7 1 10 12 1 10 12 1 1 100 1 7 1 9 12 1 100 1 8 1 10 10 7 12 1 1 1 10 12 11 7 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 12 1 1 9 100 12 1 1 11 12 7 1 11 7 12 1 1 9 12 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 9 12 1 9 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 12 1 100 1 11 12 1 10 100 12 1 1 1 10 12 1 11 12 1 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 11 12 1 11 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 100 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 100 1 10 12 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 18 12 1 1 11 12 1 1 18 11 12 1 18 12 1 11 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 8 1 10 12 1 7 1 9 12 1 1 7 1 7 1 7 1 7 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 11 12 16 1 16 15 16 15 10 12 16 16 15 10 12 16 15 16 1 15 10 12 16 15 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 100 1 100 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Class EMPTY_CLASS_ARRAY [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/Class serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/reflect/AnnotatedElement 1 1 164 11 7 12 1 1 1 11 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 11 12 1 1 11 7 12 1 1 10 7 12 1 1 1 10 12 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 18 12 1 18 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 7 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 16 15 16 1 16 1 15 11 12 16 16 1 15 10 100 12 1 1 1 16 1 15 10 100 12 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/invoke/TypeDescriptor 1 0 17 100 1 100 1 1 1 1 1 1 100 1 100 1 1 1 1
ciInstanceKlass java/lang/reflect/GenericDeclaration 1 0 30 7 1 7 1 7 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1
ciInstanceKlass java/lang/reflect/Type 1 1 17 11 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/TypeDescriptor$OfField 1 0 21 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StringBuilder 1 1 422 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 100 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 7 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StringBuilder
instanceKlass java/lang/StringBuffer
ciInstanceKlass java/lang/AbstractStringBuilder 1 1 605 7 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 7 1 3 3 10 12 1 10 12 1 1 11 7 1 100 1 7 1 10 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 8 1 10 10 12 1 1 100 1 10 12 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 100 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 10 12 10 12 1 10 10 10 12 1 10 5 0 10 10 12 1 1 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 100 1 10 12 100 1 10 100 1 10 7 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 7 1 1 16 1 15 10 12 16 15 10 12 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/AbstractStringBuilder EMPTYVALUE [B 0
ciInstanceKlass java/lang/Appendable 1 0 14 100 1 100 1 1 1 1 100 1 1 1 1 1
ciInstanceKlass java/lang/CharSequence 1 1 131 11 7 12 1 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 100 12 1 1 1 11 12 1 1 11 7 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 100 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 11 12 16 15 11 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/AutoCloseable 1 0 12 100 1 100 1 1 1 1 100 1 1 1
ciInstanceKlass java/io/Closeable 1 0 14 100 1 100 1 100 1 1 1 1 100 1 1 1
instanceKlass org/eclipse/core/runtime/CoreException
instanceKlass org/osgi/service/prefs/BackingStoreException
instanceKlass org/apache/felix/scr/impl/inject/methods/SuitableMethodNotAccessibleException
instanceKlass org/xml/sax/SAXException
instanceKlass javax/xml/parsers/ParserConfigurationException
instanceKlass java/util/concurrent/ExecutionException
instanceKlass java/lang/CloneNotSupportedException
instanceKlass org/osgi/service/resolver/ResolutionException
instanceKlass java/security/GeneralSecurityException
instanceKlass org/eclipse/osgi/container/ModuleContainer$ResolutionLockException
instanceKlass java/security/PrivilegedActionException
instanceKlass java/lang/InterruptedException
instanceKlass org/osgi/framework/InvalidSyntaxException
instanceKlass org/osgi/framework/BundleException
instanceKlass sun/nio/fs/WindowsException
instanceKlass java/net/URISyntaxException
instanceKlass java/lang/instrument/UnmodifiableClassException
instanceKlass java/io/IOException
instanceKlass java/lang/ReflectiveOperationException
instanceKlass java/lang/RuntimeException
ciInstanceKlass java/lang/Exception 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/Exception
instanceKlass java/lang/Error
ciInstanceKlass java/lang/Throwable 1 1 404 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 8 1 10 100 12 1 1 10 10 12 1 100 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 8 1 9 7 12 1 1 1 10 12 1 1 100 1 10 12 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 100 1 10 10 7 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 8 1 8 1 9 12 1 1 10 12 1 1 100 1 10 11 12 1 8 1 8 1 10 7 12 1 1 8 1 10 12 1 8 1 100 1 10 12 1 9 12 1 1 10 12 1 10 100 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 10 12 1 1 7 1 10 100 12 1 1 1 10 12 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 8 1 10 12 1 1 8 1 10 10 9 100 12 1 1 1 8 1 10 12 1 1 11 10 100 1 8 1 10 11 12 1 1 8 1 9 12 1 10 100 12 1 1 11 9 12 1 1 11 12 1 1 100 10 12 1 10 12 1 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Throwable UNASSIGNED_STACK [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
staticfield java/lang/Throwable SUPPRESSED_SENTINEL Ljava/util/List; java/util/Collections$EmptyList
staticfield java/lang/Throwable EMPTY_THROWABLE_ARRAY [Ljava/lang/Throwable; 0 [Ljava/lang/Throwable;
staticfield java/lang/Throwable $assertionsDisabled Z 1
ciMethod java/lang/Throwable fillInStackTrace ()Ljava/lang/Throwable; 512 0 593 0 -1
instanceKlass org/eclipse/osgi/util/NLS$MessagesProperties
instanceKlass java/security/Provider
ciInstanceKlass java/util/Properties 1 1 690 10 7 12 1 1 1 100 1 10 7 12 1 1 7 1 10 12 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 7 1 10 12 10 12 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 3 10 10 100 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 100 1 9 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 9 12 1 1 7 1 7 1 10 12 1 7 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 11 12 1 10 12 1 1 8 1 10 12 1 10 100 12 1 1 10 12 1 100 1 10 10 12 1 10 12 1 100 1 10 10 12 1 1 10 7 12 1 1 9 100 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 100 1 10 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 1 100 1 10 10 12 1 11 7 12 1 1 10 7 12 1 1 1 8 1 10 100 12 1 1 11 11 7 1 8 1 10 100 1 11 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 10 11 12 1 4 11 10 12 1 1 10 100 12 1 1 11 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 100 12 1 1 1 100 1 6 0 10 12 1 1 11 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 100 1 1
staticfield java/util/Properties UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass org/apache/felix/scr/impl/helper/ReadOnlyDictionary
instanceKlass org/osgi/framework/FrameworkUtil$MapAsDictionary
instanceKlass org/eclipse/osgi/internal/framework/EquinoxBundle$SystemBundle$SystemBundleHeaders
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$UnmodifiableDictionary
instanceKlass org/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap
instanceKlass org/eclipse/osgi/storage/BundleInfo$CachedManifest
instanceKlass java/util/Hashtable
ciInstanceKlass java/util/Dictionary 1 1 36 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/Properties
ciInstanceKlass java/util/Hashtable 1 1 516 7 1 10 7 12 1 1 1 9 7 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 9 12 1 1 7 1 9 12 1 1 4 10 7 12 1 1 1 9 12 1 4 10 12 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 100 1 10 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 12 1 3 9 12 1 9 12 1 3 10 12 1 10 12 1 10 12 1 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 100 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 9 12 1 1 10 100 1 7 1 10 12 1 10 8 1 10 10 12 1 8 1 10 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 7 1 10 100 1 10 10 12 1 1 11 12 1 1 11 12 1 7 1 10 10 10 100 12 1 1 11 100 12 1 1 1 100 1 10 11 100 12 1 1 11 100 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 8 1 10 4 4 10 12 1 1 10 12 1 8 1 4 10 12 10 100 12 1 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/String 1 1 1443 10 7 12 1 1 1 8 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 10 7 12 1 1 1 10 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 10 12 9 7 12 1 1 10 12 1 1 3 10 12 1 1 7 1 11 12 1 1 11 12 1 11 12 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 11 12 1 1 10 12 1 1 10 12 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 100 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 10 7 12 1 1 11 12 1 11 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 3 3 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 10 12 1 100 1 10 10 12 1 1 10 12 1 1 10 7 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 11 7 1 11 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 1 10 12 10 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 10 12 1 10 12 10 10 12 10 10 12 1 10 12 1 10 10 12 10 7 12 1 1 1 10 12 10 10 12 10 12 1 10 12 10 12 10 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 10 7 12 1 1 1 11 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 7 1 8 1 10 10 10 12 1 10 12 1 1 8 1 10 12 1 3 3 10 12 1 10 12 1 1 10 12 7 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 10 12 10 12 1 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 1 10 10 12 1 8 1 10 12 1 1 18 12 1 1 11 100 12 1 1 1 7 1 3 18 12 1 18 12 1 8 1 10 100 12 1 1 1 11 12 1 1 10 12 10 10 12 1 10 11 12 1 1 10 12 1 1 11 12 1 18 3 11 10 12 1 11 11 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 11 100 12 1 100 1 100 1 10 12 100 1 10 10 100 12 1 1 1 100 1 10 7 1 10 10 12 1 10 10 12 1 8 1 10 10 12 1 8 1 8 1 10 12 1 10 12 1 10 10 12 10 7 12 1 1 10 7 12 1 1 10 7 12 1 1 8 1 10 12 1 10 12 1 10 9 12 1 10 12 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 10 10 12 10 12 7 1 9 12 1 1 7 1 10 7 1 7 1 7 1 7 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 10 12 15 10 12 15 10 12 15 10 100 12 1 1 1 1 1 1 1 100 1 100 1 1 1
staticfield java/lang/String COMPACT_STRINGS Z 1
staticfield java/lang/String serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/String CASE_INSENSITIVE_ORDER Ljava/util/Comparator; java/lang/String$CaseInsensitiveComparator
ciInstanceKlass java/lang/constant/ConstantDesc 1 0 37 100 1 100 1 1 1 1 100 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/InternalError 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/AssertionError
instanceKlass java/lang/VirtualMachineError
instanceKlass java/lang/LinkageError
ciInstanceKlass java/lang/Error 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StackOverflowError
instanceKlass java/lang/OutOfMemoryError
instanceKlass java/lang/InternalError
ciInstanceKlass java/lang/VirtualMachineError 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Iterator 1 1 53 100 1 8 1 10 12 1 1 10 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/FileInputStream 1 1 298 7 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 7 1 10 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 100 1 10 10 12 1 1 100 1 8 1 10 7 1 10 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 7 12 1 1 1 10 12 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 7 1 5 0 8 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 10 12 1 10 12 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 8 1 10 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 100 1 5 0 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 9 12 1 1 10 12 1 7 1 10 12 1 10 12 1 100 1 10 10 100 12 1 1 7 1 10 12 1 10 12 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/eclipse/core/internal/registry/BufferedRandomInputStream
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLEntityManager$RewindableInputStream
instanceKlass org/eclipse/osgi/storage/url/reference/ReferenceInputStream
instanceKlass java/util/jar/JarVerifier$VerifierStream
instanceKlass java/util/zip/ZipFile$ZipFileInputStream
instanceKlass java/io/FilterInputStream
instanceKlass java/io/FileInputStream
instanceKlass java/io/ByteArrayInputStream
ciInstanceKlass java/io/InputStream 1 1 195 7 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 100 1 3 10 12 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 3 7 1 8 1 10 10 7 12 1 1 1 7 1 10 11 7 12 1 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 10 7 12 1 1 1 5 0 10 12 1 10 12 1 1 100 1 10 8 1 10 8 1 8 1 10 12 1 1 10 7 12 1 1 1 7 1 5 0 10 12 1 100 1 7 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/io/FileInputStream available ()I 48 0 24 0 0
ciInstanceKlass java/io/BufferedInputStream 1 1 256 9 7 12 1 1 1 100 1 8 1 10 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 7 12 1 1 9 12 1 100 1 8 1 10 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 9 12 1 10 7 12 1 1 1 9 12 1 10 100 12 1 1 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 10 12 1 100 1 10 12 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 7 1 3 10 12 1 1 10 12 1 8 1 10 12 1 8 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 100 1 7 1 5 0 10 10 12 1 1 8 10 12 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/io/BufferedInputStream EMPTY [B 0
staticfield java/io/BufferedInputStream U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/io/BufferedInputStream BUF_OFFSET J 40
instanceKlass org/eclipse/osgi/storage/bundlefile/CloseableBundleFile$BundleEntryInputStream
instanceKlass java/io/DataInputStream
instanceKlass org/eclipse/osgi/framework/internal/reliablefile/ReliableFileInputStream
instanceKlass sun/net/www/protocol/jar/JarURLConnection$JarURLInputStream
instanceKlass java/util/jar/Manifest$FastInputStream
instanceKlass java/util/zip/InflaterInputStream
instanceKlass java/io/BufferedInputStream
ciInstanceKlass java/io/FilterInputStream 1 1 62 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/io/BufferedInputStream read ([BII)I 1024 0 6784 0 -1
ciInstanceKlass jdk/internal/misc/Unsafe 1 1 1287 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 5 0 5 0 5 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 7 1 8 1 10 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 9 12 1 100 1 10 10 12 1 1 8 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 1 9 7 1 9 7 1 9 7 1 9 9 7 1 9 7 1 9 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 5 0 5 0 9 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 3 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 100 1 10 9 12 1 5 0 10 12 1 1 5 0 10 12 1 5 0 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 5 0 5 0 5 0 10 12 1 1 10 12 1 10 12 1 10 12 10 100 12 1 1 8 1 100 1 11 12 1 1 8 1 11 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 12 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 10 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/Unsafe theUnsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_INT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_INT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ADDRESS_SIZE I 8
instanceKlass org/eclipse/osgi/internal/loader/ModuleClassLoader
instanceKlass org/eclipse/osgi/internal/framework/ContextFinder
instanceKlass org/eclipse/osgi/internal/framework/EquinoxContainer$1
instanceKlass lombok/launch/ShadowClassLoader
instanceKlass jdk/internal/reflect/DelegatingClassLoader
instanceKlass java/security/SecureClassLoader
ciInstanceKlass java/lang/ClassLoader 1 1 1108 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 7 12 1 10 7 1 10 7 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 8 1 10 12 1 10 12 1 100 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 12 1 10 12 1 1 8 1 8 1 10 7 12 1 1 100 1 10 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 12 1 10 7 1 10 12 1 100 1 18 12 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 10 12 1 100 1 10 12 1 8 1 10 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 8 1 100 1 10 10 12 1 9 12 1 10 7 12 1 1 10 12 1 7 1 8 1 10 12 1 10 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 100 1 10 12 1 1 7 1 7 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 7 1 18 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 18 12 1 11 100 12 1 1 1 100 1 10 12 1 1 10 12 1 10 11 12 1 1 10 18 10 12 1 1 11 7 12 1 18 12 1 11 12 1 1 10 12 10 12 1 1 10 12 1 1 100 1 8 1 10 10 12 1 8 1 8 1 10 100 12 1 1 10 12 1 100 1 10 10 12 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 11 7 12 1 1 100 1 10 11 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 9 12 1 1 9 12 9 12 1 9 12 1 9 12 1 8 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 11 12 1 1 10 100 12 1 1 1 100 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 10 12 16 1 16 15 10 12 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/ClassLoader nocerts [Ljava/security/cert/Certificate; 0 [Ljava/security/cert/Certificate;
staticfield java/lang/ClassLoader $assertionsDisabled Z 1
ciInstanceKlass java/lang/reflect/Constructor 1 1 439 10 100 12 1 1 1 10 100 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 100 1 8 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 7 12 1 1 10 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
instanceKlass java/lang/reflect/Executable
instanceKlass java/lang/reflect/Field
ciInstanceKlass java/lang/reflect/AccessibleObject 1 1 400 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 7 1 10 7 12 1 1 1 11 12 1 7 1 10 12 1 7 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 7 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 100 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 7 1 100 1 8 1 10 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 1 8 1 10 11 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 100 1 10 12 1 7 1 10 12 1 10 12 1 1 10 100 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 100 12 1 1 8 1 10 100 12 1 1 1 8 1 10 7 12 1 1 1 9 12 1 7 1 10 7 1 10 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 7 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/reflect/AccessibleObject reflectionFactory Ljdk/internal/reflect/ReflectionFactory; jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/reflect/Constructor
instanceKlass java/lang/reflect/Method
ciInstanceKlass java/lang/reflect/Executable 1 1 581 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 8 1 8 1 8 1 10 100 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 12 1 7 1 8 1 10 12 1 8 1 11 100 12 1 1 1 7 1 11 7 12 1 1 1 11 12 1 8 1 18 8 1 10 12 1 10 12 1 1 18 8 1 10 12 1 100 1 10 12 1 10 12 1 11 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 10 12 1 8 1 10 12 1 10 12 1 3 100 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 8 1 8 1 8 1 9 12 1 1 9 12 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 10 10 10 10 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 9 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 16 15 16 1 16 1 15 10 12 16 15 10 100 12 1 1 1 1 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/reflect/Member 1 1 37 100 1 10 12 1 1 100 1 100 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/TimerThread
instanceKlass java/util/logging/LogManager$Cleaner
instanceKlass org/eclipse/osgi/framework/eventmgr/EventManager$EventThread
instanceKlass org/eclipse/equinox/launcher/Main$SplashHandler
instanceKlass java/util/concurrent/ForkJoinWorkerThread
instanceKlass jdk/internal/misc/InnocuousThread
instanceKlass java/lang/ref/Finalizer$FinalizerThread
instanceKlass java/lang/ref/Reference$ReferenceHandler
instanceKlass java/lang/BaseVirtualThread
ciInstanceKlass java/lang/Thread 1 1 870 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 10 12 1 10 100 12 1 1 100 1 8 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 9 12 1 1 10 12 1 7 1 10 12 1 100 1 8 1 10 9 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 3 8 1 7 1 5 0 10 7 12 1 1 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 1 8 1 10 7 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 8 1 9 7 12 1 1 9 12 1 1 5 0 100 1 10 100 1 10 100 1 10 7 1 10 8 1 10 12 1 1 10 7 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 7 1 9 12 1 1 100 1 10 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 1 10 10 12 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 10 12 1 10 12 1 100 1 10 10 12 9 12 1 1 10 12 1 11 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 10 10 12 1 10 12 1 1 9 12 1 9 12 10 12 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 8 1 10 9 12 1 10 12 1 7 1 8 1 10 10 12 1 8 1 10 12 1 1 9 12 10 12 8 1 10 10 12 1 10 12 1 8 1 10 12 1 10 8 1 10 100 12 1 1 10 12 1 1 100 1 8 1 10 9 12 1 9 12 1 1 10 12 1 1 10 10 12 1 10 12 1 100 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 8 1 9 12 1 10 12 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Thread NEW_THREAD_BINDINGS Ljava/lang/Object; java/lang/Class
staticfield java/lang/Thread EMPTY_STACK_TRACE [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
ciInstanceKlass java/lang/Runnable 1 0 11 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/net/URL 1 1 771 10 7 12 1 1 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 7 1 10 10 12 1 1 8 1 10 12 1 1 9 12 1 7 1 8 1 10 12 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 9 12 1 8 1 9 12 1 10 12 1 1 8 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 8 1 10 12 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 10 100 1 10 10 12 1 8 1 10 7 12 1 1 1 10 12 1 9 100 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 1 10 12 1 10 12 1 1 8 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 100 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 1 1 7 1 8 1 10 10 12 1 9 12 1 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 12 1 1 10 12 1 8 1 8 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 9 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 8 1 7 1 10 10 7 12 1 1 1 10 12 1 8 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 10 8 8 10 12 1 8 8 8 100 1 10 12 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 100 1 8 1 10 10 10 12 1 1 10 12 1 10 12 1 1 8 1 7 1 10 10 7 1 10 12 1 9 7 12 1 1 1 9 12 1 1 7 1 10 10 7 12 1 1 1 7 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/net/URL defaultFactory Ljava/net/URLStreamHandlerFactory; java/net/URL$DefaultFactory
staticfield java/net/URL streamHandlerLock Ljava/lang/Object; java/lang/Object
staticfield java/net/URL serialPersistentFields [Ljava/io/ObjectStreamField; 7 [Ljava/io/ObjectStreamField;
ciMethod java/lang/System arraycopy (Ljava/lang/Object;ILjava/lang/Object;II)V 256 0 128 0 -1
ciInstanceKlass java/lang/Module 1 1 1070 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 12 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 1 10 100 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 10 12 1 10 7 12 1 1 8 1 8 1 10 8 1 8 1 9 12 1 1 8 1 10 100 12 1 1 1 10 12 1 9 12 1 1 11 12 1 9 7 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 10 12 1 1 9 12 1 9 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 1 10 12 1 1 11 12 1 9 12 1 11 12 10 100 12 1 1 100 1 8 1 10 11 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 11 12 1 1 11 7 12 1 1 11 12 1 1 9 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 10 12 10 7 12 1 1 10 7 1 18 12 1 1 11 100 12 1 1 1 18 12 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 10 7 12 1 1 7 1 11 12 1 7 1 7 1 10 12 1 10 7 12 1 1 1 10 11 7 12 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 7 1 10 12 1 10 11 12 1 1 10 12 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 11 7 1 10 12 1 1 11 12 1 10 10 12 1 11 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 18 12 1 11 12 1 18 12 1 10 12 1 10 12 1 10 12 7 1 10 12 1 10 12 1 10 12 1 9 12 1 7 1 10 10 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 18 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 7 1 10 12 1 1 7 1 8 1 10 12 1 1 100 1 11 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 100 1 10 12 1 10 12 1 1 7 1 7 1 10 12 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 7 12 1 1 8 1 18 12 1 1 100 1 100 1 9 12 1 1 9 12 1 9 12 1 11 100 12 1 1 1 100 1 11 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 10 12 1 8 1 10 10 100 12 1 1 7 1 10 10 12 1 10 7 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 11 12 1 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 16 15 10 16 1 15 10 12 16 1 15 10 12 16 1 16 15 10 12 16 16 1 15 10 12 16 15 10 7 12 1 1 1 15 10 100 12 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Module ALL_UNNAMED_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module ALL_UNNAMED_MODULE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module EVERYONE_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module EVERYONE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module $assertionsDisabled Z 1
ciInstanceKlass java/lang/Math 1 1 460 7 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 6 0 6 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 100 1 3 3 3 10 7 12 1 1 1 100 1 5 0 5 0 5 0 5 0 5 0 9 100 12 1 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 1 1 10 12 1 1 7 1 5 0 5 0 7 1 3 5 0 3 5 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 10 12 1 1 9 12 1 1 9 12 1 100 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 6 0 10 12 1 9 12 1 1 100 1 10 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 6 0 10 12 1 1 10 12 10 12 1 4 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 6 0 4 6 0 4 6 0 4 10 12 1 9 12 1 1 10 12 9 12 1 10 7 12 1 1 1 4 6 0 1 1 6 0 1 6 0 1 6 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Math negativeZeroFloatBits J -2147483648
staticfield java/lang/Math negativeZeroDoubleBits J -9223372036854775808
staticfield java/lang/Math $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/util/ArraysSupport 1 1 378 7 1 7 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 12 9 12 1 10 12 1 1 10 12 7 1 10 12 1 1 10 12 1 100 1 10 12 1 1 10 12 100 1 10 12 1 100 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 9 12 1 1 11 100 12 1 1 1 9 12 1 9 12 1 10 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 9 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 7 12 1 1 1 9 12 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 1 3 10 12 1 7 1 8 1 8 1 8 1 10 10 100 12 1 1 1 11 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 7 1 10 7 12 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
staticfield jdk/internal/util/ArraysSupport U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/util/ArraysSupport BIG_ENDIAN Z 0
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_BOOLEAN_INDEX_SCALE I 0
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_BYTE_INDEX_SCALE I 0
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_CHAR_INDEX_SCALE I 1
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_SHORT_INDEX_SCALE I 1
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_INT_INDEX_SCALE I 2
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_LONG_INDEX_SCALE I 3
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_FLOAT_INDEX_SCALE I 2
staticfield jdk/internal/util/ArraysSupport LOG2_ARRAY_DOUBLE_INDEX_SCALE I 3
staticfield jdk/internal/util/ArraysSupport LOG2_BYTE_BIT_SIZE I 3
staticfield jdk/internal/util/ArraysSupport JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
ciInstanceKlass java/lang/Character 1 1 604 7 1 7 1 100 1 9 12 1 1 8 1 9 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 3 3 3 3 3 10 12 1 1 10 12 1 3 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 3 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 10 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 5 0 10 12 1 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 10 12 1 9 12 1 1 100 1 10 10 12 1 10 12 1 1 3 10 100 12 1 1 1 10 12 1 10 100 12 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 9 100 12 1 1 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 10 12 1 1 7 1 8 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 3 1 1 3 1 1 1 1 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/lang/Character TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Character $assertionsDisabled Z 1
ciInstanceKlass java/lang/OutOfMemoryError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Integer 1 1 453 7 1 7 1 7 1 7 1 10 12 1 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 7 1 10 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 3 10 12 1 1 3 10 12 1 1 10 12 1 1 10 7 12 1 1 1 11 7 1 10 12 1 1 11 10 12 1 1 8 1 10 12 1 1 8 1 7 1 10 12 1 1 10 12 1 1 5 0 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 9 12 1 1 9 12 1 1 10 12 1 10 7 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 5 0 3 3 3 3 10 12 1 10 12 1 3 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 3 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Integer TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Integer digits [C 36
staticfield java/lang/Integer DigitTens [B 100
staticfield java/lang/Integer DigitOnes [B 100
instanceKlass java/math/BigInteger
instanceKlass java/util/concurrent/atomic/AtomicLong
instanceKlass java/util/concurrent/atomic/AtomicInteger
instanceKlass java/lang/Long
instanceKlass java/lang/Integer
instanceKlass java/lang/Short
instanceKlass java/lang/Byte
instanceKlass java/lang/Double
instanceKlass java/lang/Float
ciInstanceKlass java/lang/Number 1 1 37 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Thread$FieldHolder 1 1 48 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/Thread$Constants 0 0 59 7 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 7 1 8 1 10 12 1 9 7 12 1 1 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ThreadGroup 1 1 411 10 7 12 1 1 1 9 7 12 1 1 1 8 1 9 12 1 1 7 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 18 12 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 11 12 1 1 11 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 10 12 1 11 12 1 11 12 1 1 100 1 10 10 12 1 100 1 10 18 12 1 1 11 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 11 12 10 12 1 1 10 12 1 1 11 7 1 9 12 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 8 1 10 8 1 10 12 1 10 12 1 8 1 9 12 1 1 9 12 1 10 100 12 1 1 1 100 9 12 1 1 7 1 9 12 1 10 12 10 12 1 1 100 10 12 9 12 1 10 12 1 100 1 10 11 12 1 1 7 1 10 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/ThreadGroup $assertionsDisabled Z 1
ciInstanceKlass java/lang/Thread$UncaughtExceptionHandler 1 0 16 100 1 100 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/security/AccessControlContext 1 1 374 9 7 12 1 1 1 9 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 10 12 1 11 7 12 1 1 1 11 12 1 11 12 1 11 12 1 1 7 1 11 12 1 1 10 12 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 7 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 7 12 1 1 1 10 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 100 1 10 12 1 10 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 10 12 1 10 12 1 1 10 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1
instanceKlass java/lang/ThreadBuilders$BoundVirtualThread
instanceKlass java/lang/VirtualThread
ciInstanceKlass java/lang/BaseVirtualThread 0 0 36 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 1
ciInstanceKlass java/lang/VirtualThread 0 0 907 9 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 100 1 10 12 1 9 12 1 1 18 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 10 12 1 10 12 1 10 12 1 10 12 1 11 100 12 1 1 1 100 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 100 1 10 10 12 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 9 12 1 1 9 12 1 100 1 10 10 12 1 10 100 12 1 1 10 9 10 10 12 1 1 10 12 1 1 10 100 12 1 1 10 100 1 10 9 10 10 12 1 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 10 12 1 10 12 1 9 12 1 1 10 12 1 10 12 1 10 12 1 11 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 100 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 9 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 7 1 9 12 1 1 10 7 12 1 1 10 9 12 1 1 18 9 100 12 1 1 1 11 100 12 1 1 1 11 100 1 11 12 10 12 1 10 12 1 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 11 100 12 1 1 10 12 9 100 12 1 1 1 9 12 1 10 12 1 1 9 12 1 9 12 1 9 12 1 7 1 10 10 12 1 1 10 12 1 10 12 7 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 8 1 10 10 12 1 10 12 1 10 7 12 1 1 8 1 8 1 10 9 100 12 1 1 1 10 12 1 1 10 12 1 10 10 10 12 9 12 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 10 12 1 1 18 12 1 1 18 12 1 10 7 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 18 12 1 10 100 12 1 1 1 100 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 100 12 1 1 8 1 10 12 1 8 1 8 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 18 12 1 1 18 12 1 1 5 0 9 12 1 10 12 1 18 12 1 100 1 10 12 10 7 12 1 1 10 12 1 1 7 1 8 1 10 10 12 1 10 12 1 1 10 12 1 9 12 1 8 10 12 1 1 8 8 9 12 1 8 10 12 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 15 16 15 10 12 16 15 10 12 16 16 15 10 12 16 15 10 12 16 15 10 12 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 7 1 1 100 1 100 1 1
ciInstanceKlass java/lang/ThreadBuilders$BoundVirtualThread 0 0 132 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 9 100 12 1 1 1 10 12 1 1 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/ContinuationScope 0 0 50 10 100 12 1 1 1 10 100 12 1 1 1 100 1 9 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/StackChunk 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Float 1 1 279 7 1 7 1 10 100 12 1 1 1 10 100 12 1 1 1 4 7 1 10 12 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 100 1 4 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 3 3 100 1 4 4 4 3 10 12 1 1 9 12 1 1 100 1 10 3 3 4 4 10 12 1 3 3 3 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 4 1 4 1 1 1 4 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Float TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Float $assertionsDisabled Z 1
ciInstanceKlass java/lang/Double 1 1 290 7 1 7 1 10 100 12 1 1 1 10 12 1 1 10 7 1 10 12 1 1 10 100 12 1 1 1 6 0 8 1 10 12 1 1 8 1 10 12 1 1 8 1 6 0 10 12 1 1 100 1 5 0 5 0 8 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 1 6 0 10 7 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 6 0 1 6 0 1 6 0 1 1 1 6 0 1 1 3 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Double TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Byte 1 1 213 7 1 100 1 10 7 12 1 1 1 9 12 1 1 8 1 9 12 1 1 7 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Byte TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Short 1 1 222 7 1 7 1 100 1 10 7 12 1 1 1 10 12 1 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 8 1 9 12 1 1 7 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 3 3 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Short TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Long 1 1 524 7 1 7 1 7 1 7 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 10 12 1 10 12 1 10 12 1 5 0 5 0 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 5 0 5 0 9 12 1 1 9 12 1 5 0 100 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 5 0 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 7 1 10 12 1 1 11 10 12 1 1 8 1 10 12 1 1 8 1 7 1 10 12 1 1 10 12 1 8 1 8 1 11 12 1 1 10 12 1 10 12 1 10 12 1 5 0 5 0 9 7 12 1 1 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 7 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 5 0 10 12 1 10 12 1 5 0 5 0 5 0 10 12 1 1 10 12 1 5 0 5 0 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 3 1 3 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Long TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass jdk/internal/vm/vector/VectorSupport 0 0 573 100 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 9 12 1 1 10 100 12 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask
instanceKlass jdk/internal/vm/vector/VectorSupport$Vector
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$Vector 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/FillerObject 0 0 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/PhantomReference
instanceKlass java/lang/ref/FinalReference
instanceKlass java/lang/ref/WeakReference
instanceKlass java/lang/ref/SoftReference
ciInstanceKlass java/lang/ref/Reference 1 1 190 9 7 12 1 1 1 9 7 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 7 1 8 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 7 1 100 1 10 12 9 12 1 9 12 1 100 1 10 10 12 1 10 10 7 12 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 7 1 1 1
staticfield java/lang/ref/Reference processPendingLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Reference $assertionsDisabled Z 1
instanceKlass org/eclipse/core/internal/registry/ReferenceMap$SoftRef
instanceKlass sun/util/locale/provider/LocaleResources$ResourceReference
instanceKlass sun/util/resources/Bundles$BundleReference
instanceKlass java/util/ResourceBundle$BundleReference
instanceKlass sun/util/locale/LocaleObjectCache$CacheEntry
instanceKlass sun/security/util/MemoryCache$SoftCacheEntry
instanceKlass java/lang/invoke/LambdaFormEditor$Transform
ciInstanceKlass java/lang/ref/SoftReference 1 1 47 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1
instanceKlass java/util/ResourceBundle$KeyElementReference
instanceKlass java/util/logging/LogManager$LoggerWeakRef
instanceKlass java/util/logging/Level$KnownLevel
instanceKlass org/eclipse/osgi/internal/container/KeyBasedLockStore$LockWeakRef
instanceKlass sun/nio/ch/FileLockTable$FileLockReference
instanceKlass java/lang/ClassValue$Entry
instanceKlass java/lang/ThreadLocal$ThreadLocalMap$Entry
instanceKlass java/lang/WeakPairMap$WeakRefPeer
instanceKlass jdk/internal/util/WeakReferenceKey
instanceKlass java/util/WeakHashMap$Entry
ciInstanceKlass java/lang/ref/WeakReference 1 1 31 10 7 12 1 1 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/Finalizer
ciInstanceKlass java/lang/ref/FinalReference 1 1 50 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 7 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1
instanceKlass jdk/internal/ref/PhantomCleanable
instanceKlass jdk/internal/ref/Cleaner
ciInstanceKlass java/lang/ref/PhantomReference 1 1 39 10 100 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ref/Finalizer 1 1 155 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 10 12 1 7 1 8 1 10 12 1 10 12 1 1 9 12 1 100 1 10 12 1 7 1 11 100 12 1 1 10 12 1 7 1 10 12 1 100 1 10 12 1 10 7 12 1 1 1 10 100 12 1 1 1 100 1 10 10 12 1 7 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 10 7 1 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Finalizer lock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Finalizer ENABLED Z 1
staticfield java/lang/ref/Finalizer $assertionsDisabled Z 1
instanceKlass org/apache/felix/scr/impl/inject/ValueUtils$ValueType
instanceKlass org/apache/felix/scr/impl/manager/RegistrationManager$RegState
instanceKlass org/apache/felix/scr/impl/manager/AbstractComponentManager$State
instanceKlass org/osgi/util/promise/PromiseFactory$Option
instanceKlass org/apache/felix/scr/impl/metadata/ReferenceMetadata$ReferenceScope
instanceKlass org/apache/felix/scr/impl/metadata/ServiceMetadata$Scope
instanceKlass org/apache/felix/scr/impl/metadata/DSVersion
instanceKlass com/sun/org/apache/xerces/internal/impl/XMLScanner$NameType
instanceKlass com/sun/org/apache/xerces/internal/util/Status
instanceKlass javax/xml/catalog/CatalogFeatures$Feature
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager$Property
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager$State
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityManager$NameMap
instanceKlass jdk/xml/internal/JdkProperty$State
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityManager$Limit
instanceKlass org/apache/felix/scr/impl/logger/InternalLogger$Level
instanceKlass java/nio/file/StandardCopyOption
instanceKlass sun/util/locale/provider/LocaleProviderAdapter$Type
instanceKlass java/lang/StackStreamFactory$WalkerState
instanceKlass java/lang/StackWalker$ExtendedOption
instanceKlass java/lang/StackWalker$Option
instanceKlass aQute/bnd/header/Attrs$Type
instanceKlass org/apache/aries/spifly/ProviderBundleTrackerCustomizer$DiscoveryMode
instanceKlass org/eclipse/osgi/container/ModuleContainerAdaptor$ContainerEvent
instanceKlass java/util/Locale$Category
instanceKlass org/eclipse/osgi/report/resolution/ResolutionReport$Entry$Type
instanceKlass java/util/Comparators$NaturalOrderComparator
instanceKlass org/eclipse/osgi/container/Module$StartOptions
instanceKlass org/apache/felix/resolver/PermutationType
instanceKlass org/eclipse/osgi/container/ModuleDatabase$Sort
instanceKlass org/eclipse/osgi/container/Module$Settings
instanceKlass org/eclipse/osgi/container/ModuleContainerAdaptor$ModuleEvent
instanceKlass org/eclipse/osgi/storage/ContentProvider$Type
instanceKlass org/eclipse/osgi/container/Module$State
instanceKlass org/osgi/service/log/LogLevel
instanceKlass sun/util/logging/PlatformLogger$Level
instanceKlass jdk/internal/logger/BootstrapLogger$LoggingBackend
instanceKlass java/lang/reflect/ProxyGenerator$PrimitiveTypeInfo
instanceKlass java/lang/annotation/RetentionPolicy
instanceKlass java/nio/file/AccessMode
instanceKlass java/nio/file/attribute/PosixFilePermission
instanceKlass jdk/internal/icu/util/CodePointTrie$ValueWidth
instanceKlass jdk/internal/icu/util/CodePointTrie$Type
instanceKlass java/text/Normalizer$Form
instanceKlass java/time/temporal/ChronoUnit
instanceKlass java/time/temporal/ChronoField
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraint$Operator
instanceKlass java/lang/System$Logger$Level
instanceKlass sun/security/rsa/RSAUtil$KeyType
instanceKlass sun/security/util/KnownOIDs
instanceKlass lombok/patcher/StackRequest
instanceKlass java/util/regex/Pattern$Qtype
instanceKlass java/lang/invoke/MethodHandleImpl$ArrayAccess
instanceKlass java/util/zip/ZipCoder$Comparison
instanceKlass java/nio/file/LinkOption
instanceKlass sun/nio/fs/WindowsPathType
instanceKlass java/nio/file/StandardOpenOption
instanceKlass java/util/stream/Collector$Characteristics
instanceKlass java/util/concurrent/TimeUnit
instanceKlass java/util/stream/StreamShape
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassOption
instanceKlass java/lang/invoke/VarHandle$AccessType
instanceKlass java/lang/invoke/VarHandle$AccessMode
instanceKlass java/lang/invoke/MethodHandleImpl$Intrinsic
instanceKlass java/lang/invoke/LambdaForm$BasicType
instanceKlass java/lang/invoke/LambdaForm$Kind
instanceKlass sun/invoke/util/Wrapper
instanceKlass java/util/stream/StreamOpFlag$Type
instanceKlass java/util/stream/StreamOpFlag
instanceKlass java/io/File$PathStatus
instanceKlass java/lang/module/ModuleDescriptor$Requires$Modifier
instanceKlass java/lang/reflect/AccessFlag$Location
instanceKlass java/lang/reflect/AccessFlag
instanceKlass java/lang/module/ModuleDescriptor$Modifier
instanceKlass java/lang/reflect/ClassFileFormatVersion
instanceKlass java/lang/Thread$State
ciInstanceKlass java/lang/Enum 1 1 204 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 7 12 1 1 1 100 1 10 10 12 1 1 10 12 1 7 1 10 10 7 12 1 1 10 12 1 1 18 12 1 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 1 100 1 8 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 7 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 100 1 7 1 1 100 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 15 10 100 12 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/reflect/Method 1 1 472 9 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 8 1 10 12 1 10 12 1 7 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 11 7 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 7 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/Field 1 1 457 9 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 7 1 10 7 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 10 12 1 8 1 8 1 10 11 7 1 9 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 12 1 1 11 7 1 10 12 1 7 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 9 100 12 1 1 10 100 12 1 1 1 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/reflect/Parameter 0 0 243 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 11 7 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 10 7 12 1 1 1 10 12 1 10 12 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 8 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 100 1 10 11 12 1 1 11 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/RecordComponent 0 0 196 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 100 12 1 1 9 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 11 7 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 9 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/StringBuffer 1 1 483 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 100 12 1 1 1 10 10 12 1 1 9 12 1 1 10 100 12 1 1 10 100 1 8 10 100 12 1 1 1 8 10 12 1 8 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 7 1 10 12 100 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 10 12 1 9 7 12 1 1 1 9 7 1 9 12 1 1 7 1 7 1 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/StringBuffer serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
instanceKlass jdk/internal/loader/BuiltinClassLoader
instanceKlass java/net/URLClassLoader
ciInstanceKlass java/security/SecureClassLoader 1 1 102 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 7 1 10 12 1 7 1 10 12 1 11 7 12 1 1 1 7 1 11 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
instanceKlass org/eclipse/equinox/launcher/Main$StartupClassLoader
ciInstanceKlass java/net/URLClassLoader 1 1 600 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 11 7 12 1 1 10 12 1 11 12 1 11 12 1 1 11 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 1 7 1 100 1 10 12 1 1 7 1 10 10 12 1 1 10 7 12 1 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 8 1 10 12 1 1 10 10 12 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 1 7 1 10 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 9 7 12 1 1 1 10 12 1 8 1 100 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 9 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 100 1 8 1 10 100 1 10 12 1 10 7 12 1 100 1 10 12 1 10 12 1 100 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 100 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/util/jar/Manifest 1 1 339 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 100 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 11 12 1 1 100 1 10 12 1 8 1 11 12 1 7 1 10 12 1 1 11 12 1 10 12 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 1 10 9 7 12 1 1 1 10 12 1 1 10 100 12 1 10 12 1 10 12 1 9 100 12 1 1 1 8 1 10 12 1 8 1 8 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 8 1 10 10 12 1 1 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 11 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 11 10 12 1 11 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/File 1 1 649 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 100 1 10 10 7 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 7 1 8 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 10 12 1 10 12 1 10 12 100 1 8 1 10 10 12 1 10 12 1 8 1 10 12 1 7 1 10 10 12 1 1 10 12 1 10 12 1 100 1 10 7 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 100 1 7 1 10 12 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 10 12 1 1 10 12 1 1 7 1 10 11 100 12 1 1 1 11 7 12 1 1 11 12 1 11 12 1 1 100 1 10 12 1 10 10 10 100 1 11 100 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 10 12 1 1 10 12 1 1 7 1 5 0 8 1 8 1 8 1 10 100 12 1 1 10 12 1 1 100 1 8 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 10 12 1 9 12 1 9 12 1 10 12 1 1 10 12 1 1 8 7 1 7 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 100 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/io/File FS Ljava/io/FileSystem; java/io/WinNTFileSystem
staticfield java/io/File separatorChar C 92
staticfield java/io/File separator Ljava/lang/String; "\"
staticfield java/io/File pathSeparatorChar C 59
staticfield java/io/File pathSeparator Ljava/lang/String; ";"
staticfield java/io/File UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/io/File PATH_OFFSET J 16
staticfield java/io/File PREFIX_LENGTH_OFFSET J 12
staticfield java/io/File $assertionsDisabled Z 1
ciMethod java/io/File toString ()Ljava/lang/String; 14 0 7 0 -1
ciInstanceKlass java/io/ByteArrayInputStream 1 1 117 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 3 10 100 1 10 100 12 1 1 1 9 12 1 1 100 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/io/ByteArrayInputStream $assertionsDisabled Z 1
instanceKlass java/nio/CharBuffer
instanceKlass java/nio/IntBuffer
instanceKlass java/nio/LongBuffer
instanceKlass java/nio/ByteBuffer
ciInstanceKlass java/nio/Buffer 1 1 256 100 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 1 100 1 8 1 10 12 1 8 1 8 1 9 12 10 12 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 100 1 10 100 1 10 100 1 10 9 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 10 12 1 10 100 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 10 12 1 1 7 1 10 10 7 12 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1
staticfield java/nio/Buffer UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/nio/Buffer SCOPED_MEMORY_ACCESS Ljdk/internal/misc/ScopedMemoryAccess; jdk/internal/misc/ScopedMemoryAccess
staticfield java/nio/Buffer IOOBE_FORMATTER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$4
staticfield java/nio/Buffer $assertionsDisabled Z 1
instanceKlass org/eclipse/osgi/internal/weaving/DynamicImportList
instanceKlass org/eclipse/osgi/internal/container/InternalUtils$CopyOnFirstWriteList
instanceKlass java/util/AbstractSequentialList
instanceKlass java/util/Collections$SingletonList
instanceKlass java/util/Vector
instanceKlass sun/security/jca/ProviderList$ServiceList
instanceKlass sun/security/jca/ProviderList$3
instanceKlass java/util/Arrays$ArrayList
instanceKlass java/util/ArrayList$SubList
instanceKlass java/util/Collections$EmptyList
instanceKlass java/util/ArrayList
ciInstanceKlass java/util/AbstractList 1 1 218 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 1 11 100 12 1 1 1 11 12 1 1 11 12 1 10 7 12 1 1 1 10 12 1 11 12 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 11 7 1 11 7 1 10 12 1 100 1 10 12 1 10 12 1 1 7 1 100 1 10 12 1 100 1 10 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 100 1 8 1 8 1 8 1 10 7 1 11 10 10 12 1 11 12 1 10 12 1 1 8 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Iterable 1 1 62 10 7 12 1 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/Collection 1 1 115 11 7 12 1 1 1 7 1 11 7 12 1 1 1 10 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 10 100 12 1 1 1 11 12 1 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/SequencedCollection 1 1 109 100 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1 1 8 1
ciInstanceKlass java/util/List 1 1 251 10 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 100 1 10 100 12 1 1 1 11 12 1 1 11 12 1 11 12 1 100 1 10 12 1 11 12 1 1 11 12 1 1 11 12 1 10 100 12 1 1 1 9 7 12 1 1 1 7 1 10 12 10 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1
instanceKlass java/util/TreeMap$Values
instanceKlass java/util/IdentityHashMap$Values
instanceKlass org/apache/felix/resolver/util/ArrayMap$1
instanceKlass org/apache/felix/resolver/util/OpenHashMap$AbstractObjectCollection
instanceKlass org/eclipse/osgi/internal/container/NamespaceList$Builder
instanceKlass java/util/AbstractQueue
instanceKlass java/util/LinkedHashMap$LinkedValues
instanceKlass java/util/HashMap$Values
instanceKlass java/util/ArrayDeque
instanceKlass java/util/AbstractSet
instanceKlass java/util/ImmutableCollections$AbstractImmutableCollection
instanceKlass java/util/AbstractList
ciInstanceKlass java/util/AbstractCollection 1 1 160 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 100 1 10 11 12 1 11 7 1 10 12 1 10 12 1 10 100 12 1 1 1 11 8 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/AssertionStatusDirectives 0 0 24 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/IllegalCallerException
instanceKlass org/eclipse/core/runtime/InvalidRegistryObjectException
instanceKlass org/eclipse/core/runtime/AssertionFailedException
instanceKlass org/osgi/service/component/ComponentException
instanceKlass org/osgi/framework/hooks/weaving/WeavingException
instanceKlass java/util/MissingResourceException
instanceKlass java/util/ConcurrentModificationException
instanceKlass org/osgi/framework/ServiceException
instanceKlass java/util/concurrent/RejectedExecutionException
instanceKlass java/lang/TypeNotPresentException
instanceKlass org/eclipse/osgi/framework/util/ThreadInfoReport
instanceKlass java/lang/IndexOutOfBoundsException
instanceKlass org/eclipse/osgi/storage/Storage$StorageException
instanceKlass java/util/NoSuchElementException
instanceKlass java/lang/SecurityException
instanceKlass java/lang/invoke/WrongMethodTypeException
instanceKlass java/lang/UnsupportedOperationException
instanceKlass java/lang/IllegalStateException
instanceKlass java/lang/IllegalArgumentException
instanceKlass java/lang/ArithmeticException
instanceKlass java/lang/NullPointerException
instanceKlass java/lang/IllegalMonitorStateException
instanceKlass java/lang/ArrayStoreException
instanceKlass java/lang/ClassCastException
ciInstanceKlass java/lang/RuntimeException 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/reflect/Proxy$ProxyBuilder$ProxyClassContext
instanceKlass java/nio/DirectByteBuffer$Deallocator
instanceKlass org/eclipse/equinox/launcher/Main$Identifier
instanceKlass sun/security/pkcs/SignerInfo$AlgorithmInfo
instanceKlass jdk/internal/misc/ThreadTracker$ThreadRef
instanceKlass java/security/SecureClassLoader$CodeSourceKey
instanceKlass jdk/internal/module/ModuleReferenceImpl$CachedHash
instanceKlass java/util/stream/Collectors$CollectorImpl
instanceKlass jdk/internal/reflect/ReflectionFactory$Config
instanceKlass jdk/internal/foreign/abi/UpcallLinker$CallRegs
instanceKlass jdk/internal/foreign/abi/VMStorage
ciInstanceKlass java/lang/Record 1 1 22 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodType 1 1 780 7 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 8 1 10 100 12 1 1 1 9 7 1 9 7 1 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 7 1 8 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 9 12 1 11 12 1 1 7 10 12 1 1 10 12 1 1 7 1 7 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 11 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 11 12 1 1 11 12 1 10 100 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 9 12 1 1 7 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 11 12 1 100 1 11 100 12 1 1 10 12 1 100 1 10 12 1 10 100 12 1 1 10 12 1 1 9 12 1 1 9 100 12 1 1 1 10 7 12 1 1 1 9 12 1 10 100 12 1 1 10 12 1 100 10 12 1 1 10 12 1 7 1 10 10 12 1 1 7 1 7 1 9 12 1 1 7 1 7 1 7 1 1 1 5 0 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 16 16 15 10 12 16 15 10 100 12 1 1 1 1 1 7 1 1 7 1 1 100 1 100 1 1
staticfield java/lang/invoke/MethodType internTable Ljdk/internal/util/ReferencedKeySet; jdk/internal/util/ReferencedKeySet
staticfield java/lang/invoke/MethodType NO_PTYPES [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType objectOnlyTypes [Ljava/lang/invoke/MethodType; 20 [Ljava/lang/invoke/MethodType;
staticfield java/lang/invoke/MethodType METHOD_HANDLE_ARRAY [Ljava/lang/Class; 1 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/invoke/MethodType $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/TypeDescriptor$OfMethod 1 0 43 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
instanceKlass java/lang/InstantiationException
instanceKlass java/lang/IllegalAccessException
instanceKlass java/lang/reflect/InvocationTargetException
instanceKlass java/lang/NoSuchFieldException
instanceKlass java/lang/NoSuchMethodException
instanceKlass java/lang/ClassNotFoundException
ciInstanceKlass java/lang/ReflectiveOperationException 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/DelegatingMethodHandle
instanceKlass java/lang/invoke/BoundMethodHandle
instanceKlass java/lang/invoke/DirectMethodHandle
ciInstanceKlass java/lang/invoke/MethodHandle 1 1 733 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 10 9 7 12 1 1 1 9 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 11 12 1 10 12 1 10 12 1 1 10 100 12 1 1 100 1 11 12 1 10 100 1 11 12 1 7 1 10 12 1 11 12 1 9 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 10 12 1 1 9 12 1 11 12 1 9 12 1 9 12 1 9 12 1 11 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 10 7 12 1 1 10 12 1 1 100 1 100 1 8 1 8 1 10 10 12 1 1 10 12 1 10 12 1 7 1 10 100 12 1 1 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 7 12 1 1 9 12 1 10 12 1 1 9 12 1 10 12 1 8 10 12 1 1 8 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 7 1 100 1 1 100 1 1 100 1 1 1 1
staticfield java/lang/invoke/MethodHandle FORM_OFFSET J 20
staticfield java/lang/invoke/MethodHandle UPDATE_OFFSET J 13
staticfield java/lang/invoke/MethodHandle $assertionsDisabled Z 1
ciInstanceKlass java/util/concurrent/ConcurrentHashMap 1 1 1210 7 1 7 1 3 10 12 1 1 3 7 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 100 1 11 12 1 1 11 12 1 11 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 4 10 12 1 9 12 1 10 12 1 1 100 1 10 5 0 10 12 1 10 12 1 1 5 0 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 7 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 7 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 9 10 12 1 1 9 12 1 10 12 1 1 5 0 9 12 1 1 7 1 10 12 1 9 12 1 1 7 1 10 12 1 9 12 1 7 1 10 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 11 100 1 10 12 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 9 10 12 1 9 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 100 1 10 12 11 100 12 1 1 10 11 7 12 1 10 12 1 100 1 10 12 1 100 1 10 10 9 7 12 1 1 1 10 12 3 10 7 12 1 1 9 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 100 12 1 1 9 12 1 9 7 12 1 1 10 12 1 1 10 12 1 3 9 12 1 9 12 1 10 12 1 1 7 1 9 3 9 12 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 12 1 9 12 1 10 100 12 1 1 1 100 10 12 1 7 1 5 0 10 100 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 100 1 10 12 1 10 100 1 100 1 10 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 7 1 10 12 1 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 9 12 1 1 10 12 1 1 8 10 12 1 1 8 8 8 8 7 10 12 1 1 10 12 1 100 1 8 1 10 7 1 7 1 7 1 1 1 5 0 1 1 3 1 3 1 1 1 1 3 1 3 1 3 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/ConcurrentHashMap NCPU I 8
staticfield java/util/concurrent/ConcurrentHashMap serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
staticfield java/util/concurrent/ConcurrentHashMap U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/concurrent/ConcurrentHashMap SIZECTL J 20
staticfield java/util/concurrent/ConcurrentHashMap TRANSFERINDEX J 32
staticfield java/util/concurrent/ConcurrentHashMap BASECOUNT J 24
staticfield java/util/concurrent/ConcurrentHashMap CELLSBUSY J 36
staticfield java/util/concurrent/ConcurrentHashMap CELLVALUE J 144
staticfield java/util/concurrent/ConcurrentHashMap ABASE I 16
staticfield java/util/concurrent/ConcurrentHashMap ASHIFT I 2
instanceKlass org/eclipse/osgi/internal/framework/FilterImpl$DictionaryMap
instanceKlass org/eclipse/osgi/internal/framework/FilterImpl$ServiceReferenceMap
instanceKlass org/eclipse/osgi/internal/serviceregistry/ShrinkableValueCollectionMap
instanceKlass org/apache/felix/resolver/util/ArrayMap
instanceKlass java/util/Collections$SingletonMap
instanceKlass java/util/TreeMap
instanceKlass java/util/IdentityHashMap
instanceKlass java/util/EnumMap
instanceKlass java/util/WeakHashMap
instanceKlass java/util/Collections$EmptyMap
instanceKlass sun/util/PreHashedMap
instanceKlass java/util/HashMap
instanceKlass java/util/ImmutableCollections$AbstractImmutableMap
instanceKlass java/util/concurrent/ConcurrentHashMap
ciInstanceKlass java/util/AbstractMap 1 1 196 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 10 12 1 1 11 12 1 100 1 10 11 12 1 11 7 1 10 12 1 1 11 12 1 9 12 1 1 7 1 10 12 1 9 12 1 1 100 1 10 11 11 12 1 1 11 12 1 7 1 100 1 11 12 1 8 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1
ciInstanceKlass java/util/concurrent/ConcurrentMap 1 1 208 11 7 12 1 1 1 10 100 12 1 1 11 12 1 1 11 100 12 1 1 1 11 7 12 1 1 1 11 12 1 1 100 1 11 12 1 11 12 1 100 1 11 100 12 1 1 1 18 12 1 11 12 1 1 11 100 12 1 1 11 12 1 1 11 100 12 1 11 12 1 1 11 12 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 11 12 15 10 100 12 1 1 1 1 1 100 1 100 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1
ciInstanceKlass jdk/internal/loader/ClassLoaders 1 1 183 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 11 100 12 1 1 1 100 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 7 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/loader/ClassLoaders JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/loader/ClassLoaders BOOT_LOADER Ljdk/internal/loader/ClassLoaders$BootClassLoader; jdk/internal/loader/ClassLoaders$BootClassLoader
staticfield jdk/internal/loader/ClassLoaders PLATFORM_LOADER Ljdk/internal/loader/ClassLoaders$PlatformClassLoader; jdk/internal/loader/ClassLoaders$PlatformClassLoader
staticfield jdk/internal/loader/ClassLoaders APP_LOADER Ljdk/internal/loader/ClassLoaders$AppClassLoader; jdk/internal/loader/ClassLoaders$AppClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$BootClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/loader/BuiltinClassLoader 1 1 737 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 7 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 7 1 10 12 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 9 12 1 1 10 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 7 12 1 1 7 1 10 7 12 1 1 1 10 12 1 100 1 8 1 10 12 1 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 12 1 1 11 12 1 7 1 10 11 12 1 1 11 10 12 1 1 7 1 10 12 1 10 7 12 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 1 11 12 1 7 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 10 12 1 18 12 1 1 10 12 1 10 12 1 1 18 100 1 10 7 12 1 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 11 12 1 7 1 10 12 1 7 1 100 1 10 12 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 7 12 1 1 10 12 1 100 1 8 1 8 1 10 10 12 1 8 1 8 1 10 7 12 1 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 1 10 12 1 7 1 10 11 12 1 1 10 12 10 12 1 10 12 1 100 1 10 12 1 10 12 1 10 10 12 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 15 10 12 16 15 10 12 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 100 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/loader/BuiltinClassLoader packageToModule Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
staticfield jdk/internal/loader/BuiltinClassLoader $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader 1 1 119 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 7 1 8 1 10 12 10 7 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader 1 1 42 8 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 100 1 1
ciInstanceKlass java/lang/ArithmeticException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ArrayStoreException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassCastException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassNotFoundException 1 1 96 7 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ClassNotFoundException serialPersistentFields [Ljava/io/ObjectStreamField; 1 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/IllegalMonitorStateException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciMethod java/lang/IllegalMonitorStateException <init> ()V 0 0 1 0 -1
ciInstanceKlass java/lang/BootstrapMethodError 0 0 45 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/ClassFormatError
instanceKlass java/lang/UnsatisfiedLinkError
instanceKlass java/lang/IncompatibleClassChangeError
instanceKlass java/lang/BootstrapMethodError
instanceKlass java/lang/NoClassDefFoundError
ciInstanceKlass java/lang/LinkageError 1 1 31 10 7 12 1 1 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/NullPointerException 1 1 52 10 7 12 1 1 1 10 12 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass java/lang/NoClassDefFoundError 1 1 26 10 7 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackOverflowError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackTraceElement 0 0 235 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 8 1 10 100 12 1 1 1 7 1 9 12 1 8 1 9 12 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 8 1 10 100 12 1 1 1 7 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 7 12 1 1 10 100 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer
ciInstanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer 1 1 32 10 7 12 1 1 1 9 7 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod java/util/concurrent/locks/AbstractOwnableSynchronizer setExclusiveOwnerThread (Ljava/lang/Thread;)V 268 0 134 0 0
ciMethod java/util/concurrent/locks/AbstractOwnableSynchronizer getExclusiveOwnerThread ()Ljava/lang/Thread; 568 0 284 0 0
ciInstanceKlass jdk/internal/vm/Continuation 0 0 549 9 100 12 1 1 1 9 12 1 9 12 1 100 1 7 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 11 100 12 1 1 1 10 7 1 9 12 1 1 9 12 1 1 10 8 1 10 12 1 9 12 1 1 10 11 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 11 12 1 1 9 12 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 100 1 10 12 1 11 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 1 9 12 1 1 11 12 1 1 9 12 1 1 8 1 10 11 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 10 12 1 8 1 10 12 1 8 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 11 12 1 7 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 11 7 12 1 1 10 7 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 8 1 10 7 12 1 1 1 10 12 1 8 1 100 1 8 1 10 9 12 1 1 8 1 10 7 12 1 1 10 100 12 1 1 8 1 8 1 10 12 10 100 12 1 1 1 10 7 1 10 7 12 1 1 1 18 11 100 12 1 1 1 18 12 1 11 12 1 1 7 1 10 7 12 1 1 10 12 1 1 8 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 8 1 10 12 1 7 1 7 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 16 1 15 10 12 16 15 11 7 12 1 1 1 16 1 16 1 15 10 12 16 15 10 100 12 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/misc/UnsafeConstants 1 1 34 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/UnsafeConstants ADDRESS_SIZE0 I 8
staticfield jdk/internal/misc/UnsafeConstants PAGE_SIZE I 4096
staticfield jdk/internal/misc/UnsafeConstants BIG_ENDIAN Z 0
staticfield jdk/internal/misc/UnsafeConstants UNALIGNED_ACCESS Z 1
staticfield jdk/internal/misc/UnsafeConstants DATA_CACHE_LINE_FLUSH_SIZE I 0
ciInstanceKlass java/lang/invoke/LambdaForm 1 1 1059 7 1 100 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 9 12 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 9 7 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 10 12 1 8 1 8 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 9 12 1 7 1 10 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 12 10 12 1 1 10 12 1 1 9 12 1 8 10 12 1 1 100 1 10 12 1 1 10 12 1 9 7 12 1 1 9 7 12 1 1 1 8 1 10 100 12 1 1 10 12 1 1 7 1 7 1 10 10 12 1 1 10 12 1 1 8 1 8 1 7 1 8 1 10 12 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 10 12 1 1 8 1 8 1 8 1 7 1 8 1 7 1 8 1 7 1 8 1 10 12 1 8 1 9 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 100 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 8 1 8 1 7 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 7 1 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 8 1 10 12 1 9 12 1 1 7 1 10 7 12 1 1 1 8 1 100 1 10 12 1 9 12 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 10 12 1 10 10 12 1 9 12 1 9 9 12 1 7 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 7 1 9 1 1 1 1 3 1 3 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 7 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/LambdaForm DEFAULT_CUSTOMIZED Ljava/lang/invoke/MethodHandle; null
staticfield java/lang/invoke/LambdaForm DEFAULT_KIND Ljava/lang/invoke/LambdaForm$Kind; java/lang/invoke/LambdaForm$Kind
staticfield java/lang/invoke/LambdaForm COMPILE_THRESHOLD I 0
staticfield java/lang/invoke/LambdaForm INTERNED_ARGUMENTS [[Ljava/lang/invoke/LambdaForm$Name; 5 [[Ljava/lang/invoke/LambdaForm$Name;
staticfield java/lang/invoke/LambdaForm IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/LambdaForm LF_identity [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm LF_zero [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm NF_identity [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm NF_zero [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm createFormsLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/invoke/LambdaForm DEBUG_NAME_COUNTERS Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm DEBUG_NAMES Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm TRACE_INTERPRETER Z 0
staticfield java/lang/invoke/LambdaForm $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MemberName 1 1 724 7 1 7 1 100 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 7 1 7 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 8 1 10 100 12 1 1 1 7 1 10 10 12 1 1 100 1 100 1 10 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 8 1 9 12 1 1 3 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 8 10 12 1 1 10 12 1 1 8 1 9 7 1 8 9 7 1 10 12 1 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 8 1 8 1 7 1 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 3 10 12 1 3 10 12 1 3 3 3 3 3 3 10 12 1 3 9 12 1 10 12 1 1 3 10 12 1 10 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 7 1 10 10 10 12 100 1 10 10 10 12 1 1 10 12 1 1 10 10 12 1 8 10 7 1 10 12 1 10 7 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 1 100 1 8 1 10 7 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 7 12 1 1 1 8 1 8 1 10 12 1 8 1 10 10 10 12 1 10 12 1 8 1 8 1 10 10 12 1 8 1 10 100 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 100 1 10 8 1 8 1 8 1 8 1 10 12 1 7 1 100 1 7 1 10 100 1 10 7 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 7 1 1 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MemberName $assertionsDisabled Z 1
instanceKlass java/lang/invoke/VarHandleByteArrayAsDoubles$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsFloats$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsChars$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsShorts$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleBooleans$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleReferences$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleInts$FieldInstanceReadOnly
instanceKlass java/lang/invoke/VarHandleByteArrayAsLongs$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsInts$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleReferences$FieldStaticReadOnly
ciInstanceKlass java/lang/invoke/VarHandle 1 1 473 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 100 1 10 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 10 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 9 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 100 1 10 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 10 12 1 1 7 1 10 12 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 100 1 1 1 100 1 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1
staticfield java/lang/invoke/VarHandle VFORM_OFFSET J 16
staticfield java/lang/invoke/VarHandle $assertionsDisabled Z 1
instanceKlass jdk/internal/reflect/FieldAccessorImpl
instanceKlass jdk/internal/reflect/ConstructorAccessorImpl
instanceKlass jdk/internal/reflect/MethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MagicAccessorImpl 1 1 16 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/DirectMethodHandleAccessor
ciInstanceKlass jdk/internal/reflect/MethodAccessorImpl 1 1 38 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/MethodAccessor 1 0 17 100 1 100 1 1 1 1 100 1 100 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/DirectConstructorHandleAccessor$NativeAccessor
instanceKlass jdk/internal/reflect/DirectConstructorHandleAccessor
instanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl
ciInstanceKlass jdk/internal/reflect/ConstructorAccessorImpl 1 1 27 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass jdk/internal/reflect/ConstructorAccessor 1 0 16 100 1 100 1 1 1 1 100 1 100 1 100 1 1 1
ciInstanceKlass jdk/internal/reflect/DelegatingClassLoader 0 0 18 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/CallerSensitive 1 0 17 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl 0 0 125 10 7 12 1 1 1 9 7 12 1 1 1 100 1 10 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 1 10 12 1 1 8 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/ConstantPool 1 1 142 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 11 7 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl 0 0 47 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 8 11 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/FieldAccessor 1 0 48 100 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/MethodHandleFieldAccessorImpl
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/FieldAccessorImpl 1 1 269 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 100 1 10 12 1 1 10 8 1 10 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 8 1 8 1 8 1 8 1 10 7 12 1 1 1 8 1 8 1 8 1 10 12 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl 0 0 62 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/VolatileCallSite
instanceKlass java/lang/invoke/MutableCallSite
instanceKlass java/lang/invoke/ConstantCallSite
ciInstanceKlass java/lang/invoke/CallSite 1 1 307 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 7 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 7 12 1 1 10 12 1 1 100 1 7 1 10 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 100 12 1 1 10 12 1 1 9 12 1 9 100 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 1 9 12 1 8 1 100 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 8 10 12 1 1 9 12 1 1 100 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 8 1 10 10 12 10 12 1 1 7 1 7 1 7 1 8 1 10 12 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 1 1
staticfield java/lang/invoke/CallSite $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/ConstantCallSite 1 1 65 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 100 1 10 12 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/ConstantCallSite UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass java/lang/invoke/DirectMethodHandle$StaticAccessor
instanceKlass java/lang/invoke/DirectMethodHandle$Special
instanceKlass java/lang/invoke/DirectMethodHandle$Interface
instanceKlass java/lang/invoke/DirectMethodHandle$Constructor
instanceKlass java/lang/invoke/DirectMethodHandle$Accessor
ciInstanceKlass java/lang/invoke/DirectMethodHandle 1 1 923 7 1 7 1 100 1 7 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 10 12 1 7 1 10 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 7 12 1 1 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 7 1 9 12 9 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 7 1 9 12 1 1 10 7 12 1 1 1 10 12 10 12 1 7 1 10 12 1 10 12 1 1 8 1 9 12 1 9 12 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 7 1 10 12 1 9 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 8 1 8 1 8 1 8 1 10 12 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 8 9 12 1 1 10 12 1 1 8 1 8 8 9 12 1 8 1 8 8 8 8 8 1 8 10 12 1 7 1 10 12 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/DirectMethodHandle FT_UNCHECKED_REF I 8
staticfield java/lang/invoke/DirectMethodHandle ACCESSOR_FORMS [Ljava/lang/invoke/LambdaForm; 132 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/DirectMethodHandle ALL_WRAPPERS [Lsun/invoke/util/Wrapper; 10 [Lsun/invoke/util/Wrapper;
staticfield java/lang/invoke/DirectMethodHandle NFS [Ljava/lang/invoke/LambdaForm$NamedFunction; 12 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/DirectMethodHandle OBJ_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle LONG_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MutableCallSite 0 0 63 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass java/lang/invoke/VolatileCallSite 0 0 37 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/ResolvedMethodName 1 1 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives 1 1 690 100 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 1 9 7 12 1 1 1 8 1 10 100 12 1 1 1 7 1 10 12 100 1 100 1 8 1 7 1 10 10 12 1 7 1 9 7 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 7 1 10 12 1 8 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 10 10 12 1 1 10 12 1 10 100 12 1 1 1 100 1 8 1 10 100 12 1 1 1 7 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 7 1 7 1 10 12 1 10 12 1 8 1 8 1 10 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 9 12 1 10 12 1 9 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 1 100 1 100 1 10 10 100 1 100 1 10 100 1 10 10 12 1 1 10 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 100 1 1 1
staticfield java/lang/invoke/MethodHandleNatives $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext 1 1 49 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/NativeEntryPoint 0 0 194 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 9 12 1 1 18 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 18 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 1 15 10 12 16 1 16 15 10 12 15 10 100 12 1 1 1 1 1 100 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/ABIDescriptor 0 0 55 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/foreign/abi/VMStorage 0 0 91 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 18 12 1 18 12 1 1 18 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 15 15 15 15 15 10 100 12 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/foreign/abi/UpcallLinker$CallRegs 0 0 66 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 18 12 1 1 18 12 1 1 18 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 8 1 15 15 15 10 100 12 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/StackWalker 1 1 271 9 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 11 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 12 1 1 100 1 8 1 10 10 7 12 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 18 12 1 1 100 1 8 1 10 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 7 12 1 1 11 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 16 15 10 12 16 1 15 10 100 12 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/StackWalker DEFAULT_EMPTY_OPTION Ljava/util/EnumSet; java/util/RegularEnumSet
staticfield java/lang/StackWalker DEFAULT_WALKER Ljava/lang/StackWalker; java/lang/StackWalker
ciInstanceKlass java/lang/StackWalker$StackFrame 1 1 41 100 1 10 12 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/LiveStackFrameInfo
ciInstanceKlass java/lang/StackFrameInfo 1 1 142 10 7 12 1 1 1 9 7 12 1 1 1 9 7 1 9 12 1 1 11 7 12 1 1 1 9 12 1 1 11 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 11 12 1 11 12 1 1 11 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 1 11 12 1 1 9 12 1 1 10 7 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 7 1 1 1 1 1 1
staticfield java/lang/StackFrameInfo JLIA Ljdk/internal/access/JavaLangInvokeAccess; java/lang/invoke/MethodHandleImpl$1
ciInstanceKlass java/lang/LiveStackFrameInfo 0 0 97 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 7 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 10 100 1 10 12 1 100 1 10 12 1 7 1 7 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/LiveStackFrame 0 0 135 100 1 10 100 12 1 1 1 11 7 12 1 1 1 11 12 1 10 7 12 1 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 12 1 10 12 1 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 8 1 1 12 10 1 1 1 8 1 1 1 8 1 1 1 8 1 1 1 8 1 1 8 1 1 1 8 1 1 8 1
instanceKlass java/lang/StackStreamFactory$StackFrameTraverser
ciInstanceKlass java/lang/StackStreamFactory$AbstractStackWalker 1 1 375 7 1 7 1 3 10 7 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 7 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 10 7 12 1 1 9 12 1 8 1 5 0 8 1 8 1 9 12 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 9 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 7 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 15 10 100 12 1 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass jdk/internal/module/Modules 1 1 504 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 11 12 1 11 12 1 11 12 1 11 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 11 12 1 9 12 1 1 11 7 12 1 1 1 10 12 1 1 10 10 12 1 10 9 12 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 12 1 1 18 12 1 1 11 100 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 7 1 11 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 1 11 12 1 1 10 12 1 18 18 10 12 1 1 9 12 1 1 11 7 12 1 1 1 100 1 10 11 12 1 11 12 1 1 11 12 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 1 11 12 10 12 1 1 7 1 10 18 12 1 10 12 1 1 7 1 8 1 10 12 1 10 100 12 1 1 18 12 1 11 11 12 10 12 1 10 10 100 1 18 12 1 10 10 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 16 15 10 12 1 16 16 15 10 12 1 16 1 16 1 15 10 12 1 16 1 16 1 15 10 12 16 1 15 10 16 1 15 10 12 16 1 15 10 12 16 15 10 12 16 15 10 12 15 10 100 12 1 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/module/Modules JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/module/Modules JLMA Ljdk/internal/access/JavaLangModuleAccess; java/lang/module/ModuleDescriptor$1
staticfield jdk/internal/module/Modules $assertionsDisabled Z 1
ciInstanceKlass java/util/ArrayList 1 1 509 10 7 12 1 1 1 7 1 9 7 12 1 1 1 9 12 1 100 1 7 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 11 7 12 1 1 1 9 12 1 1 11 12 1 1 7 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 7 1 10 12 1 10 10 7 12 1 1 1 10 7 12 1 1 10 12 1 100 1 10 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 11 12 1 7 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 100 1 8 1 10 7 1 10 12 1 7 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 1 11 100 12 1 1 7 1 10 12 1 10 12 1 1 11 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 10 12 1 1 7 1 7 1 7 1 1 1 1 5 0 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/util/ArrayList EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
staticfield java/util/ArrayList DEFAULTCAPACITY_EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
ciInstanceKlass java/util/RandomAccess 1 0 7 100 1 100 1 1 1
ciInstanceKlass jdk/internal/misc/Blocker 1 1 106 10 7 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 10 7 12 1 1 1 10 7 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 1 9 12 1 1 100 1 10 10 12 1 5 0 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 7 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/Blocker JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/misc/Blocker $assertionsDisabled Z 1
ciMethod jdk/internal/misc/Blocker begin ()J 518 0 5392 0 -1
ciMethod jdk/internal/misc/Blocker end (J)V 522 0 5389 0 -1
ciInstanceKlass java/lang/annotation/Annotation 1 0 17 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/misc/InternalLock 1 1 69 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 9 12 1 1 10 10 12 1 1 10 12 10 12 1 10 12 1 8 1 10 7 12 1 1 1 10 7 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/InternalLock CAN_USE_INTERNAL_LOCK Z 1
ciMethod jdk/internal/misc/InternalLock lock ()V 1024 0 26254 0 408
ciMethod jdk/internal/misc/InternalLock unlock ()V 1024 0 26247 0 272
ciInstanceKlass java/util/concurrent/locks/Lock 1 0 19 100 1 100 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1
instanceKlass org/eclipse/osgi/internal/serviceregistry/ServiceUse$ServiceUseLock
instanceKlass org/eclipse/osgi/internal/container/EquinoxReentrantLock
instanceKlass jdk/internal/loader/NativeLibraries$CountedLock
instanceKlass java/util/concurrent/ConcurrentHashMap$Segment
ciInstanceKlass java/util/concurrent/locks/ReentrantLock 1 1 177 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 7 1 10 10 7 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 100 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 10 12 1 1 10 12 1 1 8 1 8 1 10 7 12 1 1 8 1 10 7 1 7 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 100 1 1
ciMethod java/util/concurrent/locks/ReentrantLock lock ()V 14 0 29261 0 392
ciMethod java/util/concurrent/locks/ReentrantLock unlock ()V 12 0 29891 0 256
ciInstanceKlass java/util/concurrent/locks/LockSupport 1 1 105 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/locks/LockSupport U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/concurrent/locks/LockSupport PARKBLOCKER J 92
ciMethod java/util/concurrent/locks/LockSupport unpark (Ljava/lang/Thread;)V 10 0 351 0 0
ciInstanceKlass java/util/concurrent/locks/ReentrantLock$NonfairSync 1 1 69 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 7 1 8 1 10 12 1 10 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
instanceKlass java/util/concurrent/CountDownLatch$Sync
instanceKlass java/util/concurrent/ThreadPoolExecutor$Worker
instanceKlass java/util/concurrent/Semaphore$Sync
instanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$Sync
instanceKlass java/util/concurrent/locks/ReentrantLock$Sync
ciInstanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer 1 1 367 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 7 1 10 7 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 7 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 1 9 12 1 10 12 1 1 9 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 10 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 5 0 10 12 1 10 12 1 10 12 1 3 100 1 10 10 12 1 1 100 1 10 10 12 1 10 12 1 100 1 10 10 12 1 7 1 10 10 12 1 1 7 1 10 10 7 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 10 10 100 12 1 1 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 10 12 1 1 8 8 7 1 1 1 5 0 1 3 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/locks/AbstractQueuedSynchronizer U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/concurrent/locks/AbstractQueuedSynchronizer STATE J 16
staticfield java/util/concurrent/locks/AbstractQueuedSynchronizer HEAD J 20
staticfield java/util/concurrent/locks/AbstractQueuedSynchronizer TAIL J 24
ciMethod java/util/concurrent/locks/AbstractQueuedSynchronizer tryAcquire (I)Z 0 0 1 0 -1
ciMethod java/util/concurrent/locks/AbstractQueuedSynchronizer tryRelease (I)Z 0 0 1 0 -1
ciMethod java/util/concurrent/locks/AbstractQueuedSynchronizer getState ()I 636 0 318 0 0
ciMethod java/util/concurrent/locks/AbstractQueuedSynchronizer compareAndSetState (II)Z 538 0 2155 0 104
ciMethod java/util/concurrent/locks/AbstractQueuedSynchronizer setState (I)V 538 0 269 0 0
ciMethod java/util/concurrent/locks/AbstractQueuedSynchronizer release (I)Z 14 0 30049 0 0
ciMethod java/util/concurrent/locks/AbstractQueuedSynchronizer acquire (I)V 0 0 720 0 0
ciMethod java/util/concurrent/locks/AbstractQueuedSynchronizer acquire (Ljava/util/concurrent/locks/AbstractQueuedSynchronizer$Node;IZZZJ)I 56 94 275 0 -1
ciMethod java/util/concurrent/locks/AbstractQueuedSynchronizer signalNext (Ljava/util/concurrent/locks/AbstractQueuedSynchronizer$Node;)V 14 0 8901 0 152
instanceKlass java/util/concurrent/locks/ReentrantLock$FairSync
instanceKlass java/util/concurrent/locks/ReentrantLock$NonfairSync
ciInstanceKlass java/util/concurrent/locks/ReentrantLock$Sync 1 1 127 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 10 12 1 10 12 1 1 7 1 10 7 1 10 12 1 10 12 1 10 100 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 7 1 1 1 1 100 1 1 1 1
ciMethod java/util/concurrent/locks/ReentrantLock$Sync tryRelease (I)Z 14 0 8984 0 152
ciMethod java/util/concurrent/locks/ReentrantLock$Sync initialTryLock ()Z 0 0 1 0 -1
ciMethod java/util/concurrent/locks/ReentrantLock$Sync lock ()V 14 0 29261 0 0
ciMethod java/util/concurrent/locks/ReentrantLock$NonfairSync initialTryLock ()Z 536 0 10034 0 336
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$SharedNode
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionNode
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$ExclusiveNode
ciInstanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$Node 1 1 84 10 7 12 1 1 1 9 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/locks/AbstractQueuedSynchronizer$Node STATUS J 12
staticfield java/util/concurrent/locks/AbstractQueuedSynchronizer$Node NEXT J 20
staticfield java/util/concurrent/locks/AbstractQueuedSynchronizer$Node PREV J 16
ciMethod java/util/concurrent/locks/AbstractQueuedSynchronizer$Node getAndUnsetStatus (I)I 12 0 18 0 0
ciMethod java/io/FilterInputStream available ()I 512 0 301 0 -1
ciInstanceKlass java/util/zip/ZipFile$ZipFileInflaterInputStream 1 1 155 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 10 7 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 9 7 12 1 1 1 9 12 1 1 11 7 12 1 1 1 11 7 12 1 1 100 1 8 1 10 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 7 1 5 0 3 1 1 1 1 1 1 1 1 1 1 7 1 7 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethod jdk/internal/util/ArraysSupport newLength (III)I 406 0 7443 0 -1
ciMethod java/lang/Math max (II)I 516 0 27313 0 -1
ciMethod java/lang/Thread isVirtual ()Z 100 0 1371 0 -1
ciMethod java/lang/Thread currentThread ()Ljava/lang/Thread; 0 0 1 0 -1
ciMethod jdk/internal/misc/Unsafe getAndBitwiseAndInt (Ljava/lang/Object;JI)I 36 0 18 0 -1
ciMethod jdk/internal/misc/Unsafe unpark (Ljava/lang/Object;)V 256 0 128 0 -1
ciMethod jdk/internal/misc/Unsafe compareAndSetInt (Ljava/lang/Object;JII)Z 256 0 128 0 -1
ciMethod jdk/internal/misc/Unsafe compareAndSetReference (Ljava/lang/Object;JLjava/lang/Object;Ljava/lang/Object;)Z 256 0 128 0 -1
ciMethod java/io/BufferedInputStream read1 ([BII)I 1024 0 6743 0 1040
ciMethod java/io/BufferedInputStream ensureOpen ()V 512 0 8511 0 0
ciMethod java/io/BufferedInputStream implRead ([BII)I 1024 0 6781 0 0
ciMethod java/io/BufferedInputStream getInIfOpen ()Ljava/io/InputStream; 330 0 222 0 0
ciMethod java/io/BufferedInputStream getBufIfOpen ()[B 1024 0 14499 0 496
ciMethod java/io/BufferedInputStream getBufIfOpen (Z)[B 1024 0 14545 0 0
ciMethod java/io/BufferedInputStream fill ()V 2 0 221 0 0
ciMethod java/io/FileInputStream available0 ()I 48 0 24 0 -1
ciMethod java/io/InputStream read ([BII)I 0 0 1 0 -1
ciMethod java/io/InputStream available ()I 0 0 1 0 -1
ciMethod java/lang/Error <init> (Ljava/lang/String;)V 38 0 35 0 0
ciMethod java/lang/Exception <init> (Ljava/lang/String;Ljava/lang/Throwable;)V 512 0 523 0 -1
ciMethod java/lang/Throwable <init> (Ljava/lang/String;Ljava/lang/Throwable;)V 512 0 523 0 -1
ciMethod java/lang/Throwable <init> (Ljava/lang/String;)V 140 0 67 0 -1
ciMethod java/lang/Object <init> ()V 1024 0 367299 0 -1
ciInstanceKlass java/util/concurrent/locks/ReentrantReadWriteLock$NonfairSync 1 1 33 10 7 12 1 1 1 10 7 12 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
instanceKlass java/io/CharConversionException
instanceKlass java/io/EOFException
instanceKlass java/net/UnknownServiceException
instanceKlass java/io/InterruptedIOException
instanceKlass java/io/SyncFailedException
instanceKlass java/util/zip/ZipException
instanceKlass java/nio/file/FileSystemException
instanceKlass java/io/UnsupportedEncodingException
instanceKlass java/net/MalformedURLException
instanceKlass java/io/FileNotFoundException
ciInstanceKlass java/io/IOException 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass org/eclipse/osgi/storage/SystemBundleFile
instanceKlass org/eclipse/osgi/storage/bundlefile/BundleFileWrapper
instanceKlass org/eclipse/osgi/storage/bundlefile/DirBundleFile
instanceKlass org/eclipse/osgi/storage/bundlefile/CloseableBundleFile
instanceKlass org/eclipse/osgi/storage/bundlefile/NestedDirBundleFile
ciInstanceKlass org/eclipse/osgi/storage/bundlefile/BundleFile 1 1 185 7 1 7 1 1 1 1 1 1 1 1 1 1 10 7 1 12 1 1 10 7 1 12 1 1 9 12 1 1 1 1 10 12 9 12 9 12 1 1 1 1 1 1 1 1 1 1 10 12 1 1 1 1 1 1 100 1 1 1 1 1 1 10 12 10 12 1 1 1 1 1 1 1 1 7 1 7 1 8 1 10 7 1 12 1 1 10 7 1 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 7 1 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 1 1 1 1 1 10 12 1 8 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 1 10 12 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1
staticfield org/eclipse/osgi/storage/bundlefile/BundleFile secureAction Lorg/eclipse/osgi/framework/util/SecureAction; org/eclipse/osgi/framework/util/SecureAction
instanceKlass org/eclipse/osgi/storage/bundlefile/ZipBundleFile
instanceKlass org/eclipse/osgi/internal/connect/ConnectBundleFile
ciInstanceKlass org/eclipse/osgi/storage/bundlefile/CloseableBundleFile 1 1 497 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 12 1 7 1 10 12 1 9 12 10 12 1 1 9 12 9 12 9 12 9 12 9 12 9 12 1 1 1 1 1 1 1 1 1 1 10 12 1 10 12 1 10 12 1 10 7 1 12 1 1 9 100 1 12 1 1 100 1 10 100 1 12 1 1 10 100 1 12 1 1 11 100 1 12 1 1 9 12 1 10 12 1 1 10 100 1 12 1 1 10 100 1 12 1 1 10 12 1 1 100 1 10 100 1 12 1 1 10 100 1 12 1 1 7 1 1 1 1 1 1 1 1 1 10 12 1 10 12 1 10 12 1 9 7 1 12 1 8 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 1 1 10 12 10 12 1 1 11 7 1 12 1 1 11 7 1 12 1 1 7 1 10 12 1 1 8 1 10 12 1 10 12 1 1 11 12 1 10 12 1 10 12 1 1 1 1 1 8 1 10 12 1 1 10 12 1 1 10 7 1 12 1 10 7 1 12 1 9 12 1 8 1 8 1 10 12 1 10 12 1 10 12 10 12 1 8 1 9 100 1 12 1 10 12 1 10 100 1 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 100 1 8 1 10 12 1 8 1 10 100 1 12 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 1 1 100 1 10 7 1 10 10 12 1 10 12 1 10 7 1 12 1 1 1 1 1 1 1 100 1 1 10 12 1 1 10 12 1 10 12 1 1 1 1 10 12 1 5 0 9 7 1 12 1 1 11 7 1 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 8 1 8 1 100 1 10 7 1 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 1 10 7 1 12 1 1 11 12 1 1 1 8 1 10 12 1 10 12 7 1 10 12 1 1 1 100 1 1 1 1 1 1 1 1 1
ciInstanceKlass org/eclipse/osgi/storage/BundleInfo 1 1 363 7 1 7 1 1 1 1 8 1 1 8 1 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 1 10 7 1 12 1 1 9 12 1 1 100 1 1 1 10 12 9 12 9 12 9 12 9 12 9 12 1 1 1 1 1 1 1 1 1 100 1 9 12 7 1 10 10 7 1 12 1 1 5 0 9 7 1 12 1 1 10 12 1 1 10 100 1 12 1 1 10 12 1 8 1 10 12 1 100 1 10 12 1 1 10 12 1 7 1 10 12 1 100 1 1 1 1 1 1 100 1 1 1 100 1 8 1 10 10 12 1 10 12 1 1 1 1 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 10 12 10 12 10 12 1 1 10 100 1 12 1 1 10 12 1 10 12 1 1 100 1 8 1 100 1 8 1 10 100 1 12 1 1 100 1 1 100 1 10 7 1 12 10 8 1 10 12 1 1 10 12 1 10 12 8 1 1 1 1 8 1 9 12 1 1 10 100 1 12 1 10 12 1 1 10 12 1 10 12 1 1 10 100 1 12 1 1 9 100 1 12 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 10 7 1 12 1 1 7 1 7 1 10 7 1 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 8 1 10 12 1 8 1 8 1 8 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1
staticfield org/eclipse/osgi/storage/BundleInfo MULTI_RELEASE_FILTER_PREFIXES Ljava/util/Collection; java/util/Collections$SingletonSet
ciInstanceKlass org/eclipse/osgi/storage/BundleInfo$Generation 1 1 632 7 1 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 9 12 10 12 1 9 12 9 12 7 1 10 7 1 12 1 1 10 12 1 9 12 1 1 1 1 1 1 9 12 9 12 9 12 9 12 9 12 9 12 1 1 1 1 9 12 10 7 1 12 1 1 9 7 1 12 1 100 1 10 10 12 1 1 10 7 1 12 1 1 1 100 1 1 10 7 1 12 100 1 1 1 1 1 1 9 12 10 12 100 1 100 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 7 1 12 1 1 7 1 10 10 7 1 12 1 1 8 1 11 7 1 12 1 1 7 1 10 7 1 12 1 1 10 12 1 1 10 7 1 12 1 1 7 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 1 100 1 8 1 8 1 11 12 1 1 10 12 1 1 100 1 8 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 10 12 1 1 10 7 1 12 1 1 1 1 10 7 1 12 1 1 10 10 12 1 1 10 12 1 1 9 12 10 12 10 12 1 1 7 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 10 12 1 1 10 7 1 12 1 1 10 12 1 10 7 1 12 1 1 9 12 1 1 10 7 1 12 1 10 12 1 1 1 1 7 1 10 12 1 10 12 1 1 1 1 9 12 10 12 1 10 12 1 1 1 1 11 7 1 12 1 1 11 7 1 12 1 1 7 1 10 12 1 1 10 11 12 1 1 1 1 1 1 1 100 1 1 1 9 12 1 1 100 1 1 1 10 12 1 10 7 1 12 1 1 9 12 100 1 8 1 10 10 12 1 1 10 12 1 1 10 100 1 12 1 1 1 1 1 10 12 1 1 10 10 12 1 10 12 1 10 12 10 12 1 1 1 1 1 1 1 1 10 12 1 1 9 7 1 12 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 12 8 1 9 100 1 12 1 10 12 1 10 100 1 12 1 1 10 8 1 8 1 10 7 1 12 1 1 10 7 1 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 10 12 1 1 10 12 1 10 12 1 1 1 100 1 8 1 10 12 1 1 7 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 9 12 100 1 10 12 10 12 1 1 1 1 1 10 12 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 100 1 1 1 100 1 1 1 1 100 1 1
ciInstanceKlass org/eclipse/osgi/storage/bundlefile/CloseableBundleFile$BundleEntryInputStream 1 1 114 7 1 7 1 1 1 1 1 1 1 1 9 12 10 12 1 9 12 10 7 1 12 1 1 1 1 1 1 1 1 1 1 1 100 1 10 12 10 12 1 1 1 1 1 1 10 12 10 12 1 100 1 1 10 12 1 10 12 1 1 1 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 1 12 1 1 10 100 1 12 1 1 10 100 1 12 1 10 12 1 1 1 1 1 100 1 1 1 1 1 1
instanceKlass org/lombokweb/asm/MethodTooLargeException
instanceKlass org/lombokweb/asm/ClassTooLargeException
instanceKlass java/lang/ArrayIndexOutOfBoundsException
ciInstanceKlass java/lang/IndexOutOfBoundsException 0 0 49 10 100 12 1 1 1 10 12 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1
ciMethodData java/lang/Object <init> ()V 2 366787 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethod java/io/IOException <init> (Ljava/lang/String;Ljava/lang/Throwable;)V 0 0 1 0 -1
ciMethodData java/util/concurrent/locks/ReentrantLock lock ()V 2 29254 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x40005 0x7246 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/concurrent/locks/ReentrantLock$Sync lock ()V 2 29254 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 27 0x10005 0x0 0x0 0x2d17ee89658 0x7246 0x0 0x0 0x40007 0x7227 0x58 0x1f 0x90005 0x1f 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 3 java/util/concurrent/locks/ReentrantLock$NonfairSync methods 0
ciMethodData java/util/concurrent/locks/AbstractQueuedSynchronizer acquire (I)V 1 720 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 28 0x20005 0x14e 0x0 0x2d17ee89658 0x22 0x2d17e58dd30 0x160 0x50007 0x2b5 0x58 0x1b 0xf0005 0x1b 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xffffffffffffffff 0x0 oops 2 3 java/util/concurrent/locks/ReentrantLock$NonfairSync 5 java/util/concurrent/locks/ReentrantReadWriteLock$NonfairSync methods 0
ciMethodData java/util/concurrent/locks/ReentrantLock unlock ()V 2 29885 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x50005 0x74bd 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/concurrent/locks/AbstractQueuedSynchronizer release (I)Z 2 30042 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x20005 0xe 0x0 0x2d17ee89658 0x74c1 0x2d17e58dd30 0x8b 0x50007 0x2e9 0x30 0x7271 0xc0002 0x7271 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 3 java/util/concurrent/locks/ReentrantLock$NonfairSync 5 java/util/concurrent/locks/ReentrantReadWriteLock$NonfairSync methods 0
ciMethodData java/util/concurrent/locks/ReentrantLock$Sync tryRelease (I)Z 2 8977 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 57 0x10005 0x2311 0x0 0x0 0x0 0x0 0x0 0x80005 0x2311 0x0 0x0 0x0 0x0 0x0 0xb0002 0x2311 0xe0007 0x2311 0x30 0x0 0x150002 0x0 0x1a0007 0x1f 0x38 0x22f2 0x1e0003 0x22f2 0x18 0x240007 0x1f 0x58 0x22f2 0x290005 0x22f2 0x0 0x0 0x0 0x0 0x0 0x2e0005 0x2311 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/concurrent/locks/AbstractQueuedSynchronizer signalNext (Ljava/util/concurrent/locks/AbstractQueuedSynchronizer$Node;)V 2 8894 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 30 0x10007 0x221c 0xa8 0xa2 0xa0007 0x82 0x88 0x20 0x110007 0x1a 0x68 0x6 0x160005 0x6 0x0 0x0 0x0 0x0 0x0 0x1e0002 0x6 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/concurrent/locks/AbstractQueuedSynchronizer compareAndSetState (II)Z 2 1886 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x90005 0x14 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/util/concurrent/locks/ReentrantLock$NonfairSync initialTryLock ()Z 2 9766 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 60 0x2 0x2626 0x70005 0x2626 0x0 0x0 0x0 0x0 0x0 0xa0007 0x1 0x58 0x2625 0xf0005 0x2625 0x0 0x0 0x0 0x0 0x0 0x150005 0x1 0x0 0x0 0x0 0x0 0x0 0x190007 0x0 0xc0 0x1 0x1d0005 0x1 0x0 0x0 0x0 0x0 0x0 0x240007 0x1 0x30 0x0 0x2d0002 0x0 0x330005 0x1 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/Error <init> (Ljava/lang/String;)V 1 16 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 8 0x20002 0x10 0x0 0x0 0x9 0x2 0x1c 0x0 oops 0 methods 0
ciMethodData java/util/concurrent/locks/AbstractQueuedSynchronizer$Node getAndUnsetStatus (I)I 1 12 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 20 0xa000b 0xc 0x0 0x0 0x0 0x0 0x0 0x2 0x1 0x2 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xffffffffffffffff 0x0 oops 0 methods 0
ciMethodData java/util/concurrent/locks/LockSupport unpark (Ljava/lang/Thread;)V 1 346 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 36 0x10007 0x22 0xd8 0x138 0x50005 0x138 0x0 0x0 0x0 0x0 0x0 0x80007 0x138 0x48 0x0 0xc0002 0x0 0xf0003 0x0 0x50 0x160005 0x138 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0xffffffffffffffff oops 0 methods 0
ciMethodData jdk/internal/misc/InternalLock lock ()V 2 25742 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x40005 0x0 0x0 0x2d17ee873a0 0x648f 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 3 java/util/concurrent/locks/ReentrantLock methods 0
ciMethodData jdk/internal/misc/InternalLock unlock ()V 2 25735 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x40005 0x0 0x0 0x2d17ee873a0 0x6488 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 3 java/util/concurrent/locks/ReentrantLock methods 0
ciMethodData java/io/BufferedInputStream getBufIfOpen ()[B 2 13987 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x20005 0x36a4 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/io/BufferedInputStream getBufIfOpen (Z)[B 2 14033 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 35 0x60007 0x2f 0x98 0x36a3 0x80000006000d0007 0x3644 0x78 0x60 0x220005 0x35 0x0 0x0 0x0 0x0 0x0 0x250007 0x60 0x20 0x0 0x2e0007 0x36d3 0x30 0x0 0x370002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0xffffffffffffffff 0x0 oops 0 methods 0
ciMethodData java/io/BufferedInputStream fill ()V 1 222 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 93 0x10005 0xde 0x0 0x0 0x0 0x0 0x0 0xa0007 0x38 0x38 0xa6 0x120003 0xa6 0x160 0x1b0007 0x2d 0x148 0xb 0x220007 0x0 0x48 0xb 0x370002 0xb 0x440003 0xb 0xf8 0x4d0007 0x0 0x38 0x0 0x5a0003 0x0 0xc0 0x660002 0x0 0x6f0007 0x0 0x20 0x0 0x830002 0x0 0x8f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x920007 0x0 0x30 0x0 0x9b0002 0x0 0xaa0005 0xde 0x0 0x0 0x0 0x0 0x0 0xb90005 0x0 0x0 0x2d17e66cdb8 0xc4 0x2d17e66cd28 0x1a 0xbe0007 0x41 0x20 0x9d 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 2 65 org/eclipse/osgi/storage/bundlefile/CloseableBundleFile$BundleEntryInputStream 67 java/io/FileInputStream methods 0
ciMethodData java/lang/Exception <init> (Ljava/lang/String;Ljava/lang/Throwable;)V 1 267 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x30002 0x10b 0x0 0x0 0x9 0x3 0x1c 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/Throwable <init> (Ljava/lang/String;Ljava/lang/Throwable;)V 1 267 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 20 0x10002 0x10b 0x180005 0x0 0x0 0x2d17cb830b8 0x10b 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x1c 0x0 0x0 oops 1 5 java/lang/ClassNotFoundException methods 0
ciMethod org/eclipse/osgi/storage/bundlefile/BundleFile getBaseFile ()Ljava/io/File; 264 0 132 0 -1
ciMethod org/eclipse/osgi/storage/BundleInfo getLocation ()Ljava/lang/String; 192 0 96 0 -1
ciMethod org/eclipse/osgi/storage/BundleInfo$Generation getBundleInfo ()Lorg/eclipse/osgi/storage/BundleInfo; 276 0 138 0 -1
ciMethod org/eclipse/osgi/storage/bundlefile/CloseableBundleFile$BundleEntryInputStream available ()I 512 0 297 0 0
ciMethod org/eclipse/osgi/storage/bundlefile/CloseableBundleFile$BundleEntryInputStream enrichExceptionWithBaseFile (Ljava/io/IOException;)Ljava/io/IOException; 0 0 1 0 -1
ciMethodData org/eclipse/osgi/storage/bundlefile/CloseableBundleFile$BundleEntryInputStream enrichExceptionWithBaseFile (Ljava/io/IOException;)Ljava/io/IOException; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 54 0x40005 0x0 0x0 0x0 0x0 0x0 0x0 0x90007 0x0 0xe0 0x0 0x130007 0x0 0x38 0x0 0x170003 0x0 0xd8 0x210005 0x0 0x0 0x0 0x0 0x0 0x0 0x240005 0x0 0x0 0x0 0x0 0x0 0x0 0x270003 0x0 0x50 0x2b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x350002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/io/BufferedInputStream ensureOpen ()V 2 8255 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 15 0x40007 0x203f 0x30 0x0 0xd0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/io/BufferedInputStream read ([BII)I 2 6272 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 51 0x40007 0x0 0x100 0x1881 0xb0005 0x0 0x0 0x2d17ec8c550 0x1881 0x0 0x0 0x120005 0x1881 0x0 0x0 0x0 0x0 0x0 0x1b0005 0x0 0x0 0x2d17ec8c550 0x1881 0x0 0x0 0x270005 0x0 0x0 0x0 0x0 0x0 0x0 0x360005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 2 7 jdk/internal/misc/InternalLock 21 jdk/internal/misc/InternalLock methods 0
ciMethodData java/io/BufferedInputStream implRead ([BII)I 2 6269 orig 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 69 0x10005 0x187e 0x0 0x0 0x0 0x0 0x0 0x120007 0x187e 0x30 0x0 0x190002 0x0 0x1e0007 0x187e 0x20 0x0 0x300005 0x1899 0x0 0x0 0x0 0x0 0x0 0x8000000600370007 0x1886 0x58 0x14 0x3c0007 0x0 0x38 0x14 0x410003 0x14 0x18 0x510007 0x2f 0x20 0x1857 0x5f0007 0x0 0x78 0x2f 0x400640005 0x0 0x0 0x2d17e66cd28 0x18 0x2d17e66cdb8 0x18 0x670007 0x1b 0x20 0x15 0x6d0003 0x1b 0xfffffffffffffed8 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 2 46 java/io/FileInputStream 48 org/eclipse/osgi/storage/bundlefile/CloseableBundleFile$BundleEntryInputStream methods 0
ciMethodData java/io/BufferedInputStream read1 ([BII)I 2 6231 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 75 0xd0007 0x1840 0x170 0x18 0x120005 0x18 0x0 0x0 0x0 0x0 0x0 0x1a0002 0x18 0x220007 0x18 0xb0 0x0 0x2a0007 0x0 0x90 0x0 0x2e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x340005 0x0 0x0 0x0 0x0 0x0 0x0 0x390005 0x18 0x0 0x0 0x0 0x0 0x0 0x490007 0x18 0x20 0x0 0x510007 0x1840 0x38 0x18 0x560003 0x18 0x18 0x5d0005 0x1858 0x0 0x0 0x0 0x0 0x0 0x680002 0x1858 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/io/BufferedInputStream getInIfOpen ()Ljava/io/InputStream; 1 59 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 15 0x60007 0x3b 0x30 0x0 0xf0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData org/eclipse/osgi/storage/bundlefile/CloseableBundleFile$BundleEntryInputStream available ()I 1 41 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 11 0x10002 0x29 0x80002 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/io/FilterInputStream available ()I 1 45 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 209 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 16 0x40005 0x0 0x0 0x2d154423d40 0x2d 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 1 3 java/util/zip/ZipFile$ZipFileInflaterInputStream methods 0
compile java/io/BufferedInputStream read ([BII)I -1 4 inline 24 0 -1 0 java/io/BufferedInputStream read ([BII)I 1 11 0 jdk/internal/misc/InternalLock lock ()V 2 4 0 java/util/concurrent/locks/ReentrantLock lock ()V 3 4 0 java/util/concurrent/locks/ReentrantLock$Sync lock ()V 4 1 0 java/util/concurrent/locks/ReentrantLock$NonfairSync initialTryLock ()Z 5 7 0 java/util/concurrent/locks/AbstractQueuedSynchronizer compareAndSetState (II)Z 5 15 0 java/util/concurrent/locks/AbstractOwnableSynchronizer setExclusiveOwnerThread (Ljava/lang/Thread;)V 5 21 0 java/util/concurrent/locks/AbstractOwnableSynchronizer getExclusiveOwnerThread ()Ljava/lang/Thread; 5 29 0 java/util/concurrent/locks/AbstractQueuedSynchronizer getState ()I 5 51 0 java/util/concurrent/locks/AbstractQueuedSynchronizer setState (I)V 1 18 0 java/io/BufferedInputStream implRead ([BII)I 2 1 0 java/io/BufferedInputStream ensureOpen ()V 2 48 0 java/io/BufferedInputStream read1 ([BII)I 3 93 0 java/io/BufferedInputStream getBufIfOpen ()[B 4 2 0 java/io/BufferedInputStream getBufIfOpen (Z)[B 1 27 0 jdk/internal/misc/InternalLock unlock ()V 2 4 0 java/util/concurrent/locks/ReentrantLock unlock ()V 3 5 0 java/util/concurrent/locks/AbstractQueuedSynchronizer release (I)Z 4 2 0 java/util/concurrent/locks/ReentrantLock$Sync tryRelease (I)Z 5 1 0 java/util/concurrent/locks/AbstractQueuedSynchronizer getState ()I 5 8 0 java/util/concurrent/locks/AbstractOwnableSynchronizer getExclusiveOwnerThread ()Ljava/lang/Thread; 5 41 0 java/util/concurrent/locks/AbstractOwnableSynchronizer setExclusiveOwnerThread (Ljava/lang/Thread;)V 5 46 0 java/util/concurrent/locks/AbstractQueuedSynchronizer setState (I)V 4 12 0 java/util/concurrent/locks/AbstractQueuedSynchronizer signalNext (Ljava/util/concurrent/locks/AbstractQueuedSynchronizer$Node;)V
