# Todo REST API - Educational Project

A comprehensive Todo list REST API built with C# .NET Core, designed as a learning project for computer science graduates. This project demonstrates fundamental concepts in web development, software architecture, and computer science.

## 🎓 Learning Objectives

This project teaches key computer science concepts:
- **RESTful API Design** - HTTP methods, status codes, resource modeling
- **Object-Oriented Programming** - Classes, encapsulation, inheritance
- **Data Structures** - Collections, LINQ, time complexity analysis
- **Software Architecture** - Layered architecture, dependency injection, repository pattern
- **Web Development** - HTTP protocol, JSON serialization, middleware
- **Error Handling** - Validation, defensive programming, exception safety
- **Testing** - Unit tests, integration tests, comprehensive test coverage

## 🏗️ Project Structure

```
ToDo/
├── Program.cs              # Main application with detailed comments
├── myRequest.http          # HTTP test requests with explanations
├── COMPUTER-SCIENCE-CONCEPTS.md  # Detailed concept explanations
├── TEST-RESULTS.md         # Comprehensive testing documentation
└── TESTING-SUMMARY.md      # Test coverage summary
```

## 🚀 Features

### Core Functionality
- ✅ **Create** todos with title and description
- ✅ **Read** all todos or filter by status (pending/completed)
- ✅ **Update** todo properties (partial updates supported)
- ✅ **Delete** todos with proper cleanup
- ✅ **Statistics** showing completion percentage
- ✅ **Validation** preventing invalid data
- ✅ **Error Handling** with appropriate HTTP status codes

### Technical Features
- ✅ **RESTful Design** following HTTP conventions
- ✅ **JSON API** with automatic serialization
- ✅ **In-Memory Storage** using List<Todo>
- ✅ **Dependency Injection** for loose coupling
- ✅ **Comprehensive Logging** and error messages
- ✅ **Type Safety** with nullable reference types

## 📚 Computer Science Concepts Demonstrated

### 1. **Data Structures & Algorithms**
```csharp
// List<T> for dynamic storage - O(1) append, O(n) search
private readonly List<Todo> _todos = new();

// LINQ for functional programming and queries
_todos.Where(t => t.Status == status)      // O(n) filtering
_todos.FirstOrDefault(t => t.Id == id)     // O(n) search
```

### 2. **Object-Oriented Programming**
```csharp
// Encapsulation - private data, public interface
private readonly List<Todo> _todos = new();
public IEnumerable<Todo> GetAllTodos() => _todos;

// Polymorphism - interface abstraction
public IEnumerable<Todo> GetTodosByStatus(TodoStatus status)

// Inheritance - record types inherit value semantics
public record CreateTodoRequest(string Title, string? Description);
```

### 3. **Software Design Patterns**
```csharp
// Repository Pattern - data access abstraction
public class TodoService { /* CRUD operations */ }

// Dependency Injection - inversion of control
builder.Services.AddSingleton<TodoService>();

// Builder Pattern - fluent configuration
var builder = WebApplication.CreateBuilder(args);
```

### 4. **Functional Programming**
```csharp
// Lambda expressions and higher-order functions
todos.MapGet("/", (TodoService service, string? status) => { ... });

// Immutable data structures
public record CompletionStats(int TotalTasks, int CompletedTasks, double CompletionPercentage);

// LINQ method chaining
var completedTasks = _todos.Count(t => t.Status == TodoStatus.Completed);
```

## 🔧 Getting Started

### Prerequisites
- .NET 9.0 SDK
- Visual Studio Code or Visual Studio
- Basic understanding of C# and HTTP

### Running the Application
```bash
# Clone or download the project
cd ToDo

# Build the project
dotnet build

# Run the application
dotnet run

# API will be available at http://localhost:5047
```

### Testing the API
1. **Use the HTTP file**: Open `myRequest.http` in VS Code with REST Client extension
2. **Use curl**: See examples in the test scripts
3. **Use Postman**: Import the requests from the HTTP file
4. **Run automated tests**: Execute `test-api.ps1` PowerShell script

## 📖 Educational Resources

### 1. **Detailed Code Comments**
Every line of code includes educational comments explaining:
- **What** the code does
- **Why** it's implemented this way
- **How** it relates to computer science concepts
- **When** you might use different approaches

### 2. **Concept Documentation**
- `COMPUTER-SCIENCE-CONCEPTS.md` - Deep dive into CS concepts
- `TEST-RESULTS.md` - Testing methodology and results
- `TESTING-SUMMARY.md` - Comprehensive test coverage

### 3. **HTTP Request Examples**
`myRequest.http` contains 15 different test scenarios with explanations:
- CRUD operations
- Error handling
- Query parameters
- Status codes
- JSON structures

## 🧪 Testing & Validation

### Comprehensive Test Coverage
- **30+ Test Scenarios** covering all functionality
- **Unit Tests** for business logic methods
- **Integration Tests** for API endpoints
- **Error Handling Tests** for edge cases
- **Performance Analysis** with Big O notation

### Test Categories
1. **Happy Path Tests** - Normal operation scenarios
2. **Error Condition Tests** - Invalid inputs and missing resources
3. **Edge Case Tests** - Empty lists, boundary conditions
4. **Validation Tests** - Input sanitization and type safety

## 🎯 Learning Exercises

### Beginner Level
1. **Trace through the code** - Follow a request from HTTP to response
2. **Modify validation rules** - Add new validation constraints
3. **Add new properties** - Extend the Todo model
4. **Change status codes** - Experiment with different HTTP responses

### Intermediate Level
1. **Implement sorting** - Add query parameters for ordering
2. **Add pagination** - Implement skip/take for large datasets
3. **Create custom exceptions** - Replace generic error handling
4. **Add logging** - Implement structured logging throughout

### Advanced Level
1. **Database integration** - Replace in-memory storage with Entity Framework
2. **Authentication** - Add JWT token-based security
3. **Caching** - Implement response caching strategies
4. **Performance optimization** - Use Dictionary<int, Todo> for O(1) lookups

## 🔍 Code Analysis Questions

### Architecture Questions
1. Why use dependency injection instead of `new TodoService()`?
2. What are the benefits of returning `IEnumerable<Todo>` vs `List<Todo>`?
3. How does the Repository pattern improve testability?

### Performance Questions
1. What's the time complexity of finding a todo by ID?
2. How could we improve search performance?
3. What are the memory implications of in-memory storage?

### Design Questions
1. Why use record types for request/response models?
2. How does the enum JsonConverter improve API usability?
3. What's the difference between PUT and PATCH for updates?

## 🚀 Next Steps

### Production Readiness
- **Database Integration** - Entity Framework Core with SQL Server
- **Authentication & Authorization** - JWT tokens, role-based access
- **Validation** - FluentValidation for complex rules
- **Logging** - Serilog for structured logging
- **Testing** - xUnit with test containers
- **Documentation** - Swagger/OpenAPI integration

### Scalability Improvements
- **Caching** - Redis for distributed caching
- **Message Queues** - RabbitMQ for async processing
- **Load Balancing** - Multiple API instances
- **Database Optimization** - Indexing, query optimization

## 📝 Summary

This Todo API project provides a solid foundation for understanding:
- Modern web API development with .NET Core
- RESTful design principles and HTTP protocol
- Object-oriented and functional programming concepts
- Software architecture patterns and best practices
- Testing methodologies and quality assurance
- Performance analysis and optimization techniques

The extensive comments and documentation make it an ideal learning resource for computer science graduates entering the software development field.

## 📚 Additional Resources

- [ASP.NET Core Documentation](https://docs.microsoft.com/en-us/aspnet/core/)
- [REST API Design Guidelines](https://restfulapi.net/)
- [C# Programming Guide](https://docs.microsoft.com/en-us/dotnet/csharp/)
- [LINQ Documentation](https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/concepts/linq/)
- [HTTP Status Codes](https://httpstatuses.com/)

Happy learning! 🎓
