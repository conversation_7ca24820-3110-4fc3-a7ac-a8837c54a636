# Todo REST API - Educational Project

A comprehensive Todo list REST API built with C# .NET Core, designed as a learning project for computer science graduates. This project demonstrates fundamental concepts in web development, software architecture, and computer science.

## 🎓 Learning Objectives

This project teaches key computer science concepts:
- **RESTful API Design** - HTTP methods, status codes, resource modeling
- **Object-Oriented Programming** - Classes, encapsulation, inheritance
- **Data Structures** - Collections, LINQ, time complexity analysis
- **Software Architecture** - Layered architecture, dependency injection, repository pattern
- **Web Development** - HTTP protocol, JSON serialization, middleware
- **Error Handling** - Validation, defensive programming, exception safety
- **Testing** - Unit tests, integration tests, comprehensive test coverage

## 🏗️ Project Structure

```
ToDo/
├── Program.cs              # Main application with detailed comments
├── myRequest.http          # HTTP test requests with explanations
├── COMPUTER-SCIENCE-CONCEPTS.md  # Detailed concept explanations
├── TEST-RESULTS.md         # Comprehensive testing documentation
└── TESTING-SUMMARY.md      # Test coverage summary
```

## 🚀 Features

### Core Functionality
- ✅ **Create** todos with title and description
- ✅ **Read** all todos or filter by status (pending/completed)
- ✅ **Update** todo properties (partial updates supported)
- ✅ **Delete** todos with proper cleanup
- ✅ **Statistics** showing completion percentage
- ✅ **Validation** preventing invalid data
- ✅ **Error Handling** with appropriate HTTP status codes

### Technical Features
- ✅ **RESTful Design** following HTTP conventions
- ✅ **JSON API** with automatic serialization
- ✅ **In-Memory Storage** using List<Todo>
- ✅ **Dependency Injection** for loose coupling
- ✅ **Comprehensive Logging** and error messages
- ✅ **Type Safety** with nullable reference types

## 📚 Computer Science Concepts Demonstrated

### 1. **Data Structures & Algorithms**
```csharp
// List<T> for dynamic storage - O(1) append, O(n) search
private readonly List<Todo> _todos = new();

// LINQ for functional programming and queries
_todos.Where(t => t.Status == status)      // O(n) filtering
_todos.FirstOrDefault(t => t.Id == id)     // O(n) search
```

### 2. **Object-Oriented Programming**
```csharp
// Encapsulation - private data, public interface
private readonly List<Todo> _todos = new();
public IEnumerable<Todo> GetAllTodos() => _todos;

// Polymorphism - interface abstraction
public IEnumerable<Todo> GetTodosByStatus(TodoStatus status)

// Inheritance - record types inherit value semantics
public record CreateTodoRequest(string Title, string? Description);
```

### 3. **Software Design Patterns**
```csharp
// Repository Pattern - data access abstraction
public class TodoService { /* CRUD operations */ }

// Dependency Injection - inversion of control
builder.Services.AddSingleton<TodoService>();

// Builder Pattern - fluent configuration
var builder = WebApplication.CreateBuilder(args);
```

### 4. **Functional Programming**
```csharp
// Lambda expressions and higher-order functions
todos.MapGet("/", (TodoService service, string? status) => { ... });

// Immutable data structures
public record CompletionStats(int TotalTasks, int CompletedTasks, double CompletionPercentage);

// LINQ method chaining
var completedTasks = _todos.Count(t => t.Status == TodoStatus.Completed);
```

## 🔧 Getting Started

### Prerequisites
- .NET 9.0 SDK
- Visual Studio Code or Visual Studio
- Basic understanding of C# and HTTP

### Running the Application
```bash
# Clone or download the project
cd ToDo

# Build the project
dotnet build

# Run the application
dotnet run

# API will be available at http://localhost:5047
```

### Testing the API
1. **Use the HTTP file**: Open `myRequest.http` in VS Code with REST Client extension
2. **Use curl**: See examples in the test scripts
3. **Use Postman**: Import the requests from the HTTP file
4. **Run automated tests**: Execute `test-api.ps1` PowerShell script

## 📖 Educational Resources

### 1. **Detailed Code Comments**
Every line of code includes educational comments explaining:
- **What** the code does
- **Why** it's implemented this way
- **How** it relates to computer science concepts
- **When** you might use different approaches

### 2. **Concept Documentation**
- `COMPUTER-SCIENCE-CONCEPTS.md` - Deep dive into CS concepts
- `TEST-RESULTS.md` - Testing methodology and results
- `TESTING-SUMMARY.md` - Comprehensive test coverage

### 3. **HTTP Request Examples**
`myRequest.http` contains 15 different test scenarios with explanations:
- CRUD operations
- Error handling
- Query parameters
- Status codes
- JSON structures

## 🧪 Testing & Validation

### Comprehensive Test Coverage
- **30+ Test Scenarios** covering all functionality
- **Unit Tests** for business logic methods
- **Integration Tests** for API endpoints
- **Error Handling Tests** for edge cases
- **Performance Analysis** with Big O notation

### Test Categories
1. **Happy Path Tests** - Normal operation scenarios
2. **Error Condition Tests** - Invalid inputs and missing resources
3. **Edge Case Tests** - Empty lists, boundary conditions
4. **Validation Tests** - Input sanitization and type safety

## 🎯 Learning Exercises

### Beginner Level
1. **Trace through the code** - Follow a request from HTTP to response
2. **Modify validation rules** - Add new validation constraints
3. **Add new properties** - Extend the Todo model
4. **Change status codes** - Experiment with different HTTP responses

### Intermediate Level
1. **Implement sorting** - Add query parameters for ordering
2. **Add pagination** - Implement skip/take for large datasets
3. **Create custom exceptions** - Replace generic error handling
4. **Add logging** - Implement structured logging throughout

### Advanced Level
1. **Database integration** - Replace in-memory storage with Entity Framework
2. **Authentication** - Add JWT token-based security
3. **Caching** - Implement response caching strategies
4. **Performance optimization** - Use Dictionary<int, Todo> for O(1) lookups

## 🔍 Code Analysis Questions

### Architecture Questions
1. Why use dependency injection instead of `new TodoService()`?
2. What are the benefits of returning `IEnumerable<Todo>` vs `List<Todo>`?
3. How does the Repository pattern improve testability?

### Performance Questions
1. What's the time complexity of finding a todo by ID?
2. How could we improve search performance?
3. What are the memory implications of in-memory storage?

### Design Questions
1. Why use record types for request/response models?
2. How does the enum JsonConverter improve API usability?
3. What's the difference between PUT and PATCH for updates?

## 🔄 System Design Changes for Real-World Requirements

### Scenario: What if we needed to handle...
1. **Tasks processed in priority order (1-10)**
2. **1 million tasks in the system**
3. **Users can only view their own tasks**

Let's explore how our simple design would need to change:

---

## 🎯 **Requirement 1: Priority-Based Task Processing (1-10)**

### Current Problem
Our current system treats all tasks equally. There's no way to say "this task is more important than that task."

### What We'd Need to Change

#### **1. Add Priority to the Data Model**
```csharp
public class Todo
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TodoStatus Status { get; set; } = TodoStatus.Pending;
    public int Priority { get; set; } = 5;  // NEW: 1 = highest, 10 = lowest
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; }
}
```

#### **2. Change Our Data Structure**
**Current**: We use a simple `List<Todo>` which has no ordering
**New**: We'd need a **Priority Queue** or **Heap** data structure

```csharp
// Instead of List<Todo>, we'd use:
private readonly PriorityQueue<Todo, int> _todoQueue = new();

// Or we could sort our list every time we need it:
public IEnumerable<Todo> GetTodosByPriority() =>
    _todos.OrderBy(t => t.Priority).ThenBy(t => t.CreatedAt);
```

#### **3. Performance Impact**
- **List approach**: Sorting every time = O(n log n) - slow for many tasks
- **Priority Queue approach**: Insert = O(log n), Get highest priority = O(log n) - much faster!

#### **4. New API Endpoints**
```csharp
// Get tasks in priority order
GET /todos?sortBy=priority

// Create task with priority
POST /todos
{
  "title": "Urgent bug fix",
  "priority": 1  // Highest priority
}
```

---

## 📊 **Requirement 2: Handle 1 Million Tasks**

### Current Problem
Our `List<Todo>` in memory would use too much RAM and be too slow for 1 million tasks.

### What We'd Need to Change

#### **1. Replace In-Memory Storage with Database**
**Current**: `List<Todo>` in RAM (maybe 100MB for 1M tasks)
**New**: SQL Server or PostgreSQL database

```csharp
// Instead of List<Todo>, we'd use Entity Framework:
public class TodoDbContext : DbContext
{
    public DbSet<Todo> Todos { get; set; }
}

public class TodoService
{
    private readonly TodoDbContext _context;

    public async Task<Todo> GetTodoByIdAsync(int id)
    {
        return await _context.Todos.FindAsync(id);  // O(1) with database index!
    }
}
```

#### **2. Add Database Indexes for Speed**
```sql
-- Make finding tasks by ID super fast
CREATE INDEX IX_Todos_Id ON Todos(Id);

-- Make finding tasks by priority fast
CREATE INDEX IX_Todos_Priority ON Todos(Priority, CreatedAt);

-- Make finding tasks by status fast
CREATE INDEX IX_Todos_Status ON Todos(Status);
```

#### **3. Add Pagination**
**Problem**: Can't return 1 million tasks at once - would crash the browser!
**Solution**: Return tasks in small chunks

```csharp
// New API endpoint with pagination
GET /todos?page=1&pageSize=20&sortBy=priority

public async Task<PagedResult<Todo>> GetTodosAsync(int page, int pageSize)
{
    var todos = await _context.Todos
        .OrderBy(t => t.Priority)
        .Skip((page - 1) * pageSize)  // Skip previous pages
        .Take(pageSize)               // Take only what we need
        .ToListAsync();

    return new PagedResult<Todo>
    {
        Items = todos,
        TotalCount = await _context.Todos.CountAsync(),
        Page = page,
        PageSize = pageSize
    };
}
```

#### **4. Add Caching for Speed**
```csharp
// Cache frequently accessed data
public async Task<Todo> GetTodoByIdAsync(int id)
{
    // Check cache first
    var cached = await _cache.GetAsync($"todo:{id}");
    if (cached != null) return cached;

    // If not in cache, get from database
    var todo = await _context.Todos.FindAsync(id);

    // Store in cache for next time
    await _cache.SetAsync($"todo:{id}", todo, TimeSpan.FromMinutes(10));

    return todo;
}
```

#### **5. Performance Comparison**
| Operation | Current (List) | New (Database + Index) |
|-----------|----------------|------------------------|
| Find by ID | O(n) - slow | O(1) - instant |
| Get by priority | O(n log n) - very slow | O(log n) - fast |
| Add new task | O(1) - fast | O(log n) - still fast |
| Memory usage | All in RAM | Only active data in RAM |

---

## 👤 **Requirement 3: Users Can Only View Their Own Tasks**

### Current Problem
All tasks are shared - anyone can see anyone's tasks. No concept of "ownership."

### What We'd Need to Change

#### **1. Add User Information to Data Model**
```csharp
public class Todo
{
    public int Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public TodoStatus Status { get; set; } = TodoStatus.Pending;
    public int Priority { get; set; } = 5;
    public string UserId { get; set; } = string.Empty;  // NEW: Who owns this task?
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? UpdatedAt { get; set; }
}

// We'd also need a User model
public class User
{
    public string Id { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
```

#### **2. Add Authentication System**
**Current**: No login required
**New**: Users must log in to access their tasks

```csharp
// Add JWT authentication
builder.Services.AddAuthentication("Bearer")
    .AddJwtBearer("Bearer", options => { /* JWT config */ });

// Protect our endpoints
[Authorize]  // This means "user must be logged in"
public async Task<IEnumerable<Todo>> GetMyTodos()
{
    var userId = User.FindFirst("sub")?.Value;  // Get user ID from login token
    return await _context.Todos
        .Where(t => t.UserId == userId)  // Only return THIS user's tasks
        .ToListAsync();
}
```

#### **3. Update All Operations to Be User-Specific**
```csharp
// Create task - automatically assign to current user
public async Task<Todo> CreateTodoAsync(CreateTodoRequest request)
{
    var userId = _currentUser.GetUserId();  // Get from login session

    var todo = new Todo
    {
        Title = request.Title,
        Description = request.Description,
        UserId = userId,  // Automatically set owner
        Priority = request.Priority ?? 5
    };

    _context.Todos.Add(todo);
    await _context.SaveChangesAsync();
    return todo;
}

// Get task - verify user owns it
public async Task<Todo?> GetTodoByIdAsync(int id)
{
    var userId = _currentUser.GetUserId();

    return await _context.Todos
        .Where(t => t.Id == id && t.UserId == userId)  // Security check!
        .FirstOrDefaultAsync();
}
```

#### **4. Add Database Index for User Queries**
```sql
-- Make finding a user's tasks super fast
CREATE INDEX IX_Todos_UserId_Priority ON Todos(UserId, Priority, CreatedAt);
```

#### **5. New API Flow**
```csharp
// 1. User logs in
POST /auth/login
{
  "email": "<EMAIL>",
  "password": "password123"
}
// Returns: JWT token

// 2. User gets their tasks (must include token in header)
GET /todos
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

// 3. System automatically filters to only their tasks
```

---

## 🏗️ **Complete System Architecture Changes**

### Before (Simple Version)
```
[Browser] → [API] → [List<Todo> in RAM]
```

### After (Production Version)
```
[Browser] → [Load Balancer] → [API Server 1]
                           → [API Server 2] → [Database with Indexes]
                           → [API Server 3]     ↓
                                            [Cache (Redis)]
                                                ↓
                                            [Background Jobs]
                                                ↓
                                            [Priority Queue Processor]
```

### New Components Needed
1. **Database Server** - Store millions of tasks efficiently
2. **Authentication Service** - Handle user login/logout
3. **Cache Server** - Speed up common queries
4. **Load Balancer** - Handle many users at once
5. **Background Processor** - Handle priority queue processing
6. **Monitoring** - Watch for problems

### Performance Impact
| Metric | Before | After |
|--------|--------|-------|
| Max tasks | ~10,000 | 1,000,000+ |
| Users supported | 1 (shared) | Unlimited |
| Response time | 50ms | 10ms (with caching) |
| Memory usage | 100MB | 10MB (per server) |
| Concurrent users | 10 | 10,000+ |

This shows how a simple learning project can evolve into a production system by changing data structures, adding databases, implementing security, and optimizing for scale!

## 📝 Summary

This Todo API project provides a solid foundation for understanding:
- Modern web API development with .NET Core
- RESTful design principles and HTTP protocol
- Object-oriented and functional programming concepts
- Software architecture patterns and best practices
- Testing methodologies and quality assurance
- Performance analysis and optimization techniques

The extensive comments and documentation make it an ideal learning resource for computer science graduates entering the software development field.

## 📚 Additional Resources

- [ASP.NET Core Documentation](https://docs.microsoft.com/en-us/aspnet/core/)
- [REST API Design Guidelines](https://restfulapi.net/)
- [C# Programming Guide](https://docs.microsoft.com/en-us/dotnet/csharp/)
- [LINQ Documentation](https://docs.microsoft.com/en-us/dotnet/csharp/programming-guide/concepts/linq/)
- [HTTP Status Codes](https://httpstatuses.com/)

Happy learning! 🎓
